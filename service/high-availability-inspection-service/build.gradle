ext {
    versions = [
            springBoot : '2.6.15', // 框架当前使用的版本
            mapstruct  : '1.6.3',
            mybatisPlus: '3.5.12',
            shedlock   : '4.48.0',
            pagehelper : '2.0.0',
            okhttp     : '4.12.0',
            kubernetes : '22.0.1-legacy' // 适配 K8s 1.31
    ]
}

dependencies {
    api project(':interface:high-availability-tenant-interface')
    api project(':interface:high-availability-emergency-interface')
    api project(':interface:high-availability-extenal-interface:high-availability-container-interface')
    api project(':common:high-availability-common')

    api 'com.cmpay:lemon-framework-starter-security'
    implementation 'com.cmpay:lemon-framework-starter-datasource'
    api 'com.cmpay:lemon-framework-starter-session-hazelcast'
    api 'com.cmpay:lemon-framework-starter-context'
    api('com.cmpay:lemon-framework-redis')
    api('com.cmpay:cmpay-support')
    api("com.cmpay:lemon-framework-starter-cloud-openfeign")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")

    implementation 'org.springframework.boot:spring-boot-starter-quartz'
    implementation 'org.springframework.plugin:spring-plugin-core:2.0.0.RELEASE'
    implementation 'org.apache.sshd:sshd-core:2.15.0'
    api("com.baomidou:mybatis-plus-boot-starter:${versions.mybatisPlus}") {
        exclude group: 'org.springframework.boot'
    }
    implementation "com.baomidou:mybatis-plus-jsqlparser-4.9:${versions.mybatisPlus}"
    implementation "org.mapstruct:mapstruct:${versions.mapstruct}"
    implementation 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
    implementation 'org.apache.commons:commons-collections4:4.5.0'
    implementation "com.github.pagehelper:pagehelper-spring-boot-starter:${versions.pagehelper}"
    implementation "net.javacrumbs.shedlock:shedlock-spring:${versions.shedlock}"
    implementation "net.javacrumbs.shedlock:shedlock-provider-hazelcast4:${versions.shedlock}"
    implementation "com.squareup.okhttp3:okhttp:${versions.okhttp}"
    implementation 'com.hazelcast:hazelcast-spring:5.3.8'
    api 'org.springdoc:springdoc-openapi-ui:1.6.15' // Compatible with Spring Boot 2.6.x and Java 8

    implementation("cn.hutool:hutool-all:${hutoolVersion}")

    implementation('com.cmpay:lemon-framework-starter-cache-jcache')

    // JSONPath support for complex field value extraction
    implementation 'com.jayway.jsonpath:json-path:2.9.0'

    runtimeOnly 'com.mysql:mysql-connector-j'

    annotationProcessor "org.mapstruct:mapstruct-processor:${versions.mapstruct}"

    runtimeOnly("com.cmpay:lemon-framework-mybatis")


    // Kubernetes Java Client
    implementation "io.kubernetes:client-java:${versions.kubernetes}"
    implementation "io.kubernetes:client-java-extended:${versions.kubernetes}"
//    implementation "io.kubernetes:client-java-spring-integration:${versions.kubernetes}"

    // Test dependencies
    testImplementation "com.squareup.okhttp3:mockwebserver:${versions.okhttp}"
    testImplementation 'org.mockito:mockito-inline:4.11.0'
}

configurations.all {
    resolutionStrategy.eachDependency { details ->
        if (details.requested.group == 'org.springframework.boot') {
            details.useVersion versions.springBoot
        }
    }
}

test {
    useJUnitPlatform()

    // 传ssh和k8s前缀的属性供测试适用
    System.properties.findAll { it.key.toString().startsWith('ssh.') || it.key.toString().startsWith('k8s.') }.each { systemProperty it.key.toString(), it.value.toString() }
}