package com.cmpay.hacp.inspection.infrastructure.tag.converter;

import com.cmpay.hacp.inspection.domain.tag.model.Tag;
import com.cmpay.hacp.inspection.infrastructure.tag.repository.dataobject.TagDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TagConverter {
    List<Tag> toTagList(List<TagDO> tagDOList);

    @Mapping(target = "tagId", source = "id")
    Tag toTarget(TagDO source);
}
