package com.cmpay.hacp.inspection.infrastructure.condition.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionDO;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.mapper.RuleConditionMapper;
import org.springframework.stereotype.Repository;

@Repository
public class RuleConditionRepository extends CrudRepository<RuleConditionMapper, RuleConditionDO> {
}
