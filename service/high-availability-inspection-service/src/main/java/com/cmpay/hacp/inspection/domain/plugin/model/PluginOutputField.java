package com.cmpay.hacp.inspection.domain.plugin.model;

import com.cmpay.hacp.inspection.domain.model.enums.PluginOutputFieldType;
import lombok.Data;

/**
 * 插件脚本输出字段对象
 */
@Data
public class PluginOutputField {
    /**
     * 字段名称(如cpu.usage)
     */
    private String fieldName;

    /**
     * 取值路径 JSONPath
     */
    private String fieldPath;

    /**
     * 示例值
     */
    private String exampleValue;

    /**
     * 单位(如%)
     */
    private String fieldUnit;

    /**
     * 字段类型(0:数值型, 1:字符串型, 2:布尔型)
     */
    private PluginOutputFieldType fieldType;

    /**
     * 描述
     */
    private String description;
}
