package com.cmpay.hacp.inspection.infrastructure.config;

import com.cmpay.lemonframework.autoconfigure.hazelcast.ConfigCustomizer;
import com.hazelcast.config.Config;
import com.hazelcast.config.MapConfig;
import org.springframework.stereotype.Component;

/**
 * 萤火虫缓存配置
 * 配置萤火虫 token的分布式缓存TTL和其他参数
 */
@Component
public class FireflyCacheConfig implements ConfigCustomizer {

    /**
     * 萤火虫 token缓存名称
     */
    public static final String FIREFLY_TOKENS = "fireflyTokens";

    @Override
    public void customize(Config config) {
        config.addMapConfig(fireflyTokensMapConfig());
    }

    /**
     * 配置萤火虫 tokens缓存
     * TTL设置为60分钟，与token的默认过期时间一致
     * 
     * @return MapConfig 缓存配置
     */
    private MapConfig fireflyTokensMapConfig() {
        MapConfig mapConfig = new MapConfig();
        mapConfig.setName(FIREFLY_TOKENS);
        // 设置TTL为60分钟（3600秒）
        mapConfig.setTimeToLiveSeconds(60 * 60);
        // 设置最大空闲时间为30分钟（1800秒）
        mapConfig.setMaxIdleSeconds(30 * 60);
        // 设置最大条目数为1000（支持多用户场景）
        mapConfig.getEvictionConfig().setSize(1000);
        return mapConfig;
    }
}
