package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.assembler.DailyInspectionReportMapper;
import com.cmpay.hacp.inspection.application.service.DailyInspectionReportService;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReportDetail;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDetailDO;
import com.cmpay.hacp.inspection.infrastructure.repository.DailyInspectionReportDetailRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.DailyInspectionReportRepository;
import com.cmpay.lemon.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 按日巡检报告查询服务实现
 * 基于按日汇总表提供查询功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DailyInspectionReportServiceImpl implements DailyInspectionReportService {

    private final DailyInspectionReportRepository dailyInspectionReportRepository;
    private final DailyInspectionReportDetailRepository dailyInspectionReportDetailRepository;
    private final DailyInspectionReportMapper dailyInspectionReportMapper;

    @Override
    public IPage<DailyInspectionReport> getReportPage(PageDTO<?> page, DailyInspectionReport queryCondition) {
        log.info("Querying daily inspection reports with page: {}, condition: {}", page, queryCondition);

        // 转换领域对象为DO对象用于查询
        DailyInspectionReportDO queryConditionDO = dailyInspectionReportMapper.toDailyInspectionReportDO(queryCondition);

        // 构建查询条件
        var queryWrapper = Wrappers.lambdaQuery(DailyInspectionReportDO.class);

        if (queryConditionDO != null) {
            // 报告ID模糊查询
            if (StringUtils.isNotBlank(queryConditionDO.getReportId())) {
                queryWrapper.like(DailyInspectionReportDO::getReportId, queryConditionDO.getReportId());
            }

            // 报告日期精确查询
            if (queryConditionDO.getReportDate() != null) {
                queryWrapper.eq(DailyInspectionReportDO::getReportDate, queryConditionDO.getReportDate());
            }

            // 通过率范围查询
            if (queryConditionDO.getPassRate() != null) {
                queryWrapper.ge(DailyInspectionReportDO::getPassRate, queryConditionDO.getPassRate());
            }

            // 报告状态查询
            if (StringUtils.isNotBlank(queryConditionDO.getReportStatus())) {
                queryWrapper.eq(DailyInspectionReportDO::getReportStatus, queryConditionDO.getReportStatus());
            }
        }

        // 按报告日期倒序排列
        queryWrapper.orderByDesc(DailyInspectionReportDO::getReportDate);

        // 执行分页查询
        Page<DailyInspectionReportDO> pageParam = new Page<>(page.getCurrent(), page.getSize());
        IPage<DailyInspectionReportDO> doResult = dailyInspectionReportRepository.page(pageParam, queryWrapper);

        // 转换DO结果为领域对象
        List<DailyInspectionReport> domainRecords = dailyInspectionReportMapper.toDailyInspectionReportList(doResult.getRecords());

        // 构建领域对象分页结果
        Page<DailyInspectionReport> result = new Page<>(doResult.getCurrent(), doResult.getSize(), doResult.getTotal());
        result.setRecords(domainRecords);

        log.info("Found {} daily inspection reports", result.getTotal());
        return result;
    }

    @Override
    public DailyInspectionReportDetail getReportDetailContent(String reportId) {
        log.info("Getting daily inspection report detail content for reportId: {}", reportId);

        if (StringUtils.isBlank(reportId)) {
            log.warn("Report ID is blank");
            return null;
        }

        DailyInspectionReportDetailDO reportDetailDO = dailyInspectionReportDetailRepository.getOne(
                Wrappers.lambdaQuery(DailyInspectionReportDetailDO.class)
                        .eq(DailyInspectionReportDetailDO::getReportId, reportId)
        );

        if (reportDetailDO == null) {
            log.warn("Daily inspection report detail content not found for reportId: {}", reportId);
            return null;
        }

        // 转换DO为领域对象
        return dailyInspectionReportMapper.toDailyInspectionReportDetail(reportDetailDO);
    }
}
