package com.cmpay.hacp.inspection.domain.model.task;

import lombok.Getter;

/**
 * 资源执行统计信息
 */
@Getter
public class ResourceExecutionStats {
    private String resourceId;
    private String resourceName;
    private int successCount;
    private int failCount;
    private int totalCount;

    public ResourceExecutionStats(String resourceId, String resourceName) {
        this.resourceId = resourceId;
        this.resourceName = resourceName;
        this.successCount = 0;
        this.failCount = 0;
        this.totalCount = 0;
    }

    public void incrementSuccess() {
        this.successCount++;
        this.totalCount++;
    }

    public void incrementFail() {
        this.failCount++;
        this.totalCount++;
    }

    public boolean isHealthy() {
        return totalCount > 0 && successCount > 0 && failCount == 0;
    }

    public double getSuccessRate() {
        return totalCount > 0 ? (double) successCount / totalCount * 100 : 0.0;
    }
}
