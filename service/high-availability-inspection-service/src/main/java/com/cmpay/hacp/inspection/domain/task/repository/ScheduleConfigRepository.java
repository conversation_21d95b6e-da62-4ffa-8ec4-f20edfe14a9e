package com.cmpay.hacp.inspection.domain.task.repository;

import com.cmpay.hacp.inspection.domain.model.task.ScheduleConfig;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface ScheduleConfigRepository {
    void updateByTaskId(@NotNull String taskId, ScheduleConfig scheduleConfig);

    void saveByTaskId(@NotNull String taskId, ScheduleConfig scheduleConfig);

    void removeByTaskId(@NotNull String taskId);

    ScheduleConfig queryByTaskId(@NotNull String taskId);

    List<ScheduleConfig> listByTaskIds(List<String> taskIds);
}
