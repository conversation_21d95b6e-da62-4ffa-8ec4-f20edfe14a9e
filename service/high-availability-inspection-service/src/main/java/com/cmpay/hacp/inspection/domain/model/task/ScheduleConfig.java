package com.cmpay.hacp.inspection.domain.model.task;

import com.cmpay.hacp.inspection.domain.model.enums.IntervalUnit;
import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 调度配置
 */
@Data
public abstract class ScheduleConfig {
    private String taskId;
    /**
     * 是否启用定时执行
     */
    private boolean enabled;

    /**
     * 创建CRON表达式类型的调度配置
     */
    public static CronScheduleConfig createCronSchedule(String cronExpression) {
        CronScheduleConfig config = new CronScheduleConfig();
        config.setCronExpression(cronExpression);
        config.setEnabled(true);
        return config;
    }

    /**
     * 创建固定间隔类型的调度配置
     */
    public static IntervalScheduleConfig createIntervalSchedule(Integer value, IntervalUnit unit) {
        IntervalScheduleConfig config = new IntervalScheduleConfig();
        config.setIntervalValue(value);
        config.setIntervalUnit(unit);
        config.setEnabled(true);
        return config;
    }

    /**
     * 创建固定时间类型的调度配置
     */
    public static FixedTimeScheduleConfig createFixedTimeSchedule(LocalDateTime dateTime) {
        FixedTimeScheduleConfig config = new FixedTimeScheduleConfig();
        config.setExecutionDateTime(dateTime);
        config.setEnabled(true);
        return config;
    }

    /**
     * 获取调度类型
     */
    public abstract ScheduleType getType();

    /**
     * 验证调度配置是否有效
     */
    abstract boolean isValid();
}
