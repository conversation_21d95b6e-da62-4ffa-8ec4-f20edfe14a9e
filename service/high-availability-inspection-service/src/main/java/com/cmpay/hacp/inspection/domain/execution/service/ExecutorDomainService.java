package com.cmpay.hacp.inspection.domain.execution.service;

import com.cmpay.hacp.inspection.domain.execution.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.execution.model.InspectionExecutor;
import org.springframework.plugin.core.PluginRegistry;
import org.springframework.stereotype.Service;

@Service
public class ExecutorDomainService {
    private final PluginRegistry<InspectionExecutor, ExecutionContext> executorRegistry;

    public ExecutorDomainService(PluginRegistry<InspectionExecutor, ExecutionContext> executorRegistry) {
        this.executorRegistry = executorRegistry;
    }

    public InspectionExecutor selectOptimalExecutor(ExecutionContext context) {
        //@formatter:off
        // 获取合适的执行器
        return executorRegistry.getPluginFor(context)
                .orElseThrow(() -> new IllegalArgumentException(
                        "No suitable executor found for: " + context));
        //@formatter:on
    }
}
