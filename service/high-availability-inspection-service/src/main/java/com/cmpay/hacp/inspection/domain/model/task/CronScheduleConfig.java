package com.cmpay.hacp.inspection.domain.model.task;

import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CRON表达式调度配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CronScheduleConfig extends ScheduleConfig{
    private String cronExpression;

    @Override
    public ScheduleType getType() {
        return ScheduleType.CRON_EXPRESSION;
    }

    @Override
    public boolean isValid() {
        return cronExpression != null && !cronExpression.trim().isEmpty();
    }
}
