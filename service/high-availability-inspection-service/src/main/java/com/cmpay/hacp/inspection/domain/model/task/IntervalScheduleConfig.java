package com.cmpay.hacp.inspection.domain.model.task;

import com.cmpay.hacp.inspection.domain.model.enums.IntervalUnit;
import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 固定间隔调度配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IntervalScheduleConfig extends ScheduleConfig {
    private Integer intervalValue;
    private IntervalUnit intervalUnit;

    @Override
    public ScheduleType getType() {
        return ScheduleType.FIXED_INTERVAL;
    }

    @Override
    public boolean isValid() {
        return intervalValue != null && intervalValue > 0 && intervalUnit != null;
    }
}
