package com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("inspection_task_rule")
public class TaskRuleDO extends BaseDO {
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 规则ID
     */
    private String ruleId;
}
