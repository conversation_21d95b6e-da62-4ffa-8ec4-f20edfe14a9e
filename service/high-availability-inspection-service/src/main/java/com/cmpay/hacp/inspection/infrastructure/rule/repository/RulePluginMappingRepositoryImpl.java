package com.cmpay.hacp.inspection.infrastructure.rule.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginMapping;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginMappingRepository;
import com.cmpay.hacp.inspection.infrastructure.rule.converter.RulePluginConverter;
import com.cmpay.hacp.inspection.infrastructure.rule.repository.dataobject.RulePluginMappingDO;
import com.cmpay.hacp.inspection.infrastructure.rule.repository.mapper.RulePluginMappingMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class RulePluginMappingRepositoryImpl extends CrudRepository<RulePluginMappingMapper, RulePluginMappingDO> implements RulePluginMappingRepository {
    private final RulePluginConverter rulePluginConverter;

    @Override
    public RulePluginMapping findByRuleId(String ruleId) {
        return Optional.ofNullable(this.getOne(Wrappers.lambdaQuery(RulePluginMappingDO.class)
                        .eq(RulePluginMappingDO::getRuleId, ruleId)))
                .map(rulePluginConverter::toRulePluginMapping)
                .orElse(null);
    }

    @Override
    public void save(RulePluginMapping rulePluginMapping) {
        RulePluginMappingDO rulePluginMappingDO = rulePluginConverter.toRulePluginMappingDO(rulePluginMapping);
        this.save(rulePluginMappingDO);
    }

    @Override
    public void updatePluginId(String ruleId, String pluginId) {
        this.update(null, Wrappers.lambdaUpdate(RulePluginMappingDO.class)
                .eq(RulePluginMappingDO::getRuleId, ruleId)
                .set(RulePluginMappingDO::getPluginId, pluginId));
    }

    @Override
    public boolean removeByRuleId(String ruleId){
        return this.remove(Wrappers.lambdaUpdate(RulePluginMappingDO.class)
                .eq(RulePluginMappingDO::getRuleId, ruleId));
    }
}
