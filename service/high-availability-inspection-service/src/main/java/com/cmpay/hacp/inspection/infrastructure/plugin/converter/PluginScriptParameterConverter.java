package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptParameter;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginScriptParameterDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PluginScriptParameterConverter {
    
    @Mapping(target = "pluginId", source = "pluginId")
    @IgnoreAuditFields
    PluginScriptParameterDO toPluginScriptParameterDO(PluginScriptParameter parameter, String pluginId);

    List<PluginScriptParameter> toPluginScriptParameterList(List<PluginScriptParameterDO> list);
}
