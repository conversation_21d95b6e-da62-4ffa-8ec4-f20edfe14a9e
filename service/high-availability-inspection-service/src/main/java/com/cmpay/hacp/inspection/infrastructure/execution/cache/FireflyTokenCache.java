package com.cmpay.hacp.inspection.infrastructure.execution.cache;

import com.cmpay.hacp.inspection.domain.model.firefly.FireflyToken;
import com.cmpay.hacp.inspection.infrastructure.config.FireflyCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 萤火虫 Token 缓存实现
 * 用于缓存萤火虫认证token，避免频繁调用认证接口
 */
@Component
@Slf4j
public class FireflyTokenCache {
    private final Map<String, FireflyToken> tokenCache = new ConcurrentHashMap<>();

    /**
     * 获取缓存的token
     *
     * @param userId 用户ID
     * @return 萤火虫Token
     */
    @Cacheable(value = FireflyCacheConfig.FIREFLY_TOKENS, key = "#userId", unless = "#result == null")
    public FireflyToken getValidToken(String userId) {
        FireflyToken token = tokenCache.get(userId);
        return isTokenValid(token) ? token : null;
    }

    /**
     * 缓存token
     *
     * @param userId 用户ID
     * @param token  萤火虫Token对象
     */
    @CacheEvict(value = FireflyCacheConfig.FIREFLY_TOKENS, key = "#userId")
    public void cacheToken(String userId, FireflyToken token) {
        tokenCache.put(userId, token);
    }

    /**
     * 清除指定用户的token缓存
     *
     * @param userId 用户ID
     */
    @CacheEvict(value = FireflyCacheConfig.FIREFLY_TOKENS, key = "#userId")
    public void evictToken(String userId) {
        log.info("Evicting cached Firefly token for user: {}", userId);
        tokenCache.remove(userId);
    }

    /**
     * 清除所有token缓存
     */
    @CacheEvict(value = FireflyCacheConfig.FIREFLY_TOKENS, allEntries = true)
    public void evictAllTokens() {
        tokenCache.clear();
    }

    /**
     * 检查token是否存在且有效
     *
     * @param token token
     * @return boolean
     */
    public boolean isTokenValid(FireflyToken token) {
        return token != null && !token.isExpiringSoon();
    }
}
