package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.RuleExecutionMapper;
import org.springframework.stereotype.Repository;

/**
 * 规则执行状态Repository
 */
@Repository
public class RuleExecutionRepository extends CrudRepository<RuleExecutionMapper, RuleExecutionDO> {
}
