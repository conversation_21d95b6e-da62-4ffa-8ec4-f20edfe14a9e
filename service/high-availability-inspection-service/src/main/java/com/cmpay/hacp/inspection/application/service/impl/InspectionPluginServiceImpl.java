package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.service.InspectionPluginService;
import com.cmpay.hacp.inspection.domain.model.enums.PluginSourceEnum;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginConfig;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginTagMapping;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginRepository;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptRepository;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginTagRepository;
import com.cmpay.hacp.inspection.domain.tag.model.Tag;
import com.cmpay.hacp.inspection.domain.tag.repository.TagRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.PluginConfigStrategyFactory;
import com.cmpay.hacp.tenant.service.adapter.WorkspaceBaseServiceAdapter;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 巡检插件服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionPluginServiceImpl extends WorkspaceBaseServiceAdapter implements InspectionPluginService {

    private final PluginRepository pluginRepository;
    private final PluginScriptRepository pluginScriptRepository;
    private final PluginTagRepository pluginTagRepository;
    private final TagRepository tagRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPlugin(InspectionPlugin inspectionPlugin) {
        // 新增重名校验
        if (pluginRepository.getByPluginName(inspectionPlugin.getName()) != null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_NAME_EXIST);
        }

        // 标签校验
        if (CollectionUtils.isNotEmpty(inspectionPlugin.getTagIds())) {
            checkTagIds(inspectionPlugin.getTagIds());
        }

        // 保存插件基本信息
        InspectionPlugin pluginInfo = pluginRepository.save(inspectionPlugin);
        inspectionPlugin.setPluginId(pluginInfo.getPluginId());
        // 如果有标签，保存标签关联
        pluginTagRepository.saveLinkPluginId(pluginInfo.getPluginId(), inspectionPlugin.getTagIds());

        PluginConfigStrategyFactory.newInstance(inspectionPlugin.getType()).savePluginConfig(inspectionPlugin, inspectionPlugin.getPluginId());

        return inspectionPlugin.getPluginId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlugin(InspectionPlugin inspectionPlugin) {
        InspectionPlugin oldPlugin = pluginRepository.getByPluginId(inspectionPlugin.getPluginId());

        if (JudgeUtils.isNull(oldPlugin)) {
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_INFO_IS_NULL);
        }

        if(PluginSourceEnum.IN_BUILT.equals(oldPlugin.getSource())||PluginSourceEnum.IN_BUILT.equals(inspectionPlugin.getSource())){
            BusinessException.throwBusinessException(ErrorCodeEnum.SYSTEM_BUILT_NOT_ALLOW_UPDATE);
        }

        // 更新重名校验
        if (pluginRepository.existingPluginName(inspectionPlugin.getName(), inspectionPlugin.getPluginId())) {
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_NAME_EXIST);
        }

        // 标签校验
        if (CollectionUtils.isNotEmpty(inspectionPlugin.getTagIds())) {
            checkTagIds(inspectionPlugin.getTagIds());
        }

        // 1. 更新插件基本信息
        if (!pluginRepository.updatePlugin(inspectionPlugin)) {
            log.error("Failed to update plugin with ID: {}", inspectionPlugin.getPluginId());
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_UPDATE_FAILED);
        }

        // 3. 更新标签关联
        pluginTagRepository.updateLinkPluginId(inspectionPlugin.getPluginId(), inspectionPlugin.getTagIds());

        // 4. 处理插件配置
        PluginConfigStrategyFactory.newInstance(inspectionPlugin.getType()).updatePluginConfig(inspectionPlugin, inspectionPlugin.getPluginId());
    }

    /**
     * 标签校验
     *
     * @param tagIds 待校验的标签ID列表
     */
    public void checkTagIds(List<Long> tagIds) {
        // 查询数据库中存在哪些标签ID
        List<Tag> list = tagRepository.findTagByTagIds(tagIds);

        // 提取查询结果中的ID集合
        Set<Long> existIds = list.stream().map(Tag::getTagId).collect(Collectors.toSet());

        // 检查传入的ID是否全部存在于数据库
        if (tagIds.size() != existIds.size()) {
            BusinessException.throwBusinessException(ErrorCodeEnum.TAG_ID_NOT_EXIST);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePlugin(String pluginId) {
        InspectionPlugin plugin = pluginRepository.getByPluginId(pluginId);
        if(PluginSourceEnum.IN_BUILT.equals(plugin.getSource())){
            BusinessException.throwBusinessException(ErrorCodeEnum.SYSTEM_BUILT_NOT_ALLOW_DELETE);
        }
        pluginRepository.removeByPluginId(pluginId);

        pluginScriptRepository.removeByPluginId(pluginId);

        pluginTagRepository.removeByPluginId(pluginId);

        PluginConfigStrategyFactory.newInstance(plugin.getType()).remove(pluginId);
    }

    @Override
    public InspectionPlugin getPluginDetail(String pluginId) {
        InspectionPlugin inspectionPlugin = pluginRepository.getByPluginId(pluginId);
        if (inspectionPlugin == null) {
            return null;
        }
        PluginConfig pluginConfig = PluginConfigStrategyFactory.newInstance(inspectionPlugin.getType()).queryPluginConfig(pluginId);
        List<PluginTagMapping> pluginTagMappingList = pluginTagRepository.list(pluginId);


        inspectionPlugin.setPluginConfig(pluginConfig);
        inspectionPlugin.setTagIds(pluginTagMappingList.stream().map(PluginTagMapping::getTagId).collect(Collectors.toList()));
        return inspectionPlugin;
    }

    @Override
    public IPage<InspectionPlugin> getPluginPage(IPage<?> page, InspectionPlugin inspectionPlugin) {

        Set<String> pluginIdSet = Collections.emptySet();
        if (JudgeUtils.isNotEmpty(inspectionPlugin.getTagIds())) {
            List<PluginTagMapping> list = pluginTagRepository.listByTagIds(inspectionPlugin.getTagIds());
            if (JudgeUtils.isEmpty(list)) {
                return new Page<>();
            }
            pluginIdSet = list.stream().map(PluginTagMapping::getPluginId).collect(Collectors.toSet());
        }

        IPage<InspectionPlugin> pluginIPage = pluginRepository.page(page, inspectionPlugin, pluginIdSet);

        if (!pluginIPage.getRecords().isEmpty()) {
            enrichInspectionPlugins(pluginIPage.getRecords());
        }
        return pluginIPage;
    }

    @Override
    public List<InspectionPlugin> getPluginList(InspectionPlugin inspectionPlugin) {
        return pluginRepository.list(inspectionPlugin);
    }

    private void enrichInspectionPlugins(List<InspectionPlugin> records) {
        List<String> plugins = records.stream()
                .map(InspectionPlugin::getPluginId)
                .collect(Collectors.toList());

        // 添加标签
        List<PluginTagMapping> tagMappings = pluginTagRepository.listByPluginIds(plugins);

        // 构建关联数据映射
        Map<String, List<PluginTagMapping>> tagsMap = tagMappings.stream()
                .collect(Collectors.groupingBy(PluginTagMapping::getPluginId));

        records.forEach(record -> {
            String pluginId = record.getPluginId();

            // 设置标签ID列表
            List<Long> tagIds = Optional.ofNullable(tagsMap.get(pluginId))
                    .map(list -> list.stream().map(PluginTagMapping::getTagId).collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            record.setTagIds(tagIds);
        });
    }

    @Override
    public void initialization(String workspaceId) {
        // TODO 新增初始化插件
    }
}
