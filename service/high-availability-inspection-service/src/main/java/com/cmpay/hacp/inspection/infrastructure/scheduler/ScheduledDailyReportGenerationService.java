package com.cmpay.hacp.inspection.infrastructure.scheduler;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.service.DailyInspectionReportGenerationService;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.repository.DailyInspectionReportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduledDailyReportGenerationService {
    private final DailyInspectionReportGenerationService dailyInspectionReportGenerationService;
    private final DailyInspectionReportRepository dailyInspectionReportRepository;

    @Scheduled(cron = "0 30 1 * * ?") // 每日凌晨1:30执行
    @SchedulerLock(name = "scheduleGenerateYesterdayReport", lockAtMostFor = "30m", lockAtLeastFor = "1m")
    public void scheduleGenerateYesterdayReport() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        log.info("Scheduled task: generating yesterday's daily report for date: {}", yesterday);

        try {
            dailyInspectionReportGenerationService.generateDailyReport(yesterday, 0); // 0表示定时触发
        } catch (Exception e) {
            log.error("Failed to schedule yesterday's daily report generation for date: {}", yesterday, e);
        }
    }

    @Scheduled(cron = "0 0 * * * ?") // 每小时执行一次
    @SchedulerLock(name = "scheduleRetryFailedTasks", lockAtMostFor = "50m", lockAtLeastFor = "1m")
    public void scheduleRetryFailedTasks() {
        log.info("Scheduled task: retrying failed daily report generation tasks");

        try {
            // 查找最近7天内失败的报告
            LocalDate endDate = LocalDate.now().minusDays(1);
            LocalDate startDate = endDate.minusDays(6);

            List<DailyInspectionReportDO> failedReports = dailyInspectionReportRepository.list(
                    Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                            .eq(DailyInspectionReportDO::getReportStatus, "FAILED")
                            .between(DailyInspectionReportDO::getReportDate, startDate, endDate)
            );

            for (DailyInspectionReportDO failedReport : failedReports) {
                try {
                    dailyInspectionReportGenerationService.generateDailyReport(failedReport.getReportDate(), 2); // 2表示补偿触发
                    log.info("Scheduled retry for failed daily report on date: {}", failedReport.getReportDate());
                } catch (Exception e) {
                    log.error("Failed to schedule retry for date: {}", failedReport.getReportDate(), e);
                }
            }

        } catch (Exception e) {
            log.error("Failed to execute retry failed tasks schedule", e);
        }
    }
}
