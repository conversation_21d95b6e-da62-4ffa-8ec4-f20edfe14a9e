package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.model.enums.PluginStatus;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.PluginConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PluginMapper;
import com.cmpay.hacp.inspection.infrastructure.utils.IdGenUtil;
import com.cmpay.hacp.utils.JsonUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PluginRepositoryImpl extends CrudRepository<PluginMapper, PluginDO> implements PluginRepository {
    private final PluginConverter pluginConverter;

    @Override
    public InspectionPlugin getByPluginId(String pluginId) {
        return Optional.ofNullable(
                        this.getOne(
                                Wrappers.lambdaQuery(PluginDO.class)
                                        .eq(PluginDO::getPluginId, pluginId)
                        )
                ).map(pluginConverter::toInspectionPlugin)
                .orElse(null);
    }

    @Override
    public InspectionPlugin getByPluginName(String pluginName) {
        return Optional.ofNullable(
                        this.getOne(
                                Wrappers.lambdaQuery(PluginDO.class)
                                        .eq(PluginDO::getName, pluginName)
                        )
                ).map(pluginConverter::toInspectionPlugin)
                .orElse(null);
    }

    @Override
    public InspectionPlugin save(InspectionPlugin inspectionPlugin) {
        // 1. 将领域对象转换为DO
        PluginDO pluginDO = pluginConverter.toPluginDO(inspectionPlugin);
        pluginDO.setPluginId(IdGenUtil.generateTempId());

        if(CollectionUtils.isEmpty(pluginDO.getDeployEnvs())){
            pluginDO.setDeployEnvs(pluginDO.getType().getDeployEnvs());
        }
        this.save(pluginDO);
        // 业务标识符
        String pluginId = IdGenUtil.generatePluginId(pluginDO.getId());
        pluginDO.setPluginId(pluginId);
        // 只更新业务标识符字段，减少更新范围
        this.update(pluginDO, Wrappers.lambdaUpdate(PluginDO.class)
                .eq(PluginDO::getId, pluginDO.getId())
                .set(PluginDO::getPluginId, pluginDO.getPluginId()));

        return pluginConverter.toInspectionPlugin(pluginDO);
    }

    @Override
    public boolean existingPluginName(String pluginName, String excludePluginId) {
        return this.count(
                Wrappers.lambdaQuery(PluginDO.class)
                        .eq(PluginDO::getName, pluginName)
                        .ne(PluginDO::getPluginId, excludePluginId)
        ) > 0;
    }

    @Override
    public boolean updatePlugin(InspectionPlugin inspectionPlugin) {
        return this.update(new PluginDO(), Wrappers.lambdaUpdate(PluginDO.class)
                .eq(PluginDO::getPluginId, inspectionPlugin.getPluginId())
                .set(PluginDO::getName, inspectionPlugin.getName())
                .set(PluginDO::getType, inspectionPlugin.getType())
                .set(PluginDO::getStatus, inspectionPlugin.getStatus())
                .set(PluginDO::getDescription, inspectionPlugin.getDescription())
                .set(CollectionUtils.isNotEmpty(inspectionPlugin.getDeployEnvs()),PluginDO::getDeployEnvs, JsonUtil.objToStr(inspectionPlugin.getDeployEnvs())));
    }

    @Override
    public void removeByPluginId(String pluginId) {
        this.remove(Wrappers.lambdaUpdate(PluginDO.class)
                .eq(PluginDO::getPluginId, pluginId));
    }

    @Override
    public IPage<InspectionPlugin> page(IPage<?> page, InspectionPlugin inspectionPlugin, Set<String> pluginIdSet) {
        Page<PluginDO> queryPage = new Page<>(page.getCurrent(), page.getSize());

        LambdaQueryWrapper<PluginDO> queryWrapper = Wrappers.lambdaQuery(PluginDO.class)
                .like(StringUtils.isNotBlank(inspectionPlugin.getName()), PluginDO::getName, inspectionPlugin.getName())
                .eq(inspectionPlugin.getType() != null, PluginDO::getType, inspectionPlugin.getType())
                .eq(inspectionPlugin.getStatus() != null, PluginDO::getStatus, inspectionPlugin.getStatus())
                .between(inspectionPlugin.getStartTime() != null && inspectionPlugin.getEndTime() != null,
                        PluginDO::getCreatedTime, inspectionPlugin.getStartTime(), inspectionPlugin.getEndTime())
                .orderByAsc(PluginDO::getId);

        if (CollectionUtils.isNotEmpty(pluginIdSet)) {
            queryWrapper.in(PluginDO::getPluginId, pluginIdSet);
        }
        IPage<PluginDO> pluginPage = this.page(queryPage, queryWrapper);
        return pluginPage.convert(pluginConverter::toInspectionPlugin);
    }

    @Override
    public List<InspectionPlugin> list(InspectionPlugin inspectionPlugin) {
        LambdaQueryWrapper<PluginDO> queryWrapper = Wrappers.lambdaQuery(PluginDO.class)
                .eq(inspectionPlugin.getType() != null, PluginDO::getType, inspectionPlugin.getType())
                .eq(PluginDO::getStatus, PluginStatus.ENABLE.getCode())
                .orderByAsc(PluginDO::getId);
        List<PluginDO> pluginPage = this.list(queryWrapper);
        return pluginPage.stream().map(pluginConverter::toInspectionPlugin).collect(Collectors.toList());
    }
}
