package com.cmpay.hacp.inspection.domain.execution.gateway;

import com.cmpay.hacp.inspection.domain.execution.model.K8sConnectionConfig;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionResult;

/**
 * K8s网关接口
 * 提供基于kubectl exec的脚本执行能力
 */
public interface K8sGateway {

    /**
     * 在K8s Pod中执行脚本
     *
     * @param connectionConfig K8s连接配置
     * @param request 脚本执行请求
     * @return 脚本执行结果
     */
    ScriptExecutionResult executeScript(K8sConnectionConfig connectionConfig,
                                        ScriptExecutionRequest request);

    /**
     * 在K8s Pod中执行简单命令
     *
     * @param connectionConfig K8s连接配置
     * @param command 要执行的命令
     * @return 命令执行结果
     */
    ScriptExecutionResult executeCommand(K8sConnectionConfig connectionConfig,
                                         String command);
}
