package com.cmpay.hacp.inspection.infrastructure.condition.converter;

import com.cmpay.hacp.inspection.domain.condition.model.RuleCondition;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionDO;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreInsertAuditFields;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RuleConditionConverter {

    RuleCondition toRuleCondition(RuleConditionDO conditionDO);

    @IgnoreInsertAuditFields
    RuleConditionDO toRuleConditionDO(RuleCondition condition);

    List<RuleConditionDO> toRuleConditionDOList(List<RuleCondition> ruleConditions);

}
