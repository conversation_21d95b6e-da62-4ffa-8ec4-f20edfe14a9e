package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginTagMapping;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface PluginTagRepository {
    void saveBatch(List<PluginTagMapping> pluginTagMappingList);

    void removeByPluginId(@NotNull String pluginId);

    List<PluginTagMapping> list(@NotNull String pluginId);

    void removeByTagIds(@NotNull List<Long> tagIds,@NotNull String pluginId);

    List<PluginTagMapping> listByTagIds(List<Long> tagIds);

    List<PluginTagMapping> listByPluginIds(List<String> pluginIds);

    List<PluginTagMapping> listAll();

    void updateLinkPluginId(@NotNull String pluginId, List<Long> tagIds);

    void saveLinkPluginId(@NotNull String pluginId, List<Long> tagIds);
}
