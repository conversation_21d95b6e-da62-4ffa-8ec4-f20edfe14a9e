package com.cmpay.hacp.inspection.domain.task.repository;

import com.cmpay.hacp.inspection.domain.model.task.AlarmNotification;

import javax.validation.constraints.NotNull;

public interface TaskAlarmRepository {

    void saveOrUpdate(@NotNull String taskId, AlarmNotification alarmNotification);

    void removeByTaskId(@NotNull String taskId);

    AlarmNotification queryByTaskId(String taskId);
}
