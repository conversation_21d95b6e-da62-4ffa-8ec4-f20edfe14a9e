package com.cmpay.hacp.inspection.infrastructure.condition.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionGroupInfoDO;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.mapper.RuleConditionGroupInfoMapper;
import org.springframework.stereotype.Repository;

@Repository
public class RuleConditionGroupInfoRepository extends CrudRepository<RuleConditionGroupInfoMapper, RuleConditionGroupInfoDO> {
}
