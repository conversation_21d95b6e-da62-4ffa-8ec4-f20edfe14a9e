package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 执行状态
 */
@Getter
@Slf4j
public enum ExecutionStatus {
    PENDING(0, "待执行"),
    RUNNING(1, "执行中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "执行失败"),
    CANCELLED(4,"已取消"),
    ALERT(5,"告警"),
    TIMEOUT(6,"超时"),
    STOPPED(7, "已停止");

    @JsonValue
    private final Integer code;
    private final String desc;

    ExecutionStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    private static final Map<Integer, ExecutionStatus> ENUM_MAP = Arrays.stream(ExecutionStatus.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static ExecutionStatus getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
