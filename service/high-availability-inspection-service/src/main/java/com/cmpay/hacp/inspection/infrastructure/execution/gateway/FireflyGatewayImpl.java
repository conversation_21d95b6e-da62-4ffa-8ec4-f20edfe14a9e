package com.cmpay.hacp.inspection.infrastructure.execution.gateway;

import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.infrastructure.execution.config.FireflyProperties;
import com.cmpay.hacp.inspection.domain.execution.gateway.FireflyGateway;
import com.cmpay.hacp.inspection.domain.model.firefly.*;
import com.cmpay.hacp.inspection.infrastructure.execution.cache.FireflyTokenCache;
import com.cmpay.lemon.common.exception.BusinessException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 萤火虫网关实现类
 * 使用OkHttp客户端与萤火虫系统进行HTTP通信
 */
@Slf4j
@Component
@EnableConfigurationProperties(FireflyProperties.class)
public class FireflyGatewayImpl implements FireflyGateway, InitializingBean, DisposableBean {

    private final FireflyTokenCache fireflyTokenCache;
    private final FireflyProperties properties;
    private final JsonMapper jsonMapper;

    private OkHttpClient httpClient;

    private final Map<String, Object> lockMap = new ConcurrentHashMap<>();

    public FireflyGatewayImpl(FireflyTokenCache fireflyTokenCache, FireflyProperties properties) {
        this.fireflyTokenCache = fireflyTokenCache;
        this.properties = properties;
        this.jsonMapper = JsonMapper.builder()
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .addModule(new JavaTimeModule())
                .build();
    }

    @Override
    public void afterPropertiesSet() {
        initializeHttpClient();
        log.info("Firefly gateway initialized with base URL: {}", properties.getBaseUrl());
    }

    @Override
    public void destroy() {
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
            log.info("Firefly HTTP client has been gracefully shut down");
        }
    }

    /**
     * 初始化OkHttp客户端
     */
    private void initializeHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(properties.getHttp().getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(properties.getHttp().getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(properties.getHttp().getWriteTimeout(), TimeUnit.MILLISECONDS)
                .connectionPool(new ConnectionPool(
                        properties.getHttp().getMaxIdleConnections(),
                        properties.getHttp().getKeepAliveDuration(),
                        TimeUnit.MINUTES
                ));

        // 如果禁用SSL验证，配置信任所有证书
        if (!properties.getHttp().isEnableSslCheck()) {
            try {
                final TrustManager[] trustAllCerts = new TrustManager[]{
                        new X509TrustManager() {
                            @Override
                            public void checkClientTrusted(X509Certificate[] chain, String authType) {
                            }

                            @Override
                            public void checkServerTrusted(X509Certificate[] chain, String authType) {
                            }

                            @Override
                            public X509Certificate[] getAcceptedIssuers() {
                                return new X509Certificate[0];
                            }
                        }
                };

                final SSLContext sslContext = SSLContext.getInstance("TLS");
                sslContext.init(null, trustAllCerts, new SecureRandom());
                builder.sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0]);
                builder.hostnameVerifier((hostname, session) -> true);

                log.warn("SSL verification disabled for Firefly client");
            } catch (Exception e) {
                log.error("Failed to disable SSL verification", e);
                throw new RuntimeException("Failed to configure SSL", e);
            }
        }

        this.httpClient = builder.build();
    }

    @Override
    public FireflyToken getAuthToken() {
        String userId = properties.getAuth().getUserId();

        // 尝试从缓存获取有效token
        FireflyToken cachedToken = fireflyTokenCache.getValidToken(userId);
        if (cachedToken != null) {
            return cachedToken;
        }

        // 使用用户级别的锁避免重复请求
        Object lock = lockMap.computeIfAbsent(userId, k -> new Object());
        synchronized (lock) {
            // 双重检查：可能在等待锁的过程中其他线程已经获取了token
            cachedToken = fireflyTokenCache.getValidToken(userId);
            if (cachedToken != null) {
                return cachedToken;
            }

            return fetchAndCacheNewToken(userId);
        }
    }

    private FireflyToken fetchAndCacheNewToken(String userId) {
        FireflyToken token = fetchNewToken();
        fireflyTokenCache.cacheToken(userId, token);
        return token;
    }

    /**
     * 从认证服务器获取新的token
     */
    private FireflyToken fetchNewToken() {
        try {
            // 构建请求对象
            FireflyTokenRequest tokenRequest = FireflyTokenRequest.builder()
                    .userId(properties.getAuth().getUserId())
                    .userKey(properties.getAuth().getUserKey())
                    .build();

            // 将请求对象转换为JSON
            String requestJson = jsonMapper.writeValueAsString(tokenRequest);

            RequestBody requestBody = RequestBody.create(
                    requestJson,
                    MediaType.get("application/json; charset=utf-8")
            );

            Request request = new Request.Builder()
                    .url(properties.getBaseUrl() + properties.getAuth().getTokenUrl())
                    .post(requestBody)
                    .build();

            log.debug("Requesting Firefly token from: {}", properties.getBaseUrl() + properties.getAuth().getTokenUrl());

            try (Response response = httpClient.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.debug("Token response received: HTTP {}, body: {}", response.code(), responseBody);

                if (!response.isSuccessful()) {
                    log.error("Failed to get Firefly token, HTTP status: {}, body: {}",
                            response.code(), responseBody);
                    BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_CONNECTION_ERROR);
                }

                return parseTokenResponse(responseBody);
            }
        } catch (IOException e) {
            log.error("Network error while fetching Firefly token", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_CONNECTION_ERROR);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while fetching Firefly token", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_AUTH_ERROR);
        }
        return null;
    }

    /**
     * 解析token响应
     */
    private FireflyToken parseTokenResponse(String responseBody) {
        try {
            FireflyTokenResponse tokenResponse = jsonMapper.readValue(responseBody, FireflyTokenResponse.class);

            // 检查响应是否成功
            if (!tokenResponse.isSuccess()) {
                String errorMsg = String.format("Token request failed: %s - %s",
                        tokenResponse.getMsgCd(),
                        tokenResponse.getMsgInfo());
                log.error(errorMsg);

                // 根据不同的错误码抛出相应的异常
                if (tokenResponse.isAuthError()) {
                    BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_AUTH_ERROR);
                } else if (tokenResponse.isUserStatusError()) {
                    BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_AUTH_ERROR);
                } else {
                    BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_INVALID_RESPONSE);
                }
            }

            String accessToken = tokenResponse.getToken();
            Long expiresIn = tokenResponse.getExpire();

            if (accessToken == null || accessToken.trim().isEmpty()) {
                log.error("Invalid token response: missing or empty token");
                BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_INVALID_RESPONSE);
            }

            // 如果响应中没有expire，使用配置的默认值
            if (expiresIn == null || expiresIn <= 0) {
                expiresIn = properties.getAuth().getTokenCacheMinutes() * 60L;
            }

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expiresAt = now.plusSeconds(expiresIn);

            log.info("Successfully obtained Firefly token for user: {}, expires in {} seconds",
                    tokenResponse.getUserId(), expiresIn);

            return FireflyToken.builder()
                    .accessToken(accessToken)
                    .tokenType("Bearer") // 固定使用Bearer类型
                    .createdAt(now)
                    .expiresAt(expiresAt)
                    .build();

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to parse token response: {}", responseBody, e);
            BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_INVALID_RESPONSE);
        }
        return null;
    }

    @Override
    public IndicatorQueryResponse getIndicatorDataList(IndicatorQueryRequest request) {
        try {
            // 验证请求参数
            if (!validateIndicatorRequest(request)) {
                return IndicatorQueryResponse.builder()
                        .msgCd("FFM80003")
                        .msgInfo("请求参数验证失败")
                        .build();
            }

            FireflyToken token = getAuthToken();

            // 构建请求URL
            String url = properties.getBaseUrl() + properties.getIndicatorUrl();

            // 构建请求体
            String requestJson = jsonMapper.writeValueAsString(request);

            RequestBody requestBody = RequestBody.create(
                    requestJson,
                    MediaType.get("application/json; charset=utf-8")
            );

            Request httpRequest = new Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .addHeader("Authorization", token.getAuthorizationHeader())
                    .build();

            log.debug("Executing indicator query: {}", url);
            log.debug("Request body: {}", requestJson);

            try (Response response = httpClient.newCall(httpRequest).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.debug("Indicator query response: HTTP {}, body: {}", response.code(), responseBody);

                if (!response.isSuccessful()) {
                    log.error("Indicator query failed, HTTP status: {}, body: {}",
                            response.code(), responseBody);

                    // 如果是401错误，可能是token过期，清除缓存的token
                    if (response.code() == 401) {
                        fireflyTokenCache.evictToken(properties.getAuth().getUserId());
                        BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_TOKEN_EXPIRED);
                    }

                    BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_QUERY_ERROR);
                }

                return parseIndicatorQueryResponse(responseBody);
            }

        } catch (IOException e) {
            log.error("Network error during indicator query", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_CONNECTION_ERROR);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during indicator query", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_QUERY_ERROR);
        }
        return null;
    }

    /**
     * 验证指标查询请求
     */
    private boolean validateIndicatorRequest(IndicatorQueryRequest request) {
        // 基本参数验证
        if (request.getStartTime() == null || request.getEndTime() == null) {
            log.error("Start time and end time are required");
            return false;
        }

        if (request.getIndicatorType() == null || request.getIndicatorType().trim().isEmpty()) {
            log.error("Indicator type is required");
            return false;
        }

        if (request.getIndicatorName() == null || request.getIndicatorName().trim().isEmpty()) {
            log.error("Indicator name is required");
            return false;
        }

        if (request.getPageNum() == null || request.getPageNum() < 1) {
            log.error("Page number must be greater than 0");
            return false;
        }

        if (request.getPageSize() == null || request.getPageSize() < 1 || request.getPageSize() > 3000) {
            log.error("Page size must be between 1 and 3000");
            return false;
        }

        // 时间间隔验证（不大于24小时）
        long timeDiff = java.time.Duration.between(request.getStartTime(), request.getEndTime()).toHours();
        if (timeDiff > 24) {
            log.error("Time interval cannot exceed 24 hours");
            return false;
        }

        return true;
    }

    /**
     * 解析指标查询响应
     */
    private IndicatorQueryResponse parseIndicatorQueryResponse(String responseBody) {
        try {
            Map responseMap = jsonMapper.readValue(responseBody, Map.class);

            String msgCd = (String) responseMap.get("msgCd");
            String msgInfo = (String) responseMap.get("msgInfo");

            IndicatorQueryResponse.IndicatorQueryResponseBuilder responseBuilder =
                    IndicatorQueryResponse.builder()
                            .msgCd(msgCd)
                            .msgInfo(msgInfo);

            // 如果成功，解析数据部分
            if ("FFM00000".equals(msgCd)) {
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) responseMap.get("dataList");

                if (dataList != null) {
                    java.util.List<IndicatorQueryResponse.IndicatorData> indicatorDataList =
                            new java.util.ArrayList<>();

                    for (Map<String, Object> dataItem : dataList) {
                        IndicatorQueryResponse.IndicatorData indicatorData = parseIndicatorDataItem(dataItem);
                        if (indicatorData != null) {
                            indicatorDataList.add(indicatorData);
                        }
                    }

                    responseBuilder.dataList(indicatorDataList);
                }

                // 解析分页信息
                Object totalCount = responseMap.get("totalCount");

                if (totalCount instanceof Long) {
                    responseBuilder.totalCount((Long) totalCount);
                }
            }

            return responseBuilder.build();

        } catch (Exception e) {
            log.error("Failed to parse indicator query response: {}", responseBody, e);
            BusinessException.throwBusinessException(ErrorCodeEnum.FIREFLY_INVALID_RESPONSE);
        }
        return null;
    }

    /**
     * 解析单个指标数据项
     */
    private IndicatorQueryResponse.IndicatorData parseIndicatorDataItem(java.util.Map<String, Object> dataItem) {
        try {
            IndicatorQueryResponse.IndicatorData.IndicatorDataBuilder builder =
                    IndicatorQueryResponse.IndicatorData.builder();

            // 解析基本字段
            builder.indicatorName((String) dataItem.get("indicatorName"));
            builder.value((String) dataItem.get("value"));
            builder.zone((String) dataItem.get("zone"));

            // 解析时间戳
            Object timestamp = dataItem.get("timestamp");
            if (timestamp instanceof LocalDateTime) {
                builder.timestamp((LocalDateTime) timestamp);
            }

            // 解析特殊字段
            builder.one((String) dataItem.get("specialNameOne"));
            builder.two((String) dataItem.get("specialNameTwo"));
            builder.three((String) dataItem.get("specialNameThree"));
            builder.four((String) dataItem.get("specialNameFour"));
            builder.five((String) dataItem.get("specialNameFive"));
            builder.six((String) dataItem.get("specialNameSix"));
            builder.seven((String) dataItem.get("specialNameSeven"));
            builder.eight((String) dataItem.get("specialNameEight"));
            builder.nine((String) dataItem.get("specialNameNine"));
            builder.ten((String) dataItem.get("specialNameTen"));

            return builder.build();

        } catch (Exception e) {
            log.error("Failed to parse indicator data item: {}", dataItem, e);
            return null;
        }
    }
}
