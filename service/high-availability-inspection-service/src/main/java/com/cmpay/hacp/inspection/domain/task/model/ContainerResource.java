package com.cmpay.hacp.inspection.domain.task.model;

import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("2")
@Schema(description = "容器配置")
public class ContainerResource extends Resource {

    private String cluster;

    private String namespace;

    private String workspace;

}
