package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReportDetail;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 按次巡检报告领域对象转换器
 * 用于领域对象和数据对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface PerInspectionReportMapper {

    /**
     * 领域对象转DO（用于查询条件）
     *
     * @param report 领域对象
     * @return DO对象
     */
    @IgnoreAuditFields
    PerInspectionReportDO toPerInspectionReportDO(PerInspectionReport report);

    /**
     * DO列表转领域对象列表
     *
     * @param reportDOList DO列表
     * @return 领域对象列表
     */
    List<PerInspectionReport> toPerInspectionReportList(List<PerInspectionReportDO> reportDOList);

    /**
     * 详细内容DO转领域对象
     *
     * @param detailDO 详细内容DO
     * @return 详细内容领域对象
     */
    @Mapping(target = "ruleCheckDetails", ignore = true)
    PerInspectionReportDetail toPerInspectionReportDetail(PerInspectionReportDetailDO detailDO);
}
