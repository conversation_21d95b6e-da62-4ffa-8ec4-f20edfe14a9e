package com.cmpay.hacp.inspection.infrastructure.task.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.model.task.AlarmNotification;
import com.cmpay.hacp.inspection.domain.task.repository.TaskAlarmRepository;
import com.cmpay.hacp.inspection.infrastructure.task.converter.AlarmNotificationConverter;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskAlarmDO;
import com.cmpay.hacp.inspection.infrastructure.task.repository.mapper.TaskAlarmMapper;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class TaskAlarmRepositoryImpl extends CrudRepository<TaskAlarmMapper, TaskAlarmDO> implements TaskAlarmRepository {

    private final AlarmNotificationConverter alarmNotificationConverter;

    @Override
    public void saveOrUpdate(String taskId, AlarmNotification alarmNotification) {
        this.removeByTaskId(taskId);
        if (JudgeUtils.isNotNull(alarmNotification)) {
            TaskAlarmDO taskAlarmDO = alarmNotificationConverter.toTaskAlarmDO(taskId, alarmNotification);
            this.save(taskAlarmDO);
        }
    }

    @Override
    public void removeByTaskId(String taskId) {
        this.remove(Wrappers.lambdaUpdate(TaskAlarmDO.class).eq(TaskAlarmDO::getTaskId, taskId));
    }

    @Override
    public AlarmNotification queryByTaskId(String taskId) {
        TaskAlarmDO taskAlarmDO = this.getOne(Wrappers.lambdaQuery(TaskAlarmDO.class).eq(TaskAlarmDO::getTaskId, taskId));
        return alarmNotificationConverter.toAlarmNotification(taskAlarmDO);
    }
}
