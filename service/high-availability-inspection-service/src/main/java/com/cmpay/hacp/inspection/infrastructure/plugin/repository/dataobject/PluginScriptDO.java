package com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultType;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检插件脚本内容实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_plugin_script")
public class PluginScriptDO extends BaseDO {
    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 脚本内容
     */
    private String scriptContent;

    /**
     * 脚本输出类型（0：结构化、1：纯文本）
     */
    private ScriptResultType scriptResultType;
}
