package com.cmpay.hacp.inspection.infrastructure.tag.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseInsertDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则标签关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_rule_tag")
public class RuleTagDO extends BaseInsertDO {
    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 标签ID
     */
    private Long tagId;
}
