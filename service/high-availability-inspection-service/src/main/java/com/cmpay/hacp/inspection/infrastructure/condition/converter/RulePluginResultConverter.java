package com.cmpay.hacp.inspection.infrastructure.condition.converter;

import com.cmpay.hacp.inspection.domain.condition.model.RulePluginResult;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionDO;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionGroupDO;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionGroupInfoDO;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreInsertAuditFields;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring",uses = {RuleConditionGroupInfoConverter.class,RuleConditionConverter.class})
public interface RulePluginResultConverter {

    @IgnoreInsertAuditFields
    RuleConditionGroupDO toRuleConditionGroupDO(RulePluginResult ruleConditionGroupDOS);


    @Mapping(target = "conditionGroupId",source = "ruleConditionGroupDO.conditionGroupId")
    RulePluginResult toRulePluginResult(RuleConditionGroupDO ruleConditionGroupDO, RuleConditionGroupInfoDO ruleConditionInfo, List<RuleConditionDO> ruleConditions);
}
