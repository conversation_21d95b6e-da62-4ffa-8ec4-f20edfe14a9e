package com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则资源关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_task_rule_resource")
public class TaskRuleResourceDO extends BaseDO {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 资源
     */
    private String resource;

    private ResourceType resourceType;
}
