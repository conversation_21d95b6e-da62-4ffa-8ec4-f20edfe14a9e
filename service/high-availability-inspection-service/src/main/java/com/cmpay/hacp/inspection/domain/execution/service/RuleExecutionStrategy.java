package com.cmpay.hacp.inspection.domain.execution.service;

import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 规则执行策略 - 领域服务接口
 * 定义不同资源类型的执行策略
 */
public interface RuleExecutionStrategy {
    void execute(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount);
    boolean supports(ResourceType resourceType);
}
