package com.cmpay.hacp.inspection.infrastructure.tag.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.tag.repository.RuleTagRepository;
import com.cmpay.hacp.inspection.infrastructure.tag.repository.dataobject.RuleTagDO;
import com.cmpay.hacp.inspection.infrastructure.tag.repository.mapper.RuleTagsMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 规则标签关联Repository
 */
@Repository
public class RuleTagRepositoryImpl extends CrudRepository<RuleTagsMapper, RuleTagDO> implements RuleTagRepository {

    @Override
    public List<RuleTagDO> listAll() {
        return this.list();
    }

    @Override
    public List<RuleTagDO> listByTagIds(List<Long> tagIds) {
        if(CollectionUtils.isEmpty(tagIds)){
            return Collections.emptyList();
        }
        return this.list(Wrappers.lambdaQuery(RuleTagDO.class).in(RuleTagDO::getTagId, tagIds));
    }

    @Override
    public List<RuleTagDO> listByRuleIds(List<String> ruleIds) {
        if(CollectionUtils.isEmpty(ruleIds)){
            return Collections.emptyList();
        }
        return this.list(Wrappers.lambdaQuery(RuleTagDO.class).in(RuleTagDO::getRuleId, ruleIds));
    }

    @Override
    public void removeByRuleId(String ruleId) {
        this.remove(Wrappers.lambdaQuery(RuleTagDO.class).eq(RuleTagDO::getRuleId, ruleId));
    }

    @Override
    public List<RuleTagDO> listByRuleId(String ruleId) {
        return this.list(Wrappers.lambdaQuery(RuleTagDO.class).eq(RuleTagDO::getRuleId, ruleId));
    }

    @Override
    public void removeByTagIds(List<Long> tagIds) {
        this.removeByIds(tagIds);
    }

    @Override
    public void saveBatch(List<RuleTagDO> tagsToInsert) {
        if(tagsToInsert == null || tagsToInsert.isEmpty()){
            return;
        }
        this.saveBatch(tagsToInsert, 10);
    }

    @Override
    public void updateLinkRuleId(String ruleId, List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            // 如果标签为空，直接删除所有关联
            this.removeByRuleId(ruleId);
        } else {
            // 获取原有标签关联
            List<RuleTagDO> originList = this.listByRuleId(ruleId);

            // 将原有标签ID转换为Set提高查找效率
            Set<Long> existingTagIds = originList.stream()
                    .map(RuleTagDO::getTagId)
                    .collect(Collectors.toSet());

            // 将需要保留的标签ID转为Set
            Set<Long> retainedTagIds = new HashSet<>(tagIds);

            // 准备插入列表 - 找出新增的标签ID
            List<RuleTagDO> tagsToInsert = new ArrayList<>();
            for (Long tagId : retainedTagIds) {
                if (!existingTagIds.contains(tagId)) {
                    RuleTagDO newTag = new RuleTagDO();
                    newTag.setRuleId(ruleId);
                    newTag.setTagId(tagId);
                    tagsToInsert.add(newTag);
                }
            }

            // 因标签关联对象只有规则ID和标签ID，所以更新列表无需更新
            // 准备删除列表 - 找出需要删除的标签
            List<Long> idsToDelete = new ArrayList<>();
            for (RuleTagDO existingTag : originList) {
                if (!retainedTagIds.contains(existingTag.getTagId())) {
                    idsToDelete.add(existingTag.getId());
                }
            }

            if (!tagsToInsert.isEmpty()) {
                this.saveBatch(tagsToInsert);
            }
            if (!idsToDelete.isEmpty()) {
                this.removeByTagIds(idsToDelete);
            }
        }
    }

    @Override
    public void saveLinkRuleId(String ruleId, List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return;
        }
        // 保存新标签关联
        List<RuleTagDO> mappings = new ArrayList<>();
        for (Long tagId : tagIds) {
            RuleTagDO mapping = new RuleTagDO();
            mapping.setRuleId(ruleId);
            mapping.setTagId(tagId);
            mappings.add(mapping);
        }

        this.saveBatch(mappings);
    }
}
