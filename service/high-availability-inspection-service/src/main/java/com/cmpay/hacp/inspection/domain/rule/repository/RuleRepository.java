package com.cmpay.hacp.inspection.domain.rule.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleDO;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

public interface RuleRepository {
    IPage<InspectionRule> page(Page<RuleDO> mpPage, InspectionRule queryCondition, Set<String> ruleIdSet);

    InspectionRule save(InspectionRule inspectionRule);

    boolean checkNameExist(@NotNull String ruleId,@NotNull String ruleName);

    RuleDO findByRuleId(@NotNull String ruleId);

    boolean update(InspectionRule inspectionRule);

    void removeByRuleId(@NotNull String ruleId);

    List<InspectionRule> queryList(InspectionRule queryCondition);

    InspectionRule getByRuleId(@NotNull String ruleId);

    List<RuleDO> listByRuleIds(List<String> ruleIds);
}
