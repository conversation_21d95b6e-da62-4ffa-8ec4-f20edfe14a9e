package com.cmpay.hacp.inspection.infrastructure.validation;

import org.quartz.CronExpression;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class CronExpressionValidator implements ConstraintValidator<ValidCronExpression, String> {

    @Override
    public boolean isValid(String cronExpression, ConstraintValidatorContext context) {
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            return false;
        }

        // 使用Quartz的CronExpression进行验证
        return CronExpression.isValidExpression(cronExpression.trim());
    }
}
