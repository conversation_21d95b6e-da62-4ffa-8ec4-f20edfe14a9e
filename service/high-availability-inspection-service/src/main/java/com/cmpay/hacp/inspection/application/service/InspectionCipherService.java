package com.cmpay.hacp.inspection.application.service;

public interface InspectionCipherService {

    String dataEncrypt(String encryptData);
    String dataDecrypt(String decryptData);

    String getSm4RandomSalt(String key);

    /**
     * 解密管理应用sm4+sm2加密的数据
     * 巡检的加密key进行加密数据
     *
     * @param key
     * @param data
     * @return
     */
    String decryptSysDataBySm4AndSm2AndEncrypt(String key,String data);
}
