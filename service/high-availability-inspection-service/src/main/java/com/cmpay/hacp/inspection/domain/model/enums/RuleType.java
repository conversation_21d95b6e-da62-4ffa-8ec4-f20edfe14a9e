package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 规则类型
 */
@Getter
@Slf4j
public enum RuleType {
    METRIC_INSPECTION(1, "指标检查"),
    LOG_INSPECTION(2, "日志检查"),
    AVAILABILITY_INSPECTION(3, "可用性检查");

    @JsonValue
    private final Integer code;
    private final String desc;

    RuleType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    private static final Map<Integer, RuleType> ENUM_MAP = Arrays.stream(RuleType.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static RuleType getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }

}
