package com.cmpay.hacp.inspection.domain.rule.model;

import lombok.Data;

/**
 * 规则插件参数配置
 */
@Data
public class RulePluginParam {

    /**
     * 插件ID
     */
    private String pluginId;

    private String ruleId;

    /**
     * 插件参数Name
     */
    private String pluginParamName;

    /**
     * 插件参数值
     */
    private String pluginParamValue;

    /**
     * 是否加密
     */
    private Boolean isEncrypted;

}
