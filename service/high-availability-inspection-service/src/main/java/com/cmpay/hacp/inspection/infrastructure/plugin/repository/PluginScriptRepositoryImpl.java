package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginConfigScript;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScript;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.PluginScriptConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginScriptDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PluginScriptMapper;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class PluginScriptRepositoryImpl extends CrudRepository<PluginScriptMapper, PluginScriptDO> implements PluginScriptRepository {
    private final PluginScriptConverter pluginScriptConverter;

    @Override
    public PluginScript getByPluginId(String pluginId) {
        return pluginScriptConverter.toPluginScript(this.getOne(
                Wrappers.lambdaQuery(PluginScriptDO.class)
                        .eq(PluginScriptDO::getPluginId, pluginId)));
    }

    @Override
    public void save(PluginConfigScript pluginConfigScript, String pluginId) {
        if(JudgeUtils.isBlank(pluginConfigScript.getScriptContent())){
            return;
        }
        PluginScriptDO pluginScriptDO = pluginScriptConverter.pluginConfigScriptToPluginScriptDO(pluginConfigScript, pluginId);
        this.save(pluginScriptDO);
    }

    @Override
    public boolean updateByPluginId(PluginScript pluginScript) {
        if(JudgeUtils.isBlank(pluginScript.getScriptContent())){
            this.removeByPluginId(pluginScript.getPluginId());
            return true;
        }
        return this.update(Wrappers.lambdaUpdate(PluginScriptDO.class)
                .eq(PluginScriptDO::getPluginId, pluginScript.getPluginId())
                .set(PluginScriptDO::getScriptContent, pluginScript.getScriptContent())
                .set(PluginScriptDO::getScriptResultType, pluginScript.getScriptResultType()));
    }

    @Override
    public void removeByPluginId(String pluginId) {
        this.remove(Wrappers.lambdaQuery(PluginScriptDO.class)
                .eq(PluginScriptDO::getPluginId, pluginId));
    }
}
