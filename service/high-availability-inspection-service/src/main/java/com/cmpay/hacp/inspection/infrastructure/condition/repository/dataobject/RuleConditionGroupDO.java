package com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseInsertDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_rule_condition_group")
public class RuleConditionGroupDO extends BaseInsertDO {

    private String pluginId;

    private String ruleId;

    private String conditionGroupId;

}
