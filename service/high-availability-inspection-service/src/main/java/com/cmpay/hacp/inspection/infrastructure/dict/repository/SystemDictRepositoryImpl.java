package com.cmpay.hacp.inspection.infrastructure.dict.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.dict.repository.SystemDictRepository;
import com.cmpay.hacp.inspection.infrastructure.dict.dataobject.SystemDictPlusDO;
import com.cmpay.hacp.inspection.infrastructure.dict.repository.mapper.SystemDictMapper;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public class SystemDictRepositoryImpl extends CrudRepository<SystemDictMapper, SystemDictPlusDO> implements SystemDictRepository {
    @Override
    public String queryValueByTypeAndLabel(String dictTypeInspectionCipher, String inspectionDataPublic) {
        SystemDictPlusDO sm2PublicKey = this.getOne(Wrappers.lambdaQuery(SystemDictPlusDO.class)
                .eq(SystemDictPlusDO::getType, dictTypeInspectionCipher)
                .eq(SystemDictPlusDO::getLabel, inspectionDataPublic));
        return Optional.ofNullable(sm2PublicKey).map(SystemDictPlusDO::getValue).orElse(null);
    }
}
