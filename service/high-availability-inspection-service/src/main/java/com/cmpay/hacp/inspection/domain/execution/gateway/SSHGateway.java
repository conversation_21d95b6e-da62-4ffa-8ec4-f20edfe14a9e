package com.cmpay.hacp.inspection.domain.execution.gateway;

import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionResult;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.execution.model.SshConnectionConfig;

public interface SSHGateway {
    ScriptExecutionResult executeScript(SshConnectionConfig connectionConfig,
                                        ScriptExecutionRequest request);

    ScriptExecutionResult executeCommand(SshConnectionConfig connectionConfig,
                                         String command);
}
