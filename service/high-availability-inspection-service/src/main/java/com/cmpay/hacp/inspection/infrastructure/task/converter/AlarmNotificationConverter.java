package com.cmpay.hacp.inspection.infrastructure.task.converter;

import com.cmpay.hacp.inspection.domain.model.task.AlarmNotification;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskAlarmDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface AlarmNotificationConverter {

    @IgnoreAuditFields
    TaskAlarmDO toTaskAlarmDO(String taskId,AlarmNotification alarmNotification);


    AlarmNotification toAlarmNotification(TaskAlarmDO taskAlarmDO);

}
