package com.cmpay.hacp.inspection.infrastructure.database.dataobject;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则插件参数配置表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_rule_plugin_param")
public class RulePluginParamDO extends BaseDO {

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 插件参数ID
     */
    private String pluginParamName;

    /**
     * 插件参数值
     */
    private String pluginParamValue;

    /**
     * 是否加密
     */
    private Boolean isEncrypted;

}
