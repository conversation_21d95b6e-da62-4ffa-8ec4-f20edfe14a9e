package com.cmpay.hacp.inspection.infrastructure.rule.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.application.service.InspectionCipherService;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginParamRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RulePluginParamDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.RulePluginParamMapper;
import com.cmpay.hacp.inspection.infrastructure.rule.converter.RulePluginParamConverter;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class RulePluginParamRepositoryImpl extends CrudRepository<RulePluginParamMapper, RulePluginParamDO> implements RulePluginParamRepository {


    private final RulePluginParamConverter rulePluginParamConverter;
    private final InspectionCipherService inspectionCipherService;

    @Autowired
    @Lazy
    private RulePluginParamRepositoryImpl baseRepository;

    @Override
    public List<RulePluginParam> findByRuleId(String ruleId) {
        List<RulePluginParamDO> rulePlugins =  this.list(Wrappers.lambdaQuery(RulePluginParamDO.class).eq(RulePluginParamDO::getRuleId, ruleId));
        List<RulePluginParam> result = rulePluginParamConverter.toRulePluginParamList(rulePlugins);
        return Optional.ofNullable(result).map(m-> m.stream().peek(pluginParam -> {
                    if (JudgeUtils.isTrue(pluginParam.getIsEncrypted(), false)) {
                        pluginParam.setPluginParamValue(inspectionCipherService.dataDecrypt(pluginParam.getPluginParamValue()));
                    }
                }).collect(Collectors.toList())).orElse(null);
    }

    @Override
    public List<RulePluginParam> findByRuleIds(List<String> ruleIds) {
        List<RulePluginParamDO> list = this.list(Wrappers.lambdaQuery(RulePluginParamDO.class)
                .in(RulePluginParamDO::getRuleId, ruleIds));
        return rulePluginParamConverter.toRulePluginParamList(list);
    }

    @Override
    public void removeByRuleIdAndPluginId(String ruleId, String pluginId) {
        baseRepository.remove(Wrappers.lambdaUpdate(RulePluginParamDO.class)
                .eq(RulePluginParamDO::getRuleId, ruleId)
                .eq(RulePluginParamDO::getPluginId, pluginId));
    }

    @Override
    public void saveBatch(List<RulePluginParam> pluginParams, String ruleId, String sm4Key) {
        if(JudgeUtils.isEmpty(pluginParams)){
            return;
        }
        List<RulePluginParamDO> results = pluginParams
                .stream()
                .peek(scriptResult -> {
                    if (JudgeUtils.isTrue(scriptResult.getIsEncrypted(), false)) {
                        scriptResult.setPluginParamValue(inspectionCipherService.decryptSysDataBySm4AndSm2AndEncrypt(sm4Key, scriptResult.getPluginParamValue()));
                    }
                })
                .map(scriptResult -> rulePluginParamConverter.toRulePluginParamDO(scriptResult, ruleId))
                .collect(Collectors.toList());
        baseRepository.saveBatch(results, 20);
    }

    @Override
    public void removeByRuleId(String ruleId) {
        baseRepository.remove(Wrappers.lambdaQuery(RulePluginParamDO.class)
                .eq(RulePluginParamDO::getRuleId, ruleId));
    }
}
