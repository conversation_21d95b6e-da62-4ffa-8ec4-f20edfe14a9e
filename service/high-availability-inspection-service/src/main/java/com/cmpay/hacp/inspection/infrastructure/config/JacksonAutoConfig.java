package com.cmpay.hacp.inspection.infrastructure.config;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cmpay.hacp.system.autoconfigure.HacpJacksonAutoConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Configuration;

@Configuration
@AutoConfigureAfter(HacpJacksonAutoConfiguration.class )
public class JacksonAutoConfig {

    public JacksonAutoConfig(@Qualifier("hacpObjectMapper")ObjectMapper objectMapper) {
        JacksonTypeHandler.setObjectMapper(objectMapper);
    }

}
