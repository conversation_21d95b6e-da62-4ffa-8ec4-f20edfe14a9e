package com.cmpay.hacp.inspection.infrastructure.execution.gateway;

import com.cmpay.hacp.inspection.domain.execution.gateway.K8sGateway;
import com.cmpay.hacp.inspection.domain.execution.model.K8sConnectionConfig;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionResult;
import com.cmpay.hacp.inspection.infrastructure.execution.config.K8sProperties;
import io.kubernetes.client.Exec;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.util.Config;
import io.kubernetes.client.util.Streams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * K8s网关实现类
 * 基于Kubernetes Java Client执行脚本和命令
 * RBAC 权限：确保有 pods/exec 权限
 */
@Slf4j
@EnableConfigurationProperties(K8sProperties.class)
@Component
public class K8sGatewayImpl implements K8sGateway, InitializingBean, DisposableBean {

    private final K8sProperties k8sProperties;
    private ApiClient apiClient;
    private CoreV1Api coreV1Api;

    public K8sGatewayImpl(K8sProperties k8sProperties) {
        this.k8sProperties = k8sProperties;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化Kubernetes客户端 (适配 22.0.1-legacy)
        try {
            if (isInCluster()) {
                log.info("Running in Kubernetes cluster, using ServiceAccount");
                // 使用默认配置（集群内或 ~/.kube/config）
                apiClient = Config.defaultClient();
            } else {
                log.info("Running outside cluster, using kubeconfig: {}", k8sProperties.getDefaultKubeconfigPath());
                // 从指定的 kubeconfig 文件加载配置
                String kubeconfigPath = k8sProperties.getDefaultKubeconfigPath();

                if (!StringUtils.hasText(kubeconfigPath)) {
                    log.warn("No kubeconfig path configured, K8s functionality will be disabled");
                    return;
                }

                if (kubeconfigPath.startsWith("~")) {
                    kubeconfigPath = kubeconfigPath.replace("~", System.getProperty("user.home"));
                }
                // 标准化路径，使其符合当前操作系统
                Path normalizedPath = Paths.get(kubeconfigPath).normalize();
                if (!Files.exists(normalizedPath)) {
                    log.warn("Kubeconfig file not found at: {}, K8s functionality will be disabled", normalizedPath);
                    return;
                }
                kubeconfigPath = normalizedPath.toString();
                apiClient = Config.fromConfig(kubeconfigPath);
            }


            // 设置超时 (22.0.1-legacy 版本的超时设置)
            apiClient.setConnectTimeout(k8sProperties.getConnectionTimeout());
            apiClient.setReadTimeout(k8sProperties.getExecutionTimeout());
            apiClient.setWriteTimeout(k8sProperties.getExecutionTimeout());

            // 设置为默认客户端
            Configuration.setDefaultApiClient(apiClient);

            // 创建API实例
            coreV1Api = new CoreV1Api(apiClient);

            log.info("Kubernetes client initialized successfully");
        } catch (Exception e) {
            log.warn("Failed to initialize Kubernetes client: {}, K8s functionality will be disabled", e.getMessage());
            // 不抛出异常，让应用继续启动
            apiClient = null;
            coreV1Api = null;
        }
    }

    private boolean isInCluster() {
        return System.getenv("KUBERNETES_SERVICE_HOST") != null &&
                Files.exists(Paths.get("/var/run/secrets/kubernetes.io/serviceaccount/token"));
    }

    /**
     * 执行需要K8s客户端的操作
     *
     * @param operation 具体的业务操作
     * @return 执行结果
     */
    private ScriptExecutionResult executeWithK8sClient(Supplier<ScriptExecutionResult> operation) {
        if (coreV1Api == null) {
            log.warn("K8s client not initialized, cannot execute operation");
            return ScriptExecutionResult.builder()
                    .success(false)
                    .errorMessage("K8s client not available")
                    .build();
        }

        try {
            return operation.get();
        } catch (Exception e) {
            log.error("Error executing K8s operation", e);
            return ScriptExecutionResult.builder()
                    .success(false)
                    .errorMessage("K8s operation failed: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public void destroy() {
        // 关闭客户端资源
        if (apiClient != null) {
            try {
                apiClient.getHttpClient().connectionPool().evictAll();
                log.info("Kubernetes client resources released");
            } catch (Exception e) {
                log.warn("Error while releasing Kubernetes client resources", e);
            }
        }
    }

    @Override
    public ScriptExecutionResult executeScript(K8sConnectionConfig connectionConfig,
                                               ScriptExecutionRequest request) {
        return executeWithK8sClient(() -> {
            long startTime = System.currentTimeMillis();

            try {
                // 验证连接配置
                validateConnectionConfig(connectionConfig);

                // 验证Pod是否存在
                validatePodExists(connectionConfig);

                // 构建脚本执行命令
                String scriptCommand = buildScriptCommand(request);

                // 执行脚本
                return executeCommandInPod(connectionConfig, scriptCommand, startTime);

            } catch (Exception e) {
                log.error("Script execution failed", e);
                return ScriptExecutionResult.builder()
                        .success(false)
                        .errorMessage("Execution exception: " + e.getMessage())
                        .executionTime(System.currentTimeMillis() - startTime)
                        .build();
            }
        });
    }

    @Override
    public ScriptExecutionResult executeCommand(K8sConnectionConfig connectionConfig,
                                                String command) {
        return executeWithK8sClient(() -> {
            long startTime = System.currentTimeMillis();

            try {
                validateConnectionConfig(connectionConfig);
                validatePodExists(connectionConfig);
                return executeCommandInPod(connectionConfig, command, startTime);
            } catch (Exception e) {
                log.error("Command execution failed", e);
                return ScriptExecutionResult.builder()
                        .success(false)
                        .errorMessage("Execution exception: " + e.getMessage())
                        .executionTime(System.currentTimeMillis() - startTime)
                        .build();
            }
        });
    }

    /**
     * 验证连接配置
     */
    private void validateConnectionConfig(K8sConnectionConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("K8s connection config cannot be null");
        }
        if (!StringUtils.hasText(config.getNamespace())) {
            throw new IllegalArgumentException("Namespace cannot be empty");
        }
        if (!StringUtils.hasText(config.getPodName())) {
            throw new IllegalArgumentException("Pod name cannot be empty");
        }
    }

    /**
     * 验证Pod是否存在 (适配 22.0.1-legacy)
     */
    private void validatePodExists(K8sConnectionConfig config) throws ApiException {
        try {
            // 22.0.1-legacy 版本的 API 调用
            V1Pod pod = coreV1Api.readNamespacedPod(
                    config.getPodName(),
                    config.getNamespace(),
                    null  // pretty
            );

            if (pod == null || pod.getStatus() == null) {
                throw new RuntimeException("Pod not found: " + config.getNamespace() + "/" + config.getPodName());
            }

            String phase = pod.getStatus().getPhase();
            if (!"Running".equals(phase)) {
                throw new RuntimeException("Pod is not running. Current phase: " + phase);
            }

            // 检查容器状态（如果指定了容器）
            if (StringUtils.hasText(config.getContainer()) && pod.getStatus().getContainerStatuses() != null) {
                boolean containerFound = pod.getStatus().getContainerStatuses().stream()
                        .anyMatch(status -> config.getContainer().equals(status.getName()) &&
                                Boolean.TRUE.equals(status.getReady()));

                if (!containerFound) {
                    throw new RuntimeException("Container '" + config.getContainer() + "' not found or not ready in pod: "
                            + config.getNamespace() + "/" + config.getPodName());
                }
            }

            log.debug("Pod validation successful: {}/{} (K8s 1.31)", config.getNamespace(), config.getPodName());
        } catch (ApiException e) {
            log.error("Failed to validate pod: {}/{} - HTTP {}: {}",
                    config.getNamespace(), config.getPodName(), e.getCode(), e.getResponseBody(), e);
            throw e;
        }
    }

    /**
     * 构建脚本执行命令
     */
    private String buildScriptCommand(ScriptExecutionRequest request) {
        StringBuilder command = new StringBuilder();

        // 设置环境变量
        if (request.getEnvironment() != null && !request.getEnvironment().isEmpty()) {
            for (Map.Entry<String, String> entry : request.getEnvironment().entrySet()) {
                command.append("export ").append(entry.getKey()).append("='").append(entry.getValue()).append("'; ");
            }
        }

        // 切换工作目录
        if (StringUtils.hasText(request.getWorkingDirectory())) {
            command.append("cd ").append(request.getWorkingDirectory()).append("; ");
        }

        // 创建临时脚本文件并执行
        String tempFileName = "/tmp/" + k8sProperties.getTempFilePrefix() + UUID.randomUUID().toString() + request.getScriptType().getExtension();

        // 写入脚本内容到临时文件
        command.append("cat > ").append(tempFileName).append(" << 'EOF'\n");
        command.append(request.getScriptContent()).append("\n");
        command.append("EOF\n");

        // 设置执行权限并执行
        command.append("chmod +x ").append(tempFileName).append("; ");
        command.append(request.getScriptType().getInterpreter()).append(" ").append(tempFileName);

        // 清理临时文件
        if (k8sProperties.isCleanupTempFiles()) {
            command.append("; rm -f ").append(tempFileName);
        }

        return command.toString();
    }

    /**
     * 在Pod中执行命令（使用Kubernetes Java Client 22.0.1-legacy）
     */
    private ScriptExecutionResult executeCommandInPod(K8sConnectionConfig config,
                                                      String command, long startTime) throws Exception {
        try {
            // 创建 Exec 实例 (22.0.1-legacy 版本)
            Exec exec = new Exec(apiClient);

            // 构建执行命令
            String[] execCommand = {"sh", "-c", command};

            // 创建输出流
            ByteArrayOutputStream stdout = new ByteArrayOutputStream();
            ByteArrayOutputStream stderr = new ByteArrayOutputStream();

            log.debug("Executing command in pod {}/{} (K8s 1.31): {}",
                    config.getNamespace(), config.getPodName(),
                    command.length() > 100 ? command.substring(0, 100) + "..." : command);

            // 执行命令 (22.0.1-legacy API)
            Process process = exec.exec(
                    config.getNamespace(),
                    config.getPodName(),
                    execCommand,
                    StringUtils.hasText(config.getContainer()) ? config.getContainer() : null,
                    false,  // stdin
                    false   // tty
            );

            // 复制输出流 (22.0.1-legacy 兼容)
            Thread stdoutThread = new Thread(() -> {
                try {
                    Streams.copy(process.getInputStream(), stdout);
                } catch (IOException e) {
                    log.warn("Error reading stdout from pod {}/{}: {}",
                            config.getNamespace(), config.getPodName(), e.getMessage());
                }
            }, "k8s-stdout-reader");

            Thread stderrThread = new Thread(() -> {
                try {
                    Streams.copy(process.getErrorStream(), stderr);
                } catch (IOException e) {
                    log.warn("Error reading stderr from pod {}/{}: {}",
                            config.getNamespace(), config.getPodName(), e.getMessage());
                }
            }, "k8s-stderr-reader");

            stdoutThread.start();
            stderrThread.start();

            // 等待执行完成
            boolean finished = process.waitFor(k8sProperties.getExecutionTimeout(), TimeUnit.MILLISECONDS);

            if (!finished) {
                process.destroyForcibly();
                log.warn("Command execution timed out in pod {}/{} after {}ms",
                        config.getNamespace(), config.getPodName(), k8sProperties.getExecutionTimeout());
                return ScriptExecutionResult.builder()
                        .success(false)
                        .errorMessage("Command execution timed out after " + k8sProperties.getExecutionTimeout() + "ms")
                        .executionTime(System.currentTimeMillis() - startTime)
                        .build();
            }

            // 等待输出流读取完成
            stdoutThread.join(2000);  // 增加等待时间
            stderrThread.join(2000);

            int exitCode = process.exitValue();
            String stdoutStr = stdout.toString(StandardCharsets.UTF_8.name());
            String stderrStr = stderr.toString(StandardCharsets.UTF_8.name());

            log.debug("Command execution completed in pod {}/{}. Exit code: {}, stdout length: {}, stderr length: {}",
                    config.getNamespace(), config.getPodName(), exitCode, stdoutStr.length(), stderrStr.length());

            return ScriptExecutionResult.builder()
                    .exitCode(exitCode)
                    .stdout(stdoutStr)
                    .stderr(stderrStr)
                    .success(exitCode == 0)
                    .executionTime(System.currentTimeMillis() - startTime)
                    .errorMessage(exitCode != 0 ? stderrStr : null)
                    .build();

        } catch (ApiException e) {
            log.error("Kubernetes API error executing command in pod {}/{}: HTTP {}: {}",
                    config.getNamespace(), config.getPodName(), e.getCode(), e.getResponseBody(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to execute command in pod {}/{}: {}",
                    config.getNamespace(), config.getPodName(), e.getMessage(), e);
            throw e;
        }
    }


}
