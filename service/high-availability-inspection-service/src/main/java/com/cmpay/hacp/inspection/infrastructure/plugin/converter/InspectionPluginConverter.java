package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.*;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginDO;
import org.mapstruct.Mapper;

/**
 * 巡检插件对象转换器
 * 用于领域对象和实体对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface InspectionPluginConverter {

    @IgnoreAuditFields
    PluginDO toInspectionPluginDO(InspectionPlugin inspectionPlugin);

}
