package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.inspection.domain.model.task.ResourceExecutionStats;
import com.cmpay.hacp.inspection.domain.task.model.TaskExecution;

import java.util.Map;

public interface InspectionTaskMonitoringService {
    String initializeTaskExecution(String taskId, String taskName, TriggerMode triggerMode, Integer ruleTotal);

    void markTaskAsRunning(String executionId);

    void markTaskAsCompleted(String executionId, boolean success);

    void recordRuleExecutionResult(String taskId, String executionId, String ruleId, String pluginId, InspectionResult result);

    /**
     * 记录规则执行结果（包含资源信息）
     * @param taskId 任务ID
     * @param ruleId 规则ID
     * @param pluginId 插件ID
     * @param result 执行结果
     * @param resource 资源
     * @param resourceName 资源名称
     */
    void recordRuleExecutionResult(String taskId, String executionId, String ruleId, String pluginId, InspectionResult result,
                                   String resource, String resourceName);

    TaskExecution getTaskExecutionStatus(String executionId);

    /**
     * 计算资源覆盖率
     * @param taskId 任务ID
     * @return 资源覆盖率(%)
     */
    Integer calculateResourceCoverageRate(String taskId);

    /**
     * 获取资源执行统计信息
     * @param taskId 任务ID
     * @return 资源执行统计 (resourceId -> success/fail count)
     */
    Map<String, ResourceExecutionStats> getResourceExecutionStatistics(String taskId);
}
