package com.cmpay.hacp.inspection.infrastructure.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.listeners.JobListenerSupport;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TaskExecutionListener extends JobListenerSupport {
    @Override
    public String getName() {
        return "InspectionTaskExecutionListener";
    }

    @Override
    public void jobToBeExecuted(JobExecutionContext context) {
        log.info("Task about to execute: {}", context.getJobDetail().getKey());
    }

    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
        if (jobException == null) {
            log.info("Task execution completed: {}", context.getJobDetail().getKey());
        } else {
            log.error("Task execution exception: {}", context.getJobDetail().getKey(), jobException);
        }
    }
}
