package com.cmpay.hacp.inspection.infrastructure.task.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.task.model.Resource;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRuleResourceRepository;
import com.cmpay.hacp.inspection.infrastructure.task.converter.TaskRuleResourceConverter;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskRuleResourceDO;
import com.cmpay.hacp.inspection.infrastructure.task.repository.mapper.TaskRuleResourceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class TaskRuleResourceRepositoryImpl extends CrudRepository<TaskRuleResourceMapper, TaskRuleResourceDO> implements TaskRuleResourceRepository {
    private final TaskRuleResourceConverter taskRuleResourceConverter;

    @Autowired
    @Lazy
    private TaskRuleResourceRepositoryImpl taskRuleResourceRepository;


    @Override
    public List<Resource> list(String taskId, String ruleId) {
        return taskRuleResourceConverter.toResourceList(this.list(
                Wrappers.lambdaQuery(TaskRuleResourceDO.class)
                        .eq(TaskRuleResourceDO::getTaskId, taskId)
                        .eq(TaskRuleResourceDO::getRuleId, ruleId)));
    }

    @Override
    public List<Resource> listByTaskId(String taskId) {
        return taskRuleResourceConverter.toResourceList(this.list(
                Wrappers.lambdaQuery(TaskRuleResourceDO.class)
                        .eq(TaskRuleResourceDO::getTaskId, taskId)));
    }

    @Override
    public void saveBatch(InspectionTask inspectionTask) {
        List<TaskRuleResourceDO> taskRuleResourceDOList = inspectionTask.getTaskRuleExecutions().stream()
                .flatMap(ruleExecution ->
                        ruleExecution.getResourceList().stream()
                                .map(resource -> {
                                    TaskRuleResourceDO ruleResourceDO =  taskRuleResourceConverter.toTaskRuleResourceDO(resource);
                                    ruleResourceDO.setTaskId(inspectionTask.getTaskId());
                                    ruleResourceDO.setRuleId(ruleExecution.getRuleId());
                                    return ruleResourceDO;
                                })
                )
                .collect(Collectors.toList());
        taskRuleResourceRepository.saveBatch(taskRuleResourceDOList, 20);
    }

    @Override
    public void deleteByTaskId(String taskId) {
        this.remove(Wrappers.lambdaUpdate(TaskRuleResourceDO.class)
                .eq(TaskRuleResourceDO::getTaskId, taskId));
    }
}
