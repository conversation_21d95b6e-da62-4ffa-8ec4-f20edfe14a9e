package com.cmpay.hacp.inspection.infrastructure.task.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.model.task.ScheduleConfig;
import com.cmpay.hacp.inspection.domain.task.repository.ScheduleConfigRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskScheduleConfigDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.ScheduleConfigMapper;
import com.cmpay.hacp.inspection.infrastructure.task.converter.ScheduleConfigConverter;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
@RequiredArgsConstructor
public class ScheduleConfigRepositoryImpl extends CrudRepository<ScheduleConfigMapper, TaskScheduleConfigDO> implements ScheduleConfigRepository {

    private final ScheduleConfigConverter scheduleConfigConverter;

    @Override
    public void updateByTaskId(String taskId, ScheduleConfig scheduleConfig) {
        this.removeByTaskId(taskId);
        if(JudgeUtils.isNull(scheduleConfig)){
            return;
        }
        TaskScheduleConfigDO taskScheduleConfigDO = scheduleConfigConverter.toScheduleConfigDO(taskId,scheduleConfig);
        this.save(taskScheduleConfigDO);
    }

    @Override
    public void saveByTaskId(String taskId, ScheduleConfig scheduleConfig) {
        if(JudgeUtils.isNull(scheduleConfig)){
            return;
        }
        TaskScheduleConfigDO taskScheduleConfigDO = scheduleConfigConverter.toScheduleConfigDO(taskId,scheduleConfig);
        this.save(taskScheduleConfigDO);
    }

    @Override
    public void removeByTaskId(String taskId) {
        this.remove(Wrappers.lambdaUpdate(TaskScheduleConfigDO.class)
                .eq(TaskScheduleConfigDO::getTaskId, taskId));
    }

    @Override
    public ScheduleConfig queryByTaskId(String taskId) {
        TaskScheduleConfigDO taskScheduleConfigDO = this.getOne(Wrappers.lambdaQuery(TaskScheduleConfigDO.class)
                .eq(TaskScheduleConfigDO::getTaskId, taskId));
        return scheduleConfigConverter.toScheduleConfig(taskScheduleConfigDO);
    }

    @Override
    public List<ScheduleConfig> listByTaskIds(List<String> taskIds) {
        if(JudgeUtils.isEmpty(taskIds)){
            return Collections.emptyList();
        }
        List<TaskScheduleConfigDO> list = this.list(Wrappers.lambdaQuery(TaskScheduleConfigDO.class)
                .in(TaskScheduleConfigDO::getTaskId, taskIds));

        return scheduleConfigConverter.toScheduleConfigList(list);
    }
}
