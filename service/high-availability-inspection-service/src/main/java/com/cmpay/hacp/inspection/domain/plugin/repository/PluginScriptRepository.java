package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginConfigScript;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScript;

import javax.validation.constraints.NotNull;

public interface PluginScriptRepository {
    PluginScript getByPluginId(@NotNull String pluginId);

    void save(PluginConfigScript pluginConfigScript, @NotNull String pluginId);

    boolean updateByPluginId(PluginScript pluginScript);

    void removeByPluginId(@NotNull String pluginId);
}
