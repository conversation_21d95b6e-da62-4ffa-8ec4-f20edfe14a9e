package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.FireflyIndicatorDefinitionDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface IndicatorDefinitionConverter {
    List<IndicatorDefinition> toIndicatorDefinitionList(List<FireflyIndicatorDefinitionDO> indicatorDefinitionDOList);

    @Mapping(target = "inputParams", ignore = true)
    IndicatorDefinition toIndicatorDefinition(FireflyIndicatorDefinitionDO indicatorDefinitionDO);
}
