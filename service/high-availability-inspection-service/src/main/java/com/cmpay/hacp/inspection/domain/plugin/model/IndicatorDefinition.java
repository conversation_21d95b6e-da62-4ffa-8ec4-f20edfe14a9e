package com.cmpay.hacp.inspection.domain.plugin.model;

import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 指标定义领域对象
 */
@Data
@Builder
public class IndicatorDefinition {
    
    /**
     * 指标ID
     */
    private String indicatorId;
    
    /**
     * 指标名
     */
    private String indicatorName;

    /**
     * 指标类型：mid-中间件，host-主机，container-容器
     */
    private IndicatorType indicatorType;
    
    /**
     * 指标描述
     */
    private String description;
    
    /**
     * 输入参数列表
     */
    private List<IndicatorParam> inputParams;
    
    /**
     * 指标参数
     */
    @Data
    @Builder
    public static class IndicatorParam {
        
        /**
         * 参数顺序
         */
        private Integer paramOrder;
        
        /**
         * 参数名称
         */
        private String paramName;
        
        /**
         * 参数代码
         */
        private String paramCode;
        
        /**
         * 参数描述
         */
        private String description;
    }
}
