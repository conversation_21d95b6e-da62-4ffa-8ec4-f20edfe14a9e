package com.cmpay.hacp.inspection.domain.model.firefly;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 指标查询响应对象
 */
@Data
@Builder
public class IndicatorQueryResponse {

    /**
     * 返回码
     */
    private String msgCd;

    /**
     * 返回信息
     */
    private String msgInfo;

    /**
     * 数据列表
     */
    private List<IndicatorData> dataList;

    /**
     * 总记录数
     */
    private Long totalCount;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 检查响应是否成功
     *
     * @return true如果成功，否则false
     */
    public boolean isSuccess() {
        return "FFM00000".equals(msgCd);
    }

    /**
     * 指标数据项
     */
    @Data
    @Builder
    public static class IndicatorData {

        /**
         * 指标名称
         */
        private String indicatorName;

        /**
         * 时间戳
         */
        private LocalDateTime timestamp;

        /**
         * 指标值
         */
        private String value;

        /**
         * 机房区域
         */
        private String zone;

        /**
         * 特殊字段1
         */
        private String one;

        /**
         * 特殊字段2
         */
        private String two;

        /**
         * 特殊字段3
         */
        private String three;

        /**
         * 特殊字段4
         */
        private String four;

        /**
         * 特殊字段5
         */
        private String five;

        /**
         * 特殊字段6
         */
        private String six;

        /**
         * 特殊字段7
         */
        private String seven;

        /**
         * 特殊字段8
         */
        private String eight;

        /**
         * 特殊字段9
         */
        private String nine;

        /**
         * 特殊字段10
         */
        private String ten;
    }
}
