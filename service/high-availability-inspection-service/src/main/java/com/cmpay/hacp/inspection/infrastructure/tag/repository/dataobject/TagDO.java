package com.cmpay.hacp.inspection.infrastructure.tag.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标签定义实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_tag")
public class TagDO extends BaseDO {
    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签描述
     */
    private String description;
}
