package com.cmpay.hacp.inspection.infrastructure.task.converter;

import com.cmpay.hacp.inspection.domain.model.task.CronScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.FixedTimeScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.IntervalScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.ScheduleConfig;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskScheduleConfigDO;
import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ScheduleConfigConverter {

    @IgnoreAuditFields
    @Mapping(target = "scheduleType", ignore = true)
    @Mapping(target = "cronExpression", ignore = true)
    @Mapping(target = "intervalValue", ignore = true)
    @Mapping(target = "intervalUnit", ignore = true)
    @Mapping(target = "executionDateTime", ignore = true)
    @Mapping(target = "taskId", source = "taskId")
    TaskScheduleConfigDO toScheduleConfigDO(String taskId,ScheduleConfig config);

    @AfterMapping
    default void afterToScheduleConfigDO(ScheduleConfig config, @MappingTarget TaskScheduleConfigDO configDO) {
        if (config == null) return;

        configDO.setScheduleType(config.getType());
        configDO.setEnabled(config.isEnabled());

        if (config instanceof CronScheduleConfig) {
            CronScheduleConfig cronConfig = (CronScheduleConfig) config;
            configDO.setCronExpression(cronConfig.getCronExpression());
            configDO.setIntervalValue(null);
            configDO.setIntervalUnit(null);
            configDO.setExecutionDateTime(null);
        }
        else if (config instanceof IntervalScheduleConfig) {
            IntervalScheduleConfig intervalConfig = (IntervalScheduleConfig) config;
            configDO.setCronExpression(null);
            configDO.setIntervalValue(intervalConfig.getIntervalValue());
            configDO.setIntervalUnit(intervalConfig.getIntervalUnit());
            configDO.setExecutionDateTime(null);
        }
        else if (config instanceof FixedTimeScheduleConfig) {
            FixedTimeScheduleConfig fixedConfig = (FixedTimeScheduleConfig) config;
            configDO.setCronExpression(null);
            configDO.setIntervalValue(null);
            configDO.setIntervalUnit(null);
            configDO.setExecutionDateTime(fixedConfig.getExecutionDateTime());
        }
    }

    ScheduleConfig toScheduleConfig(TaskScheduleConfigDO configDO);


    List<ScheduleConfig> toScheduleConfigList(List<TaskScheduleConfigDO> list);

    @ObjectFactory
    default ScheduleConfig createScheduleConfig(TaskScheduleConfigDO configDO) {
        if (configDO == null) return null;

        ScheduleConfig result;
        switch (configDO.getScheduleType()) {
            case CRON_EXPRESSION:
                result = ScheduleConfig.createCronSchedule(configDO.getCronExpression());
                break;
            case FIXED_INTERVAL:
                result = ScheduleConfig.createIntervalSchedule(
                        configDO.getIntervalValue(),
                        configDO.getIntervalUnit()
                );
                break;
            case FIXED_TIME:
                result = ScheduleConfig.createFixedTimeSchedule(
                        configDO.getExecutionDateTime()
                );
                break;
            default:
                throw new IllegalArgumentException("Unknown schedule type: " + configDO.getScheduleType());
        }

        result.setEnabled(configDO.isEnabled());
        return result;
    }
}
