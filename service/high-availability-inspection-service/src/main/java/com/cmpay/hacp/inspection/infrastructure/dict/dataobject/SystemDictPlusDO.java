package com.cmpay.hacp.inspection.infrastructure.dict.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("sys_dict")
public class SystemDictPlusDO {
    /**
     * @Fields dictId 编号
     */
    private String dictId;
    /**
     * @Fields value 数据值
     */
    private String value;
    /**
     * @Fields label 标签名
     */
    private String label;
    /**
     * @Fields type 类型
     */
    private String type;
    /**
     * @Fields description 描述
     */
    private String description;
    /**
     * @Fields sort 排序（升序）
     */
    private Long sort;
    /**
     * @Fields parentId 父级编号
     */
    private String parentId;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

}
