package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

public interface PluginRepository {
    /**
     * 根据pluginId获取插件信息
     *
     * @param pluginId 插件ID
     * @return 插件信息
     */
    InspectionPlugin getByPluginId(@NotNull String pluginId);

    InspectionPlugin getByPluginName(@NotNull String pluginName);

    InspectionPlugin save(InspectionPlugin inspectionPlugin);

    boolean existingPluginName(@NotNull String pluginName, @NotNull String excludePluginId);

    boolean updatePlugin(InspectionPlugin inspectionPlugin);

    void removeByPluginId(@NotNull String pluginId);

    IPage<InspectionPlugin> page(IPage<?> page, InspectionPlugin inspectionPlugin, Set<String> pluginIdSet);

    List<InspectionPlugin> list(InspectionPlugin inspectionPlugin);
}
