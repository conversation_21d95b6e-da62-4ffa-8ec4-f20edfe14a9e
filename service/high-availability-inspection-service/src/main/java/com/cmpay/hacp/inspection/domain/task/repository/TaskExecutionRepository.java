package com.cmpay.hacp.inspection.domain.task.repository;

import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface TaskExecutionRepository {
    List<String> listTaskIdByTaskStatus(@NotNull ExecutionStatus executionStatus);

    ExecutionStatus queryStatusByTaskId(@NotNull String taskId);

    /**
     * 保存任务执行记录
     *
     * @param taskExecution 任务执行记录
     */
    void save(@NotNull TaskExecutionDO taskExecution);

    /**
     * 根据ID更新任务执行记录
     *
     * @param taskExecution 任务执行记录
     */
    void updateById(@NotNull TaskExecutionDO taskExecution);

    /**
     * 根据执行ID查询任务执行记录
     *
     * @param executionId 执行ID
     * @return 任务执行记录
     */
    TaskExecutionDO getByExecutionId(@NotNull String executionId);

    /**
     * 根据任务ID查询最新的任务执行记录
     *
     * @param taskId 任务ID
     * @return 最新的任务执行记录
     */
    TaskExecutionDO getLatestByTaskId(@NotNull String taskId);
}
