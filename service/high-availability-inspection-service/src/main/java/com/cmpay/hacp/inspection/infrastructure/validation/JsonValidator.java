package com.cmpay.hacp.inspection.infrastructure.validation;

import com.fasterxml.jackson.databind.ObjectMapper;

public class JsonValidator {
    private static final ObjectMapper mapper = new ObjectMapper();

    public static boolean isValidJson(String json) {
        try {
            mapper.readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
