package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 参数类型
 */
@Getter
public enum ParamType {
    TEXT(1, "文本"),
    NUMBER(2, "数字"),
    EMAIL(3, "邮箱"),
    URL(4, "URL"),
    REGEX(5, "正则表达式");

    @JsonValue
    private final Integer code;
    private final String desc;

    ParamType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
