package com.cmpay.hacp.inspection.domain.task.repository;

import com.cmpay.hacp.inspection.domain.task.model.TaskRuleMapping;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface TaskRuleRepository {
    List<TaskRuleMapping> list(@NotNull String taskId);

    List<TaskRuleMapping> listByTaskIds(@NotNull List<String> ruleIds);

    void saveBatch(List<TaskRuleMapping> taskRuleMappingList);

    boolean removeByTaskId(@NotNull String taskId);
}
