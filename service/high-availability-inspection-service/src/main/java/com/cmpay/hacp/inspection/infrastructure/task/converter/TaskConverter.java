package com.cmpay.hacp.inspection.infrastructure.task.converter;

import com.cmpay.hacp.inspection.domain.task.model.Task;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface TaskConverter {
    Task toTask(TaskDO one);

    @IgnoreAuditFields
    TaskDO toTaskDO(Task task);
}
