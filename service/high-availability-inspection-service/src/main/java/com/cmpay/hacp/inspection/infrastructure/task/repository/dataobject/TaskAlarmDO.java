package com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cmpay.hacp.inspection.domain.model.enums.AlertCondition;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 规则资源关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "inspection_task_alarm",autoResultMap = true)
public class TaskAlarmDO extends BaseDO {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 通知条件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<AlertCondition> alertConditions;

    /**
     * 通知邮箱
     */
    private String email;
}
