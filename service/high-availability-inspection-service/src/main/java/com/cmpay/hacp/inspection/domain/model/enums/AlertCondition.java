package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Getter
public enum AlertCondition {


    SUCCESS(1, "成功时通知"),
    WARNING(2, "警告时通知"),
    FAIL(3, "失败时通知");

    @JsonValue
    private final Integer code;
    private final String desc;

    AlertCondition(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    private static final Map<Integer, AlertCondition> ENUM_MAP = Arrays.stream(AlertCondition.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static AlertCondition getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
