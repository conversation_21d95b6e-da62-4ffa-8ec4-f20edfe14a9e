package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 字段类型
 */
@Getter
public enum PluginOutputFieldType {
    NUMERIC(1, "数值型字段"),
    STRING(2, "字符串型字段"),
    BOOLEAN(3, "布尔型字段");

    @JsonValue
    private final Integer code;
    private final String desc;

    PluginOutputFieldType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
