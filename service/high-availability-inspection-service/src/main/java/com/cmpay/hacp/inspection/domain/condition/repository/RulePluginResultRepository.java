package com.cmpay.hacp.inspection.domain.condition.repository;

import com.cmpay.hacp.inspection.domain.condition.model.RulePluginResult;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface RulePluginResultRepository {

    List<RulePluginResult> findByRuleId(@NotNull String ruleId);

    RulePluginResult findByRuleId(@NotNull String ruleId,String pluginId);

    List<RulePluginResult> findByRuleIds(List<String> ruleIds);

    List<RulePluginResult> findByRuleIds(List<String> ruleIds,List<String> pluginIds);

    void removeByRuleId(@NotNull String ruleId);

    void saveBatch(List<RulePluginResult> pluginResults, @NotNull String ruleId);
}
