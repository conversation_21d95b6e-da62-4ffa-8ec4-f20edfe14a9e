package com.cmpay.hacp.inspection.domain.execution.service;

import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 规则执行领域服务 - 核心协调器
 */
@Service
public class RuleExecutionDomainService {
    private final RuleExecutionStrategyFactory strategyFactory;

    public RuleExecutionDomainService(RuleExecutionStrategyFactory strategyFactory) {
        this.strategyFactory = strategyFactory;
    }

    public void executeRule(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount) {
        // 根据资源类型选择策略
        RuleExecutionStrategy strategy = strategyFactory.getStrategy(ruleExecution.getResourceType());

        // 执行规则
        strategy.execute(ruleExecution, successCount, failCount);
    }
}
