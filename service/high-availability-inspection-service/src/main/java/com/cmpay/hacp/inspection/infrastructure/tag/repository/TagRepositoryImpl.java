package com.cmpay.hacp.inspection.infrastructure.tag.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginTagMapping;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginTagRepository;
import com.cmpay.hacp.inspection.domain.tag.model.Tag;
import com.cmpay.hacp.inspection.domain.tag.repository.RuleTagRepository;
import com.cmpay.hacp.inspection.domain.tag.repository.TagRepository;
import com.cmpay.hacp.inspection.infrastructure.tag.converter.TagConverter;
import com.cmpay.hacp.inspection.infrastructure.tag.repository.dataobject.RuleTagDO;
import com.cmpay.hacp.inspection.infrastructure.tag.repository.dataobject.TagDO;
import com.cmpay.hacp.inspection.infrastructure.tag.repository.mapper.TagsMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 标签Repository
 */
@Repository
@RequiredArgsConstructor
public class TagRepositoryImpl extends CrudRepository<TagsMapper, TagDO> implements TagRepository {

    private final PluginTagRepository pluginTagRepository;
    private final RuleTagRepository ruleTagRepository;
    private final TagConverter tagConverter;

    @Override
    public Long save(String name) {
        // 防止标签重名
        TagDO tag = this.getOne(Wrappers.lambdaQuery(TagDO.class)
                .eq(TagDO::getName, name));
        // 不为空，直接返回标签id
        if (tag != null) {
            return tag.getId();
        }

        // 否则保存标签信息
        TagDO tagDO = new TagDO();
        tagDO.setName(name);
        this.save(tagDO);

        return tagDO.getId();
    }

    @Override
    public Map<Long, String> findPluginTagAll() {
        List<PluginTagMapping> list = pluginTagRepository.listAll();

        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        List<Long> tagIds = list.stream()
                .map(PluginTagMapping::getTagId)
                .collect(Collectors.toList());

        List<TagDO> tagDOS = this.list(
                Wrappers.lambdaQuery(TagDO.class)
                        .in(TagDO::getId, tagIds));

        return Optional.ofNullable(tagDOS).map(m -> tagDOS.stream()
                        .collect(Collectors.toMap(TagDO::getId, TagDO::getName)))
                .orElse(null);
    }

    @Override
    public Map<Long, String> findRuleTagAll() {
        List<RuleTagDO> list = ruleTagRepository.listAll();

        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        List<Long> tagIds = list.stream()
                .map(RuleTagDO::getTagId)
                .collect(Collectors.toList());

        List<TagDO> tagDOS = this.list(
                Wrappers.lambdaQuery(TagDO.class)
                        .in(TagDO::getId, tagIds));

        return Optional.ofNullable(tagDOS).map(m -> tagDOS.stream()
                        .collect(Collectors.toMap(TagDO::getId, TagDO::getName)))
                .orElse(null);
    }

    @Override
    public List<Tag> findTagByTagIds(List<Long> tagIds) {
        List<TagDO> tagDOS = this.list(
                Wrappers.lambdaQuery(TagDO.class)
                        .in(TagDO::getId, tagIds));

        return tagConverter.toTagList(tagDOS);
    }
}
