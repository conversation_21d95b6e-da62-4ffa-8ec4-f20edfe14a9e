package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDO;

/**
 * 按次巡检报告生成服务接口
 * 负责单次巡检任务报告的生成、重新生成和管理
 */
public interface PerInspectionReportGenerationService {

    /**
     * 同步生成按次巡检报告
     * 立即生成并返回报告对象
     *
     * @param executionId 任务ID
     * @return 生成的报告对象
     */
    PerInspectionReportDO generatePerInspectionReport(String executionId);
}
