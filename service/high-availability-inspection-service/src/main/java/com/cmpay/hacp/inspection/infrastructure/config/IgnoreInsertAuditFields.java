package com.cmpay.hacp.inspection.infrastructure.config;

import org.mapstruct.Mapping;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

// 创建自定义注解
@Retention(RetentionPolicy.CLASS)
@Target(ElementType.METHOD)
@Mapping(target = "id", ignore = true)
@Mapping(target = "workspaceId", ignore = true)
@Mapping(target = "createdBy", ignore = true)
@Mapping(target = "createdByName", ignore = true)
@Mapping(target = "createdTime", ignore = true)
public @interface IgnoreInsertAuditFields {
}
