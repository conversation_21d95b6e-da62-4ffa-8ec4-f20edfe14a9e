package com.cmpay.hacp.inspection.domain.model.task;

import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 固定时间调度配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FixedTimeScheduleConfig extends ScheduleConfig {
    private LocalDateTime executionDateTime;

    @Override
    public ScheduleType getType() {
        return ScheduleType.FIXED_TIME;
    }

    @Override
    public boolean isValid() {
        return executionDateTime != null;
    }
}
