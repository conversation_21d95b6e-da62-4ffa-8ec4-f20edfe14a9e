package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.repository.IndicatorParamRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.IndicatorParamConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.FireflyIndicatorParamDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.FireflyIndicatorParamMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class IndicatorParamRepositoryImpl extends CrudRepository<FireflyIndicatorParamMapper, FireflyIndicatorParamDO> implements IndicatorParamRepository {
    private final IndicatorParamConverter indicatorParamConverter;

    @Override
    public IndicatorDefinition enrichWithParams(IndicatorDefinition indicatorDefinition) {
        List<FireflyIndicatorParamDO> indicatorParamDOList = list(Wrappers.lambdaQuery(FireflyIndicatorParamDO.class)
                .eq(FireflyIndicatorParamDO::getIndicatorId, indicatorDefinition.getIndicatorId()));
        indicatorDefinition.setInputParams(indicatorParamConverter.toIndicatorParamList(indicatorParamDOList));
        return indicatorDefinition;
    }
}
