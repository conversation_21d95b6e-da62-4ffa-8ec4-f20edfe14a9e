package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginConfig;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginConfigScript;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginOutputField;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginConfigRepository;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginOutputFiledRepository;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PluginConfigK8sStrategyRepositoryImpl implements PluginConfigRepository {
    private final PluginOutputFiledRepository scriptResultRepository;


    @Override
    public void remove(String pluginId) {
        scriptResultRepository.removeByPluginId(pluginId);
    }

    @Override
    public void savePluginConfig(InspectionPlugin inspectionPlugin, String pluginId) {
        String cacheKey = inspectionPlugin.getKey();
        PluginConfigScript configScript = (PluginConfigScript)inspectionPlugin.getPluginConfig();

        // 保存输出字段定义
        if (CollectionUtils.isNotEmpty(configScript.getResults())) {
            scriptResultRepository.saveBatch(configScript.getResults(), pluginId);
        }
    }

    @Override
    public void updatePluginConfig(InspectionPlugin inspectionPlugin, String pluginId) {
        PluginConfigScript configScript = (PluginConfigScript)inspectionPlugin.getPluginConfig();

        // 5. 更新输出字段定义
        if (CollectionUtils.isEmpty(configScript.getResults())) {
            // 如果输出字段为空，直接删除所有相关字段
            scriptResultRepository.removeByPluginId(pluginId);
        } else {
            // 获取当前所有输出字段
            List<PluginOutputField> originList = scriptResultRepository.listByPluginId(pluginId);

            // 创建字段名称到字段对象的映射，提高查找效率
            Map<String, PluginOutputField> originResultMap = originList.stream()
                    .collect(Collectors.toMap(PluginOutputField::getFieldName, Function.identity()));

            List<PluginOutputField> insertList = new ArrayList<>();
            List<PluginOutputField> updateList = new ArrayList<>();
            List<String> deleteFieldNames = new ArrayList<>();

            // 设置将要保留的字段名集合，用于判断删除
            Set<String> retainedFieldNames = new HashSet<>();

            // 处理新增和更新
            for (PluginOutputField scriptResult : configScript.getResults()) {
                String fieldName = scriptResult.getFieldName();
                retainedFieldNames.add(fieldName);

                PluginOutputField existingResult = originResultMap.get(fieldName);
                if (existingResult == null) {
                    // 新增字段
                    insertList.add(scriptResult);
                } else {
                    // 更新字段
                    updateList.add(scriptResult);
                }
            }

            // 确定要删除的字段
            originResultMap.forEach((fieldName, result) -> {
                if (!retainedFieldNames.contains(fieldName)) {
                    deleteFieldNames.add(result.getFieldName());
                }
            });

            if (!insertList.isEmpty()) {
                scriptResultRepository.saveBatch(insertList, pluginId);
            }
            if (!updateList.isEmpty()) {
                scriptResultRepository.updateBatchByName(updateList, pluginId);
            }
            if (!deleteFieldNames.isEmpty()) {
                scriptResultRepository.removeByFieldNames(deleteFieldNames, pluginId);
            }
        }
    }

    @Override
    public PluginConfig queryPluginConfig(String pluginId) {

        List<PluginOutputField> pluginOutputFieldList = scriptResultRepository.listByPluginId(pluginId);

        PluginConfigScript pluginConfigScript = new PluginConfigScript();
        pluginConfigScript.setResults(pluginOutputFieldList);

        return pluginConfigScript;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        PluginConfigStrategyFactory.addStrategy(Collections.singletonList(PluginType.K8S_DEPLOYMENT),this);
    }
}
