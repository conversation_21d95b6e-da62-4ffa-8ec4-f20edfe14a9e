package com.cmpay.hacp.inspection.infrastructure.condition.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionGroupDO;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.mapper.RuleConditionGroupMapper;
import org.springframework.stereotype.Repository;

@Repository
public class RuleConditionGroupRepository extends CrudRepository<RuleConditionGroupMapper, RuleConditionGroupDO> {
}
