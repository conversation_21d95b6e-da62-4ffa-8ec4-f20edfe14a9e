package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.service.DailyInspectionReportGenerationService;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDetailDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.repository.DailyInspectionReportDetailRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.DailyInspectionReportRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleExecutionRepository;
import com.cmpay.hacp.inspection.infrastructure.task.repository.TaskExecutionRepositoryImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 按日报告生成服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DailyInspectionReportGenerationServiceImpl implements DailyInspectionReportGenerationService {

    private final DailyInspectionReportRepository dailyInspectionReportRepository;
    private final DailyInspectionReportDetailRepository dailyInspectionReportDetailRepository;
    private final TaskExecutionRepositoryImpl taskExecutionRepositoryImpl;
    private final RuleExecutionRepository ruleExecutionRepository;

    @Override
    @Transactional
    public DailyInspectionReportDO generateDailyReport(LocalDate reportDate, Integer triggerType) {
        log.info("Starting daily report generation for date: {}, triggerType: {}", reportDate, triggerType);

        LocalDateTime startTime = LocalDateTime.now();

        try {
            // 1. 检查是否已存在该日期的报告
            DailyInspectionReportDO existingReport = getDailyReportByDate(reportDate);
            if (existingReport != null && "COMPLETED".equals(existingReport.getReportStatus())) {
                log.info("Daily report already exists for date: {}, reportId: {}", reportDate, existingReport.getReportId());
                return existingReport;
            }

            // 2. 获取该日期的所有任务执行记录
            List<TaskExecutionDO> taskExecutions = getTaskExecutionsByDate(reportDate);
            if (taskExecutions.isEmpty()) {
                log.warn("No task executions found for date: {}", reportDate);
                return createEmptyDailyReport(reportDate, startTime);
            }

            // 3. 获取所有相关的规则执行记录
            List<Long> taskExecutionIds = taskExecutions.stream()
                    .map(TaskExecutionDO::getId)
                    .collect(Collectors.toList());

            List<RuleExecutionDO> ruleExecutions = getRuleExecutionsByTaskIds(taskExecutionIds);

            // 4. 创建或更新按日报告
            DailyInspectionReportDO dailyReport = existingReport != null ? existingReport : createNewDailyReport(reportDate);

            // 5. 设置生成状态
            dailyReport.setReportStatus("GENERATING");

            // 6. 计算汇总统计数据
            calculateSummaryStatistics(dailyReport, taskExecutions, ruleExecutions);

            // 7. 构建详细内容
            DailyInspectionReportDetailDO detail = buildDailyReportContent(reportDate, taskExecutions, ruleExecutions);

            // 8. 设置完成状态
            dailyReport.setReportStatus("COMPLETED");

            // 9. 保存报告
            dailyInspectionReportRepository.saveOrUpdate(dailyReport);

            dailyInspectionReportDetailRepository.saveOrUpdate(detail);

            log.info("Daily report generated successfully for date: {}, reportId: {}",
                    reportDate, dailyReport.getReportId());

            return dailyReport;

        } catch (Exception e) {
            log.error("Failed to generate daily report for date: {}", reportDate, e);

            // 更新失败状态
            DailyInspectionReportDO failedReport = getDailyReportByDate(reportDate);
            if (failedReport != null) {
                failedReport.setReportStatus("FAILED");
                dailyInspectionReportRepository.saveOrUpdate(failedReport);
            }

            throw new RuntimeException("Failed to generate daily report for date: " + reportDate, e);
        }
    }

    /**
     * 获取指定日期的按日报告
     */
    private DailyInspectionReportDO getDailyReportByDate(LocalDate reportDate) {
        return dailyInspectionReportRepository.getOne(
                Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                        .eq(DailyInspectionReportDO::getReportDate, reportDate)
        );
    }

    /**
     * 获取指定日期的所有任务执行记录
     */
    private List<TaskExecutionDO> getTaskExecutionsByDate(LocalDate reportDate) {
        LocalDateTime startOfDay = reportDate.atStartOfDay();
        LocalDateTime endOfDay = reportDate.plusDays(1).atStartOfDay();

        return taskExecutionRepositoryImpl.list(
                Wrappers.lambdaQuery(TaskExecutionDO.class)
                        .between(TaskExecutionDO::getExecutionTime, startOfDay, endOfDay)
                        .orderBy(true, true, TaskExecutionDO::getExecutionTime)
        );
    }

    /**
     * 根据任务执行ID列表获取规则执行记录
     */
    private List<RuleExecutionDO> getRuleExecutionsByTaskIds(List<Long> taskExecutionIds) {
        if (taskExecutionIds.isEmpty()) {
            return new ArrayList<>();
        }

        return ruleExecutionRepository.list(
                Wrappers.lambdaQuery(RuleExecutionDO.class)
                        .in(RuleExecutionDO::getTaskExecutionId, taskExecutionIds)
                        .orderBy(true, true, RuleExecutionDO::getExecutionTime)
        );
    }

    /**
     * 创建空的按日报告（当没有任务执行记录时）
     */
    private DailyInspectionReportDO createEmptyDailyReport(LocalDate reportDate, LocalDateTime startTime) {
        DailyInspectionReportDO dailyReport = createNewDailyReport(reportDate);
        dailyReport.setReportStatus("COMPLETED");

        // 设置所有统计字段为0
        dailyReport.setRuleCount(0);
        dailyReport.setExecutionCount(0);
        dailyReport.setWarningCount(0);
        dailyReport.setErrorCount(0);
        dailyReport.setPassRate(0.0);
        dailyReport.setPassRateChange(0.0);
        dailyReport.setAverageResponseTime(0L);
        dailyReport.setResponseTimeChange(0L);
        dailyReport.setTotalExceptionCount(0);

        dailyInspectionReportRepository.save(dailyReport);
        return dailyReport;
    }

    /**
     * 创建新的按日报告对象
     */
    private DailyInspectionReportDO createNewDailyReport(LocalDate reportDate) {
        DailyInspectionReportDO dailyReport = new DailyInspectionReportDO();
        dailyReport.setReportDate(reportDate);
        dailyReport.setReportId(generateDailyReportId(reportDate));
        dailyReport.setReportStatus("GENERATING");
        return dailyReport;
    }

    /**
     * 生成按日报告ID
     */
    private String generateDailyReportId(LocalDate reportDate) {
        return "DR" + reportDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 计算汇总统计数据
     */
    private void calculateSummaryStatistics(DailyInspectionReportDO dailyReport, List<TaskExecutionDO> taskExecutions, List<RuleExecutionDO> ruleExecutions) {
        // 基础统计
        dailyReport.setExecutionCount(ruleExecutions.size());

        // 统计规则数量（去重）
        Set<String> uniqueRules = ruleExecutions.stream()
                .map(RuleExecutionDO::getRuleId)
                .collect(Collectors.toSet());
        dailyReport.setRuleCount(uniqueRules.size());

        // 统计规则执行结果
        int totalRuleExecutions = ruleExecutions.size();
        long successRuleExecutions = ruleExecutions.stream()
                .filter(rule -> "1".equals(rule.getExecutionStatus()) && !rule.isMatchStatus())
                .count();
        long warningRuleExecutions = ruleExecutions.stream()
                .filter(rule -> "1".equals(rule.getExecutionStatus()) && rule.isMatchStatus())
                .count();
        long failedRuleExecutions = ruleExecutions.stream()
                .filter(rule -> "0".equals(rule.getExecutionStatus()))
                .count();

        // 计算通过率
        double passRate = totalRuleExecutions > 0 ? (successRuleExecutions * 100.0) / totalRuleExecutions : 0.0;
        dailyReport.setPassRate(passRate);

        // 统计异常数量（失败的规则执行视为异常）
        dailyReport.setErrorCount((int) failedRuleExecutions);
        dailyReport.setWarningCount((int) warningRuleExecutions);
        dailyReport.setTotalExceptionCount((int) failedRuleExecutions);

        // 计算平均响应时间
        OptionalDouble avgResponseTime = ruleExecutions.stream()
                .filter(rule -> rule.getExecutionDuration() != null)
                .mapToLong(RuleExecutionDO::getExecutionDuration)
                .average();
        dailyReport.setAverageResponseTime(avgResponseTime.isPresent() ? (long) avgResponseTime.getAsDouble() : 0L);

        // 计算昨日对比数据（通过率变化和响应时间变化）
        calculateTrendChanges(dailyReport);
    }

    /**
     * 计算趋势变化数据
     */
    private void calculateTrendChanges(DailyInspectionReportDO dailyReport) {
        LocalDate yesterdayDate = dailyReport.getReportDate().minusDays(1);
        DailyInspectionReportDO yesterdayReport = getDailyReportByDate(yesterdayDate);

        if (yesterdayReport != null) {
            // 计算通过率变化
            double passRateChange = dailyReport.getPassRate() - yesterdayReport.getPassRate();
            dailyReport.setPassRateChange(passRateChange);

            // 计算响应时间变化
            long responseTimeChange = dailyReport.getAverageResponseTime() - yesterdayReport.getAverageResponseTime();
            dailyReport.setResponseTimeChange(responseTimeChange);
        } else {
            dailyReport.setPassRateChange(0.0);
            dailyReport.setResponseTimeChange(0L);
        }
    }

    /**
     * 构建按日报告详细内容
     */
    private DailyInspectionReportDetailDO buildDailyReportContent(LocalDate reportDate, List<TaskExecutionDO> taskExecutions, List<RuleExecutionDO> ruleExecutions) {
        return DailyInspectionReportDetailDO.builder()
                .categoryStatistics(buildCategoryStatistics(ruleExecutions))
                .recentPassRateTrends(buildRecentPassRateTrends(reportDate))
                .exceptionDetails(buildExceptionDetails(ruleExecutions))
                .trendAnalysis(buildTrendAnalysis(reportDate))
                .build();
    }

    /**
     * 构建巡检分类统计
     */
    private List<DailyInspectionReportDetailDO.CategoryStatistics> buildCategoryStatistics(List<RuleExecutionDO> ruleExecutions) {
        // 按插件名称分类统计（可以根据实际业务调整分类逻辑）
        Map<String, List<RuleExecutionDO>> categoryGroups = ruleExecutions.stream()
                .collect(Collectors.groupingBy(rule ->
                        rule.getPluginName() != null ? rule.getPluginName() : "其他"));// FIXME 规则类型

        List<DailyInspectionReportDetailDO.CategoryStatistics> categoryStatistics = new ArrayList<>();
        int totalChecks = ruleExecutions.size();

        for (Map.Entry<String, List<RuleExecutionDO>> entry : categoryGroups.entrySet()) {
            String categoryName = entry.getKey();
            List<RuleExecutionDO> categoryRules = entry.getValue();
            int checkCount = categoryRules.size();
            double percentage = checkCount * 100.0 / totalChecks;

            categoryStatistics.add(DailyInspectionReportDetailDO.CategoryStatistics.builder()
                    .categoryName(categoryName)
                    .checkCount(checkCount)
                    .percentage(percentage)
                    .build());
        }

        return categoryStatistics;
    }

    /**
     * 构建近7日通过率趋势
     */
    private List<DailyInspectionReportDetailDO.DailyPassRateTrend> buildRecentPassRateTrends(LocalDate reportDate) {
        List<DailyInspectionReportDetailDO.DailyPassRateTrend> trends = new ArrayList<>();

        // 获取近7日的数据
        for (int i = 6; i >= 0; i--) {
            LocalDate date = reportDate.minusDays(i);

            DailyInspectionReportDO historicalReport = dailyInspectionReportRepository.getOne(
                    Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                            .select(DailyInspectionReportDO::getPassRate)
                            .eq(DailyInspectionReportDO::getReportDate, reportDate));

            double passRate = 0.0;
            String level = "差";

            if (historicalReport != null && historicalReport.getPassRate() != null) {
                passRate = historicalReport.getPassRate();
            }

            // 根据通过率确定级别
            if (passRate >= 95) {
                level = "优秀";
            } else if (passRate >= 90) {
                level = "良好";
            } else if (passRate >= 80) {
                level = "中等";
            }

            trends.add(DailyInspectionReportDetailDO.DailyPassRateTrend.builder()
                    .date(date)
                    .passRate(passRate)
                    .level(level)
                    .build());
        }

        return trends;
    }

    /**
     * 构建异常详情
     */
    private DailyInspectionReportDetailDO.ExceptionDetails buildExceptionDetails(List<RuleExecutionDO> ruleExecutions) {
        // 统计异常数量
        List<RuleExecutionDO> exceptionRules = ruleExecutions.stream()
                .filter(rule -> "0".equals(rule.getExecutionStatus()) || rule.isMatchStatus())
                .collect(Collectors.toList());

        int totalCount = exceptionRules.size();
        int errorCount = (int) exceptionRules.stream()
                .filter(rule -> "0".equals(rule.getExecutionStatus()))
                .count();
        int warningCount = totalCount - errorCount;

        // 构建异常项列表
        List<DailyInspectionReportDetailDO.ExceptionDetails.ExceptionItem> exceptions = exceptionRules.stream()
                .map(rule -> DailyInspectionReportDetailDO.ExceptionDetails.ExceptionItem.builder()
                        .ruleExecutionId(rule.getId())
                        .type("0".equals(rule.getExecutionStatus()) ? "错误" : "警告")
                        .title(rule.getRuleId()) // FIXME 规则名称
                        .description(rule.getExecutionResult())
                        .resourceType("HOST")
                        .resourceName(rule.getResourceName())
                        .suggest("请检查资源状态和规则配置")
                        .occurTime(rule.getExecutionTime())
                        .build())
                .collect(Collectors.toList());

        return DailyInspectionReportDetailDO.ExceptionDetails.builder()
                .totalCount(totalCount)
                .errorCount(errorCount)
                .warningCount(warningCount)
                .exceptions(exceptions)
                .build();
    }

    /**
     * 构建趋势分析
     */
    private DailyInspectionReportDetailDO.TrendAnalysis buildTrendAnalysis(LocalDate reportDate) {
        // 构建通过率趋势
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> passRateTrend = buildPassRateTrendPoints(reportDate);

        // 构建响应时间趋势
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> responseTimeTrend = buildResponseTimeTrendPoints(reportDate);

        // 构建异常数量趋势
        DailyInspectionReportDetailDO.TrendAnalysis.ExceptionTrend exceptionTrend = buildExceptionTrend(reportDate);

        return DailyInspectionReportDetailDO.TrendAnalysis.builder()
                .passRateTrend(passRateTrend)
                .responseTimeTrend(responseTimeTrend)
                .exceptionTrend(exceptionTrend)
                .build();
    }

    /**
     * 构建通过率趋势点
     */
    private List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> buildPassRateTrendPoints(LocalDate reportDate) {
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> trendPoints = new ArrayList<>();

        for (int i = 6; i >= 0; i--) {
            LocalDate date = reportDate.minusDays(i);
            DailyInspectionReportDO historicalReport = dailyInspectionReportRepository.getOne(
                    Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                            .select(DailyInspectionReportDO::getPassRate)
                            .eq(DailyInspectionReportDO::getReportDate, reportDate));

            double passRate = 0.0;
            if (historicalReport != null && historicalReport.getPassRate() != null) {
                passRate = historicalReport.getPassRate();
            }

            trendPoints.add(DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint.builder()
                    .date(date)
                    .dateDisplay(date.format(DateTimeFormatter.ofPattern("MM-dd")))
                    .value(passRate)
                    .unit("%")
                    .build());
        }

        return trendPoints;
    }

    /**
     * 构建响应时间趋势点
     */
    private List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> buildResponseTimeTrendPoints(LocalDate reportDate) {
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> trendPoints = new ArrayList<>();

        for (int i = 6; i >= 0; i--) {
            LocalDate date = reportDate.minusDays(i);
            DailyInspectionReportDO historicalReport = dailyInspectionReportRepository.getOne(
                    Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                            .select(DailyInspectionReportDO::getAverageResponseTime)
                            .eq(DailyInspectionReportDO::getReportDate, reportDate));

            double responseTime = 0.0;
            if (historicalReport != null && historicalReport.getAverageResponseTime() != null) {
                responseTime = historicalReport.getAverageResponseTime().doubleValue();
            }

            trendPoints.add(DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint.builder()
                    .date(date)
                    .dateDisplay(date.format(DateTimeFormatter.ofPattern("MM-dd")))
                    .value(responseTime)
                    .unit("ms")
                    .build());
        }

        return trendPoints;
    }

    /**
     * 构建异常趋势
     */
    private DailyInspectionReportDetailDO.TrendAnalysis.ExceptionTrend buildExceptionTrend(LocalDate reportDate) {
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> errorTrend = new ArrayList<>();
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> warningTrend = new ArrayList<>();

        for (int i = 6; i >= 0; i--) {
            LocalDate date = reportDate.minusDays(i);
            DailyInspectionReportDO historicalReport = dailyInspectionReportRepository.getOne(
                    Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                            .select(DailyInspectionReportDO::getErrorCount)
                            .select(DailyInspectionReportDO::getWarningCount)
                            .eq(DailyInspectionReportDO::getReportDate, reportDate));

            double errorCount = 0.0;
            double warningCount = 0.0;

            if (historicalReport != null) {
                if (historicalReport.getErrorCount() != null) {
                    errorCount = historicalReport.getErrorCount().doubleValue();
                }
                if (historicalReport.getWarningCount() != null) {
                    warningCount = historicalReport.getWarningCount().doubleValue();
                }
            }

            String dateDisplay = date.format(DateTimeFormatter.ofPattern("MM-dd"));

            errorTrend.add(DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint.builder()
                    .date(date).dateDisplay(dateDisplay).value(errorCount).unit("个").build());
            warningTrend.add(DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint.builder()
                    .date(date).dateDisplay(dateDisplay).value(warningCount).unit("个").build());
        }

        return DailyInspectionReportDetailDO.TrendAnalysis.ExceptionTrend.builder()
                .errorTrend(errorTrend)
                .warningTrend(warningTrend)
                .build();
    }
}
