package com.cmpay.hacp.inspection.infrastructure.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.cmpay.lemon.framework.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Slf4j
@Component
public class InspectionMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("Starting to insert padding...");
        this.strictInsertFill(metaObject, "createdBy", String.class, SecurityUtils.getLoginUserId());
        this.strictInsertFill(metaObject, "createdByName", String.class, SecurityUtils.getLoginName());
        this.strictInsertFill(metaObject, "createdTime", LocalDateTime.class, LocalDateTime.now());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("Starting to update padding...");
        this.strictUpdateFill(metaObject, "updatedBy", String.class, SecurityUtils.getLoginUserId());
        this.strictUpdateFill(metaObject, "updatedByName", String.class, SecurityUtils.getLoginName());
        this.strictUpdateFill(metaObject, "updatedTime", LocalDateTime.class, LocalDateTime.now());
    }
}
