package com.cmpay.hacp.inspection.infrastructure.rule.converter;

import com.cmpay.hacp.inspection.domain.rule.model.RulePluginMapping;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.rule.repository.dataobject.RulePluginMappingDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RulePluginConverter {
    RulePluginMapping toRulePluginMapping(RulePluginMappingDO rulePluginMappingDO);

    List<RulePluginMapping> toRulePluginMappingList(List<RulePluginMappingDO> rulePluginMappingDOList);

    @IgnoreAuditFields
    RulePluginMappingDO toRulePluginMappingDO(RulePluginMapping rulePluginMapping);


}
