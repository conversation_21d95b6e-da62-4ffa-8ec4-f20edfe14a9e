package com.cmpay.hacp.inspection.domain.model.task;

import com.cmpay.hacp.inspection.domain.model.common.AuditInfo;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TaskStatus;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检任务聚合根
 */
@Data
@Builder
public class InspectionTask {
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 执行状态：0-待执行，1-执行中，2-已完成，3-执行失败，4-已取消，5-告警，6-超时，7-已停止
     */
    private ExecutionStatus executionStatus;

    /**
     * 任务状态
     */
    private TaskStatus status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务关联的规则执行对象列表
     */
    private List<TaskRuleExecution> taskRuleExecutions;

    /**
     * 调度配置
     */
    private ScheduleConfig scheduleConfig;

    /**
     * 执行计划
     */
    private String executionPlan;

    /**
     * 最近执行
     */
    private LocalDateTime latestExecutionTime;

    private AlarmNotification alarmNotification;

    private AuditInfo auditInfo;
}
