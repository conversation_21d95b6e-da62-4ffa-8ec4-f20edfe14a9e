package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.repository.IndicatorDefinitionRepository;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.IndicatorDefinitionConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.FireflyIndicatorDefinitionDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.FireflyIndicatorDefinitionMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class IndicatorDefinitionRepositoryImpl extends CrudRepository<FireflyIndicatorDefinitionMapper, FireflyIndicatorDefinitionDO> implements IndicatorDefinitionRepository {
    private final IndicatorDefinitionConverter indicatorDefinitionConverter;

    @Override
    public List<IndicatorDefinition> findByType(IndicatorType indicatorType) {
        List<FireflyIndicatorDefinitionDO> indicatorDefinitionDOList = list(Wrappers.lambdaQuery(FireflyIndicatorDefinitionDO.class)
                .eq(FireflyIndicatorDefinitionDO::getIndicatorType, indicatorType));
        return indicatorDefinitionConverter.toIndicatorDefinitionList(indicatorDefinitionDOList);
    }

    @Override
    public IndicatorDefinition findByName(String indicatorName) {
        FireflyIndicatorDefinitionDO indicatorDefinitionDO = getOne(Wrappers.lambdaQuery(FireflyIndicatorDefinitionDO.class)
                .eq(FireflyIndicatorDefinitionDO::getIndicatorName, indicatorName));
        return indicatorDefinitionConverter.toIndicatorDefinition(indicatorDefinitionDO);
    }
}
