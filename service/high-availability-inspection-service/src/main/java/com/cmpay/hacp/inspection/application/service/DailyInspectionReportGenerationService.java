package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;

import java.time.LocalDate;

/**
 * 按日报告生成服务接口
 * 负责按日巡检报告的生成、调度和管理
 */
public interface DailyInspectionReportGenerationService {

    /**
     * 生成指定日期的按日报告
     *
     * @param reportDate  报告日期
     * @param triggerType 触发类型（0:定时触发 1:手动触发 2:补偿触发）
     * @return 生成的报告对象
     */
    DailyInspectionReportDO generateDailyReport(LocalDate reportDate, Integer triggerType);
}
