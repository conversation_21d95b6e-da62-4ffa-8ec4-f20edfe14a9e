package com.cmpay.hacp.inspection.domain.plugin.model;

import com.cmpay.hacp.inspection.domain.model.enums.ParamType;
import lombok.Data;

/**
 * 插件参数业务对象
 */
@Data
public class PluginScriptParameter {
    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数类型(文本、数字、邮箱、URL、自定义正则等)
     */
    private ParamType paramType;

    /**
     * 正则表达式
     */
    private String regexPattern;

    /**
     * 参数值
     */
    private String paramValue;

    /**
     * 参数描述
     */
    private String paramDesc;

    /**
     * 是否加密(0不加密，1加密)
     */
    private Boolean isEncrypted;
}
