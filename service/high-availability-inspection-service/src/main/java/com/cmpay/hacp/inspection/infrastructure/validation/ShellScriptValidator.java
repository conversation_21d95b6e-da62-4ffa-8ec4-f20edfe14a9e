package com.cmpay.hacp.inspection.infrastructure.validation;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public class ShellScriptValidator {
    private static final List<String> FORBIDDEN_COMMANDS = Arrays.asList(
            "rm -rf", "mkfs", "dd", ":(){ :|:& };:", "shutdown", "reboot"
    );

    public void validateScript(String scriptContent) {
        // 检查脚本中是否包含危险命令
        for (String command : FORBIDDEN_COMMANDS) {
            if (scriptContent.contains(command)) {
//                throw new UnsafeScriptException("Script contains forbidden command: " + command);
            }
        }

        // 可以添加更复杂的脚本语法分析
    }
}
