package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginTagMapping;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginTagRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.PluginTagConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginTagDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PluginTagsMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 插件标签关联Repository
 */
@Repository
@RequiredArgsConstructor
public class PluginTagRepositoryImpl extends CrudRepository<PluginTagsMapper, PluginTagDO> implements PluginTagRepository {
    private final PluginTagConverter pluginTagConverter;

    @Override
    public void saveBatch(List<PluginTagMapping> pluginTagMappingList) {
        if (pluginTagMappingList == null || pluginTagMappingList.isEmpty()) {
            return;
        }

        List<PluginTagDO> tagDOs = pluginTagMappingList.stream()
                .map(pluginTagConverter::toPluginTagDO)
                .collect(Collectors.toList());

        this.saveBatch(tagDOs, 10);
    }

    @Override
    public void removeByPluginId(String pluginId) {
        this.remove(Wrappers.lambdaQuery(PluginTagDO.class)
                .eq(PluginTagDO::getPluginId, pluginId));
    }

    @Override
    public List<PluginTagMapping> list(String pluginId) {
        return pluginTagConverter.toPluginTagMappingList(this.list(
                Wrappers.lambdaQuery(PluginTagDO.class)
                        .eq(PluginTagDO::getPluginId, pluginId)));
    }

    @Override
    public void removeByTagIds(List<Long> tagIds, String pluginId) {
        this.remove(Wrappers.lambdaQuery(PluginTagDO.class)
                .eq(PluginTagDO::getPluginId, pluginId)
                .in(PluginTagDO::getTagId, tagIds));
    }

    @Override
    public List<PluginTagMapping> listByTagIds(List<Long> tagIds) {
        if(CollectionUtils.isEmpty(tagIds)){
            return new ArrayList<>();
        }
        return pluginTagConverter.toPluginTagMappingList(this.list(
                Wrappers.lambdaQuery(PluginTagDO.class)
                        .in(PluginTagDO::getTagId, tagIds)));
    }

    @Override
    public List<PluginTagMapping> listByPluginIds(List<String> pluginIds) {
        if(CollectionUtils.isEmpty(pluginIds)){
            return new ArrayList<>();
        }
        return pluginTagConverter.toPluginTagMappingList(this.list(
                Wrappers.lambdaQuery(PluginTagDO.class)
                        .in(PluginTagDO::getPluginId, pluginIds)));
    }

    @Override
    public List<PluginTagMapping> listAll(){
        return pluginTagConverter.toPluginTagMappingList(this.list());
    }

    @Override
    public void updateLinkPluginId(String pluginId, List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            // 如果标签为空，直接删除所有关联
            this.removeByPluginId(pluginId);
        } else {
            // 获取原有标签关联
            List<PluginTagMapping> originList = this.list(pluginId);

            // 将原有标签ID转换为Set提高查找效率
            Set<Long> existingTagIds = originList.stream()
                    .map(PluginTagMapping::getTagId)
                    .collect(Collectors.toSet());

            // 将需要保留的标签ID转为Set
            Set<Long> retainedTagIds = new HashSet<>(tagIds);

            // 准备插入列表 - 找出新增的标签ID
            List<PluginTagMapping> tagsToInsert = retainedTagIds.stream()
                    .filter(tagId -> !existingTagIds.contains(tagId))
                    .map(tagId -> {
                        PluginTagMapping newTag = new PluginTagMapping();
                        newTag.setPluginId(pluginId);
                        newTag.setTagId(tagId);
                        return newTag;
                    })
                    .collect(Collectors.toList());

            // 因标签关联对象只有插件ID和标签ID，所以更新列表无需更新

            // 准备删除列表 - 找出需要删除的标签
            List<Long> idsToDelete = new ArrayList<>();
            for (PluginTagMapping existingTag : originList) {
                if (!retainedTagIds.contains(existingTag.getTagId())) {
                    idsToDelete.add(existingTag.getTagId());
                }
            }

            if (!tagsToInsert.isEmpty()) {
                this.saveBatch(tagsToInsert);
            }
            if (!idsToDelete.isEmpty()) {
                this.removeByTagIds(idsToDelete, pluginId);
            }
        }
    }

    @Override
    public void saveLinkPluginId(String pluginId, List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return;
        }
        List<PluginTagMapping> mappings = tagIds.stream()
                .map(tagId -> {
                    PluginTagMapping mapping = new PluginTagMapping();
                    mapping.setPluginId(pluginId);
                    mapping.setTagId(tagId);
                    return mapping;
                })
                .collect(Collectors.toList());

        this.saveBatch(mappings);
    }
}
