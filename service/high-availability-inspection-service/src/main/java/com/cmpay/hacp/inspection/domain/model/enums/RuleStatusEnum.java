package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 规则状态
 */
@Getter
@Slf4j
public enum RuleStatusEnum {

    ENABLED(1, "启用"),
    DISABLED(0, "禁用");

    @JsonValue
    private final Integer code;
    private final String desc;

    RuleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    private static final Map<Integer, RuleStatusEnum> ENUM_MAP = Arrays.stream(RuleStatusEnum.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static RuleStatusEnum getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
