package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
@Slf4j
public enum ResourceExecutionStrategyEnum {

    RANDOM(0,"随机选择一台虚拟机执行"),
    ALL(1, "全部虚拟机执行"),
    ;

    @JsonValue
    private final Integer code;
    private final String desc;

    ResourceExecutionStrategyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<Integer, ResourceExecutionStrategyEnum> ENUM_MAP = Arrays.stream(ResourceExecutionStrategyEnum.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static ResourceExecutionStrategyEnum getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
