package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptParameter;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptParameterRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.PluginScriptParameterConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginScriptParameterDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PluginScriptParameterMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PluginScriptParameterRepositoryImpl extends CrudRepository<PluginScriptParameterMapper, PluginScriptParameterDO> implements PluginScriptParameterRepository {
    private final PluginScriptParameterConverter pluginScriptParameterConverter;

    @Override
    public void removeByPluginId(String pluginId) {
        this.remove(Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                .eq(PluginScriptParameterDO::getPluginId, pluginId));
    }

    @Override
    public void saveBatch(List<PluginScriptParameter> parameters, String pluginId) {
        if (parameters == null || parameters.isEmpty()) {
            return;
        }

        List<PluginScriptParameterDO> results = parameters.stream()
                .map(scriptResult -> pluginScriptParameterConverter.toPluginScriptParameterDO(scriptResult, pluginId))
                .collect(Collectors.toList());

        this.saveBatch(results, 10);
    }

    @Override
    public List<PluginScriptParameter> listByPluginId(String pluginId) {
        return pluginScriptParameterConverter.toPluginScriptParameterList(this.list(
                Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                        .eq(PluginScriptParameterDO::getPluginId, pluginId)));
    }

    @Override
    public void removeByPluginIdAndParamNames(String pluginId, List<String> deleteParamNames) {
        this.remove(Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                .eq(PluginScriptParameterDO::getPluginId, pluginId)
                .in(PluginScriptParameterDO::getParamName, deleteParamNames));
    }

    @Override
    public void updateBatchByPluginIdAndParamName(List<PluginScriptParameter> parameters, String pluginId) {
        if (parameters == null || parameters.isEmpty()) {
            return;
        }

        List<PluginScriptParameterDO> results = parameters.stream()
                .map(scriptResult -> pluginScriptParameterConverter.toPluginScriptParameterDO(scriptResult, pluginId))
                .collect(Collectors.toList());

        this.updateBatchById(results, 10);
    }

    @Override
    public Map<String, Object> getParameterMapByPluginId(String pluginId) {
        return this.getMap(
                Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                        .select(PluginScriptParameterDO::getParamName, PluginScriptParameterDO::getParamValue)
                        .eq(PluginScriptParameterDO::getPluginId, pluginId)
        );
    }
}
