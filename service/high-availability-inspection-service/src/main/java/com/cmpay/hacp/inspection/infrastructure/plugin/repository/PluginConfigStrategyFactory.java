package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.cmpay.hacp.exception.StrategyFactoryException;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginConfigRepository;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class PluginConfigStrategyFactory {

    public static Map<Integer, PluginConfigRepository> PLUGIN_CONFIG_REPOSITORY_MAP;

    static {
        PLUGIN_CONFIG_REPOSITORY_MAP = new HashMap<>();
    }

    /**
     * 添加插件配置策略
     * @param pluginTypes
     * @param pluginConfigRepository
     */
    protected static void addStrategy(@NotNull List<PluginType> pluginTypes, @NotNull PluginConfigRepository pluginConfigRepository) {
        pluginTypes.forEach(f->{
            if(PLUGIN_CONFIG_REPOSITORY_MAP.containsKey(f.getCode())){
                log.error("old clazz :{}", pluginConfigRepository.getClass());
                throw new StrategyFactoryException("类型策略重复：" + f.getCode());
            }
            PLUGIN_CONFIG_REPOSITORY_MAP.put(f.getCode(), pluginConfigRepository);
        });
    }

    public static PluginConfigRepository newInstance(PluginType pluginType) {
        return PLUGIN_CONFIG_REPOSITORY_MAP.get(pluginType.getCode());
    }
}
