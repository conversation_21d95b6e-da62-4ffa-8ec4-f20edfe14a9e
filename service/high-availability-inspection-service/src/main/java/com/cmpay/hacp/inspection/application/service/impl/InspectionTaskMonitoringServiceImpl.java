package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.inspection.application.service.InspectionTaskMonitoringService;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionResult;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.domain.model.task.ResourceExecutionStats;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.inspection.domain.task.model.Resource;
import com.cmpay.hacp.inspection.domain.task.repository.TaskExecutionRepository;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRuleResourceRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleExecutionRepository;
import com.cmpay.hacp.utils.RandomUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 巡检任务监控服务
 * 负责跟踪和更新任务执行状态
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionTaskMonitoringServiceImpl implements InspectionTaskMonitoringService {
    private final TaskExecutionRepository taskExecutionRepository;
    private final RuleExecutionRepository ruleExecutionRepository;
    private final TaskRuleResourceRepository taskRuleResourceRepository;

    // 内存中的任务执行状态缓存，用于快速访问
    private final Map<String, TaskExecutionDO> activeTaskExecutions = new ConcurrentHashMap<>();

    /**
     * 初始化任务执行状态
     *
     * @param taskId      任务ID
     * @param taskName    任务名称
     * @param triggerMode 触发方式 0:定时触发 1:手动触发
     * @param ruleTotal   规则总数
     */
    @Override
    public String initializeTaskExecution(String taskId, String taskName, TriggerMode triggerMode, Integer ruleTotal) {
        String executionId = "EXE-" + LocalDateTime.now() + "-" + RandomUtil.getCharacterAndNumber(3);

        TaskExecutionDO execution = new TaskExecutionDO();
        execution.setExecutionId(executionId);
        execution.setTaskId(taskId);
        execution.setTaskName(taskName);
        execution.setTriggerMode(triggerMode);
        execution.setExecutionStatus(ExecutionStatus.PENDING);
        execution.setScheduledTime(LocalDateTime.now());
        execution.setRuleTotal(ruleTotal);
        execution.setRuleSuccessCount(0);
        execution.setRuleFailCount(0);
        execution.setResultSummary("Task initialized");

        taskExecutionRepository.save(execution);

        // 缓存活动任务
        activeTaskExecutions.put(executionId, execution);

        log.info("Task execution initialized: {}, status ID: {}", taskId, execution.getId());

        return executionId;
    }

    /**
     * 更新任务为执行中状态
     *
     * @param executionId 执行ID
     */
    @Override
    public void markTaskAsRunning(String executionId) {
        TaskExecutionDO executionStatus = getTaskExecutionStatus(executionId);
        if (executionStatus != null) {
            executionStatus.setExecutionStatus(ExecutionStatus.RUNNING); // 执行中
            executionStatus.setStartTime(LocalDateTime.now());
            executionStatus.setExecutionTime(LocalDateTime.now());
            executionStatus.setStatusSummary("Task execution in progress");

            taskExecutionRepository.updateById(executionStatus);
            log.info("Task marked as running: {}", executionStatus.getTaskId());
        }
    }

    /**
     * 更新任务为已完成状态
     *
     * @param executionId 执行ID
     * @param success     是否成功
     */
    @Override
    public void markTaskAsCompleted(String executionId, boolean success) {
        TaskExecutionDO executionStatus = getTaskExecutionStatus(executionId);
        if (executionStatus != null) {
            LocalDateTime endTime = LocalDateTime.now();
            executionStatus.setExecutionStatus(success ? ExecutionStatus.COMPLETED : ExecutionStatus.FAILED); // 2:已完成 3:失败
            executionStatus.setExecutionResult(success ? ExecutionResult.PASS : ExecutionResult.FAIL);
            executionStatus.setEndTime(endTime);

            // 计算执行时长（秒）
            if (executionStatus.getStartTime() != null) {
                long durationSeconds = ChronoUnit.SECONDS.between(executionStatus.getStartTime(), endTime);
                executionStatus.setDuration(durationSeconds);
            }

            // 计算成功率
            if (executionStatus.getRuleTotal() > 0) {
                int successRate = (executionStatus.getRuleSuccessCount() * 100) / executionStatus.getRuleTotal();
                executionStatus.setSuccessRate(successRate);
            }

            // 计算资源覆盖率
            Integer coverageRate = calculateResourceCoverageRate(executionId);
            if (coverageRate != null) {
                executionStatus.setCoverageRate(coverageRate);
            }


            executionStatus.setResultSummary(success ?
                    "Task completed successfully" :
                    "Task completed with failures");

            taskExecutionRepository.updateById(executionStatus);

            // 从活动任务缓存中移除
            activeTaskExecutions.remove(executionId);

            log.info("Task marked as completed: {}, success: {}", executionId, success);
        }
    }

    /**
     * 记录规则执行结果
     *
     * @param taskId   任务ID
     * @param ruleId   规则ID
     * @param pluginId 插件ID
     * @param result   执行结果
     */
    @Override
    public void recordRuleExecutionResult(String taskId, String executionId, String ruleId, String pluginId, InspectionResult result) {
        recordRuleExecutionResult(taskId, executionId, ruleId, pluginId, result, null, null);
    }

    /**
     * 记录规则执行结果（包含资源信息）
     *
     * @param taskId       任务ID
     * @param ruleId       规则ID
     * @param pluginId     插件ID
     * @param result       执行结果
     * @param resource     资源
     * @param resourceName 资源名称
     */
    @Override
    public void recordRuleExecutionResult(String taskId, String executionId, String ruleId, String pluginId, InspectionResult result,
                                          String resource, String resourceName) {
        TaskExecutionDO taskStatus = getTaskExecutionStatus(executionId);
        if (taskStatus == null) {
            log.warn("Cannot record rule execution result: task status not found for taskId: {}", taskId);
            return;
        }

        // 创建规则执行状态记录
        RuleExecutionDO ruleStatus = new RuleExecutionDO();
        ruleStatus.setTaskExecutionId(executionId);
        ruleStatus.setRuleId(ruleId);
        ruleStatus.setPluginId(pluginId);
        ruleStatus.setExecutionStatus(result.isSuccess() ? "1" : "0"); // 1:成功 0:失败
        ruleStatus.setMatchStatus(result.isRuleMatchingSuccess());
        ruleStatus.setExecutionTime(LocalDateTime.now());
        List<RuleMatchingResult.RuleMatchingFieldResult> ruleMatchingFieldResults = result.getRuleMatchingResult()
                .getRuleMatchingFieldResults();
        Optional.ofNullable(ruleMatchingFieldResults).ifPresent(f -> {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < f.size(); ) {
                sb.append(f.get(i).getFieldName())
                        .append(CommonConstant.COLON)
                        .append(f.get(i).getActualValue());
                if (i++ < f.size()) {
                    sb.append(CommonConstant.LINE_BREAK);
                }
            }
            ruleStatus.setExecutionResult(sb.toString());
        });
        ruleStatus.setPluginName(result.getPluginName());

        // 设置资源信息
        if (StringUtils.hasText(resource)) {
            ruleStatus.setResource(resource);
        }
        if (StringUtils.hasText(resourceName)) {
            ruleStatus.setResourceName(resourceName);
        }

        ruleExecutionRepository.save(ruleStatus);

        // 更新任务执行状态中的规则执行计数
        if (result.isSuccess()) {
            taskStatus.setRuleSuccessCount(taskStatus.getRuleSuccessCount() + 1);
        } else {
            taskStatus.setRuleFailCount(taskStatus.getRuleFailCount() + 1);
        }

        taskExecutionRepository.updateById(taskStatus);

        log.info("Rule execution result recorded: taskId={}, ruleId={}, resourceId={}, success={}",
                taskId, ruleId, resource, result.isSuccess());
    }

    /**
     * 获取任务执行状态
     *
     * @param executionId 执行ID
     * @return 任务执行状态
     */
    @Override
    public TaskExecutionDO getTaskExecutionStatus(String executionId) {
        // 先从缓存中获取
        TaskExecutionDO cachedStatus = activeTaskExecutions.get(executionId);
        if (cachedStatus != null) {
            return cachedStatus;
        }

        // 缓存中没有，从数据库查询
        return taskExecutionRepository.getOne(
                Wrappers.lambdaQuery(TaskExecutionDO.class)
                        .eq(TaskExecutionDO::getExecutionId, executionId));
    }

    /**
     * 计算资源覆盖率
     *
     * @param taskId 任务ID
     * @return 资源覆盖率(%)
     */
    @Override
    public Integer calculateResourceCoverageRate(String taskId) {
        try {
            // 获取任务关联的所有资源
            List<Resource> resourceList = taskRuleResourceRepository.listByTaskId(taskId);

            if (CollectionUtils.isEmpty(resourceList)) {
                log.warn("No resources found for task: {}", taskId);
                return 0;
            }

            // 获取资源执行统计
            Map<String, ResourceExecutionStats> resourceStats = getResourceExecutionStatistics(taskId);

            // 计算有成功执行记录的资源数量
            long successfulResourceCount = resourceStats.values().stream()
                    .mapToLong(stats -> stats.getSuccessCount() > 0 ? 1 : 0)
                    .sum();

            // 计算覆盖率
            int coverageRate = (int) ((successfulResourceCount * 100) / resourceList.size());

            log.info("Resource coverage rate calculated for task {}: {}% ({}/{} resources)",
                    taskId, coverageRate, successfulResourceCount, resourceList.size());

            return coverageRate;

        } catch (Exception e) {
            log.error("Error calculating resource coverage rate for task: {}", taskId, e);
            return null;
        }
    }

    /**
     * 获取资源执行统计信息
     *
     * @param taskId 任务ID
     * @return 资源执行统计 (resourceId -> success/fail count)
     */
    @Override
    public Map<String, ResourceExecutionStats> getResourceExecutionStatistics(String taskId) {
        try {
            // 获取任务执行状态
            TaskExecutionDO taskStatus = getTaskExecutionStatus(taskId);
            if (taskStatus == null) {
                log.warn("Task status not found for taskId: {}", taskId);
                return new HashMap<>();
            }

            // 查询该任务的所有规则执行记录
            List<RuleExecutionDO> executionRecords = ruleExecutionRepository.list(
                    Wrappers.lambdaQuery(RuleExecutionDO.class)
                            .eq(RuleExecutionDO::getTaskExecutionId, taskStatus.getId()));

            // 按资源ID分组统计
            Map<String, ResourceExecutionStats> resourceStatsMap = new HashMap<>();

            for (RuleExecutionDO record : executionRecords) {
                String resource = record.getResource();
                if (!StringUtils.hasText(resource)) {
                    continue; // 跳过没有资源信息的记录
                }

                // 获取或创建资源统计对象
                ResourceExecutionStats stats = resourceStatsMap.computeIfAbsent(resource,
                        id -> new ResourceExecutionStats(id, record.getResourceName()));

                // 更新统计计数
                if ("1".equals(record.getExecutionStatus())) {
                    stats.incrementSuccess();
                } else {
                    stats.incrementFail();
                }
            }

            log.info("Resource execution statistics calculated for task {}: {} resources",
                    taskId, resourceStatsMap.size());

            return resourceStatsMap;

        } catch (Exception e) {
            log.error("Error getting resource execution statistics for task: {}", taskId, e);
            return new HashMap<>();
        }
    }
}
