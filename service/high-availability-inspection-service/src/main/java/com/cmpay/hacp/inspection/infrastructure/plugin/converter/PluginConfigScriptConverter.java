package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginConfigScript;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginOutputField;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptParameter;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginScriptDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginOutputFieldDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginScriptParameterDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface PluginConfigScriptConverter {
    
    @IgnoreAuditFields
    PluginScriptDO toPluginScriptDO(PluginConfigScript pluginConfigScript, String pluginId);

    @IgnoreAuditFields
    PluginOutputFieldDO toPluginScriptResultDO(PluginOutputField scriptResult, String pluginId);

    @IgnoreAuditFields
    PluginScriptParameterDO toPluginScriptParameterDO(PluginScriptParameter scriptParameter, String pluginId);
}
