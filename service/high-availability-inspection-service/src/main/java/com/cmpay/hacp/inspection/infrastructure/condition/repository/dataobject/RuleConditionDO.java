package com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseInsertDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则监控字段表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_rule_condition")
public class RuleConditionDO extends BaseInsertDO {

    /**
     * 条件组
     */
    private String conditionGroupId;

    /**
     * 监控字段（插件输出字段Name）
     */
    private String pluginOutputFiledName;

    /**
     * 判断条件(大小等于)
     * {@link RuleComparisonOperator}
     */
    private RuleComparisonOperator comparisonOperator;

    /**
     * 数值
     */
    private String comparisonValue;

}
