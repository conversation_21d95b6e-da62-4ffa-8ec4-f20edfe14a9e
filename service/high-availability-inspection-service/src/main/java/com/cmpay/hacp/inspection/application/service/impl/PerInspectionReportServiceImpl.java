package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.assembler.PerInspectionReportMapper;
import com.cmpay.hacp.inspection.application.service.PerInspectionReportService;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReportDetail;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDetailDO;
import com.cmpay.hacp.inspection.infrastructure.repository.PerInspectionReportDetailRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.PerInspectionReportRepository;
import com.cmpay.lemon.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 按次巡检报告查询服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PerInspectionReportServiceImpl implements PerInspectionReportService {

    private final PerInspectionReportRepository perInspectionReportRepository;
    private final PerInspectionReportDetailRepository perInspectionReportDetailRepository;
    private final PerInspectionReportMapper perInspectionReportMapper;

    @Override
    public IPage<PerInspectionReport> getReportPage(PageDTO<?> page, PerInspectionReport queryCondition) {
        // 转换领域对象为DO对象用于查询
        PerInspectionReportDO queryConditionDO = perInspectionReportMapper.toPerInspectionReportDO(queryCondition);

        // 构建查询条件
        LambdaQueryWrapper<PerInspectionReportDO> queryWrapper = Wrappers.lambdaQuery(PerInspectionReportDO.class);

        if (queryConditionDO != null) {
            // 报告ID模糊查询
            queryWrapper.notLike(StringUtils.isNotBlank(queryConditionDO.getReportId()),PerInspectionReportDO::getReportId, queryConditionDO.getReportId());

            // 任务ID精确查询
            queryWrapper.eq(StringUtils.isNotBlank(queryConditionDO.getTaskId()),PerInspectionReportDO::getTaskId, queryConditionDO.getTaskId());

            // 任务名称模糊查询
            queryWrapper.like(StringUtils.isNotBlank(queryConditionDO.getTaskName()),PerInspectionReportDO::getTaskName, queryConditionDO.getTaskName());

            // 触发方式查询
            queryWrapper.eq(queryConditionDO.getTriggerMode() != null,PerInspectionReportDO::getTriggerMode, queryConditionDO.getTriggerMode());

            // 执行状态查询
            queryWrapper.eq(queryConditionDO.getExecutionStatus() != null,PerInspectionReportDO::getExecutionStatus, queryConditionDO.getExecutionStatus());

            // 开始时间范围查询
            queryWrapper.ge(queryConditionDO.getStartTime() != null,PerInspectionReportDO::getStartTime, queryConditionDO.getStartTime());

            // 结束时间范围查询
            queryWrapper.le(queryConditionDO.getEndTime() != null,PerInspectionReportDO::getEndTime, queryConditionDO.getEndTime());
        }

        // 按生成时间倒序排列
        queryWrapper.orderByDesc(PerInspectionReportDO::getGenerateTime);

        // 执行分页查询
        Page<PerInspectionReportDO> pageParam = new Page<>(page.getCurrent(), page.getSize());
        IPage<PerInspectionReportDO> doResult = perInspectionReportRepository.page(pageParam, queryWrapper);

        // 转换DO结果为领域对象
        List<PerInspectionReport> domainRecords = perInspectionReportMapper.toPerInspectionReportList(doResult.getRecords());

        // 构建领域对象分页结果
        Page<PerInspectionReport> result = new Page<>(doResult.getCurrent(), doResult.getSize(), doResult.getTotal());
        result.setRecords(domainRecords);

        log.info("Found {} per-inspection reports", result.getTotal());
        return result;
    }

    @Override
    public PerInspectionReportDetail getReportDetailContent(String reportId) {
        log.info("Getting per-inspection report detail content for reportId: {}", reportId);

        if (StringUtils.isBlank(reportId)) {
            log.warn("Report ID is blank");
            return null;
        }

        PerInspectionReportDetailDO reportDetailDO = perInspectionReportDetailRepository.getOne(
                Wrappers.lambdaQuery(PerInspectionReportDetailDO.class)
                        .eq(PerInspectionReportDetailDO::getReportId, reportId)
        );

        if (reportDetailDO == null) {
            log.warn("Per-inspection report detail content not found for reportId: {}", reportId);
            return null;
        }

        // 转换DO为领域对象
        return perInspectionReportMapper.toPerInspectionReportDetail(reportDetailDO);
    }
}
