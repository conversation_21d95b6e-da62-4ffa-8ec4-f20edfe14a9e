package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginTagMapping;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreInsertAuditFields;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginTagDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PluginTagConverter {
    @IgnoreInsertAuditFields
    PluginTagDO toPluginTagDO(PluginTagMapping pluginTagMapping);

    List<PluginTagMapping> toPluginTagMappingList(List<PluginTagDO> list);
}
