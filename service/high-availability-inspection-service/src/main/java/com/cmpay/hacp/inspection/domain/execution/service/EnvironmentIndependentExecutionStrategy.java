package com.cmpay.hacp.inspection.domain.execution.service;

import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginRepository;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginMappingRepository;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 环境无关执行策略 - 领域服务
 * 处理系统指标类插件的执行
 */
@Service
public class EnvironmentIndependentExecutionStrategy extends AbstractRuleExecutionStrategy {

    private final PluginExecutionDomainService pluginExecutionDomainService;

    public EnvironmentIndependentExecutionStrategy(PluginExecutionDomainService pluginExecutionDomainService, RulePluginMappingRepository rulePluginMappingRepository, PluginRepository pluginRepository) {
        super(rulePluginMappingRepository, pluginRepository);
        this.pluginExecutionDomainService = pluginExecutionDomainService;
    }


    @Override
    protected void doExecute(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount) {
        // 执行系统指标检查
        pluginExecutionDomainService.executeSystemMetric(ruleExecution, successCount, failCount);

    }

    @Override
    public boolean supports(ResourceType resourceType) {
        return resourceType == ResourceType.ENVIRONMENT_INDEPENDENT;
    }
}
