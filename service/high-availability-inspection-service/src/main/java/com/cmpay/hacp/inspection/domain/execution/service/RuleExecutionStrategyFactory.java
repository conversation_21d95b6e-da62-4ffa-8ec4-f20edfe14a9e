package com.cmpay.hacp.inspection.domain.execution.service;

import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 规则执行策略工厂 - 领域服务
 */
@Service
public class RuleExecutionStrategyFactory {
    private final List<RuleExecutionStrategy> strategies;

    public RuleExecutionStrategyFactory(List<RuleExecutionStrategy> strategies) {
        this.strategies = strategies;
    }

    public RuleExecutionStrategy getStrategy(ResourceType resourceType) {
        return strategies.stream()
                .filter(strategy -> strategy.supports(resourceType))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("No strategy found for resource type: " + resourceType));
    }

}
