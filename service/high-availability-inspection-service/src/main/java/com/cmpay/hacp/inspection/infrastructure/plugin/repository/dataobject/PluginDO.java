package com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cmpay.hacp.inspection.domain.model.enums.DeployEnvEnum;
import com.cmpay.hacp.inspection.domain.model.enums.PluginSourceEnum;
import com.cmpay.hacp.inspection.domain.model.enums.PluginStatus;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 巡检插件实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "inspection_plugin",autoResultMap = true)
public class PluginDO extends BaseDO {
    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 插件名称
     */
    private String name;

    /**
     * 插件类型(SHELL、PYTHON等)
     */
    private PluginType type;

    /**
     * 插件来源(0内置，1自定义)
     */
    private PluginSourceEnum source;

    /**
     * 插件状态(0禁用，1启用)
     */
    private PluginStatus status;

    /**
     * 插件描述
     */
    private String description;

    /**
     * 支持环境
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<DeployEnvEnum> deployEnvs;
}
