package com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.ParamType;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检插件参数定义实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_plugin_script_parameter")
public class PluginScriptParameterDO extends BaseDO {
    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数类型(文本、数字、邮箱、URL、自定义正则等)
     */
    private ParamType paramType;

    /**
     * 正则表达式
     */
    private String regexPattern;

    /**
     * 参数值
     */
    private String paramValue;

    /**
     * 参数描述
     */
    private String paramDesc;

    /**
     * 是否加密(0不加密，1加密)
     */
    private Boolean isEncrypted;
}
