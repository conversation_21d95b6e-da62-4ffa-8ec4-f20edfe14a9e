package com.cmpay.hacp.inspection.domain.condition.model;

import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class RuleCondition {

    private String conditionGroupId;

    /**
     * 监控字段（插件输出字段Name）
     */
    private String pluginOutputFiledName;

    /**
     * @see RuleComparisonOperator
     */
    @Schema(description = "判断条件(1大于,2小于,3大于等于,4小于等于,5等于)", required = true, example = "1")
    @NotNull(message = "HAI50507")
    private RuleComparisonOperator comparisonOperator;

    @Schema(description = "值", required = true, example = "100")
    @NotNull(message = "HAI50508")
    private String comparisonValue;
}
