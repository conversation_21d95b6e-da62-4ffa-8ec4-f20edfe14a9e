package com.cmpay.hacp.inspection.infrastructure.task.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.task.repository.TaskExecutionRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.TaskExecutionMapper;
import com.cmpay.hacp.inspection.infrastructure.task.converter.TaskExecutionConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 任务执行状态Repository
 */
@Repository
@RequiredArgsConstructor
public class TaskExecutionRepositoryImpl implements TaskExecutionRepository {
    private final TaskExecutionMapper mapper;
    private final TaskExecutionConverter converter;

    @Override
    public List<String> listTaskIdByTaskStatus(ExecutionStatus executionStatus) {
        List<TaskExecutionDO> dos = mapper.selectList(
                Wrappers.lambdaQuery(TaskExecutionDO.class)
                        .select(TaskExecutionDO::getTaskId)
                        .eq(TaskExecutionDO::getExecutionStatus, executionStatus)
        );
        return dos.stream().map(TaskExecutionDO::getTaskId).collect(Collectors.toList());
    }

    @Override
    public ExecutionStatus queryStatusByTaskId(String taskId) {
        TaskExecutionDO taskExecutionDO = mapper.selectOne(Wrappers.lambdaQuery(TaskExecutionDO.class)
                .eq(TaskExecutionDO::getTaskId, taskId));

        return Optional.ofNullable(taskExecutionDO).map(TaskExecutionDO::getExecutionStatus).orElse(null);
    }

    @Override
    public void save(TaskExecutionDO taskExecution) {
        mapper.insert(taskExecution);
    }

    @Override
    public void updateById(TaskExecutionDO taskExecution) {
        mapper.updateById(taskExecution);
    }

    @Override
    public TaskExecutionDO getByExecutionId(String executionId) {
        return mapper.selectOne(Wrappers.lambdaQuery(TaskExecutionDO.class)
                .eq(TaskExecutionDO::getExecutionId, executionId));
    }

    @Override
    public TaskExecutionDO getLatestByTaskId(String taskId) {
        return mapper.selectOne(Wrappers.lambdaQuery(TaskExecutionDO.class)
                .eq(TaskExecutionDO::getTaskId, taskId)
                .orderByDesc(TaskExecutionDO::getCreatedTime)
                .last("LIMIT 1"));
    }
}
