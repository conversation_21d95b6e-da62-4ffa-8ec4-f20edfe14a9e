package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.service.TagService;
import com.cmpay.hacp.inspection.domain.tag.model.Tag;
import com.cmpay.hacp.inspection.domain.tag.repository.TagRepository;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 标签服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TagServiceImpl implements TagService {

    private final TagRepository tagRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTag(String name) {
        return tagRepository.save(name);
    }

    @Override
    public List<Tag> getTagByTagIds(List<Long> tagIds) {
        if(JudgeUtils.isEmpty(tagIds)){
            return new ArrayList<>();
        }
        return tagRepository.findTagByTagIds(tagIds);
    }

    @Override
    public Map<Long, String> pluginTags() {
        return tagRepository.findPluginTagAll();
    }

    @Override
    public Map<Long, String> ruleTags() {
        return tagRepository.findRuleTagAll();
    }

}
