package com.cmpay.hacp.inspection.infrastructure.rule.converter;

import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 巡检规则对象转换器
 * 用于领域对象和实体对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface InspectionRuleConverter {

    @IgnoreAuditFields
    @Mapping(target = "ruleId", ignore = true)
    RuleDO toInspectionRuleDO(InspectionRule queryCondition);

    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    @Mapping(target = "cacheKey", ignore = true)
    @Mapping(target = "tagIds", ignore = true)
    @Mapping(target = "pluginId", ignore = true)
    @Mapping(target = "pluginParams", ignore = true)
    @Mapping(target = "pluginResults", ignore = true)
    @Mapping(target = "pluginInfo", ignore = true)
    @Mapping(target = "auditInfo.createdBy", source = "ruleDO.createdByName")
    @Mapping(target = "auditInfo.createdTime", source = "ruleDO.createdTime")
    @Mapping(target = "auditInfo.updatedBy", source = "ruleDO.updatedByName")
    @Mapping(target = "auditInfo.updatedTime", source = "ruleDO.updatedTime")
    InspectionRule toInspectionRule(RuleDO ruleDO);

    List<InspectionRule> toInspectionRuleList(List<RuleDO> ruleDOList);

}
