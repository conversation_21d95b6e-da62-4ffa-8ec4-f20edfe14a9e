package com.cmpay.hacp.inspection.infrastructure.config;

import com.hazelcast.core.HazelcastInstance;
import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.hazelcast4.HazelcastLockProvider;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Shedlock 分布式锁配置
 * 使用 Hazelcast 作为锁提供者，确保定时任务在集群环境中只在一个节点上执行
 */
@Configuration
@EnableSchedulerLock(defaultLockAtMostFor = "10m")
public class ShedlockConfig {

    /**
     * 配置 Hazelcast 锁提供者
     * 
     * @param hazelcastInstance Hazelcast 实例
     * @return LockProvider 锁提供者
     */
    @Bean
    public LockProvider lockProvider(HazelcastInstance hazelcastInstance) {
        return new HazelcastLockProvider(hazelcastInstance);
    }
}
