package com.cmpay.hacp.inspection.domain.model.report;

import com.cmpay.hacp.inspection.domain.model.common.AuditInfo;
import lombok.Data;

import java.time.LocalDate;

/**
 * 按日巡检报告领域对象
 */
@Data
public class DailyInspectionReport {

    /**
     * 报告日期（YYYY-MM-DD）
     */
    private LocalDate reportDate;

    /**
     * 日报告编号（唯一标识，如：DR20250324）
     */
    private String reportId;

    /**
     * 规则数（显示在列表页）
     */
    private Integer ruleCount;

    /**
     * 执行数（总检查数，显示在列表页）
     */
    private Integer executionCount;

    /**
     * 告警数（显示在列表页）
     */
    private Integer warningCount;

    /**
     * 错误数（显示在列表页）
     */
    private Integer errorCount;

    /**
     * 通过率（百分比，如：92.2）
     */
    private Double passRate;

    /**
     * 通过率变化（相对昨日，百分点）
     * 正数表示上升，负数表示下降
     */
    private Double passRateChange;

    /**
     * 平均响应时间（毫秒）
     */
    private Long averageResponseTime;

    /**
     * 报告状态
     */
    private String reportStatus;
}
