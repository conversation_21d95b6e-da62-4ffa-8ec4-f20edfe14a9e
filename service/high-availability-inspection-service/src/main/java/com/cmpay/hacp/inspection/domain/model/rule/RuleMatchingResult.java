package com.cmpay.hacp.inspection.domain.model.rule;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 规则匹配结果
 */
@Data
@Builder
public class RuleMatchingResult {
    
    /**
     * 是否匹配成功（巡检通过）
     */
    private boolean success;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 治理建议
     */
    private String suggest;

    /**
     * 错误信息（如果匹配失败）
     */
    private String errorMessage;


    /**
     * 匹配详情信息
     */
    private String message;

    private List<RuleMatchingFieldResult> ruleMatchingFieldResults;

    @Data
    @Builder
    public static class RuleMatchingFieldResult {

        /**
         * 是否匹配成功（巡检通过）
         */
        private boolean success;

        /**
         * 匹配的字段名称
         */
        private String fieldName;

        /**
         * 实际值
         */
        private Object actualValue;

        /**
         * 期望值（规则条件值）
         */
        private String expectedValue;

        /**
         * 判断条件描述
         */
        private String condition;

        /**
         * 匹配详情信息
         */
        private String message;

        /**
         * 错误信息（如果匹配失败）
         */
        private String errorMessage;

        /**
         * 详细的字段匹配结果列表
         */
        private List<FieldMatchingDetail> fieldMatchingDetails;
    }

    /**
     * 字段匹配详情
     */
    @Data
    @Builder
    public static class FieldMatchingDetail {
        /**
         * 字段名称
         */
        private String fieldName;
        
        /**
         * 实际值
         */
        private Object actualValue;
        
        /**
         * 期望值
         */
        private String expectedValue;
        
        /**
         * 判断条件
         */
        private String condition;
        
        /**
         * 是否匹配
         */
        private boolean matched;
        
        /**
         * 匹配信息
         */
        private String message;
    }
}
