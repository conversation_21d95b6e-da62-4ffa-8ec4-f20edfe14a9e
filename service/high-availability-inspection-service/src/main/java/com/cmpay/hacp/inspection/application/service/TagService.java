package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.domain.tag.model.Tag;

import java.util.List;
import java.util.Map;

/**
 * 标签服务接口
 */
public interface TagService {

    /**
     * 创建标签
     *
     * @param name 标签信息
     * @return 创建的标签ID
     */
    Long createTag(String name);


    List<Tag> getTagByTagIds(List<Long> tagIds);

    Map<Long, String> pluginTags();

    Map<Long, String> ruleTags();

}
