package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 仅检查峰值
 */
@Getter
@Slf4j
public enum RuleCheckPeakEnum {

    NO(0, "否"),
    YES(1, "是");

    @JsonValue
    private final Integer code;
    private final String desc;

    RuleCheckPeakEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<Integer, RuleCheckPeakEnum> ENUM_MAP = Arrays.stream(RuleCheckPeakEnum.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static RuleCheckPeakEnum getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
