package com.cmpay.hacp.inspection.domain.plugin.valueobject;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "指标类型枚举")
public enum IndicatorType {
    @Schema(description = "中间件指标")
    MIDDLEWARE("mid", "中间件"),

    @Schema(description = "主机指标")
    HOST("host", "主机"),

    @Schema(description = "容器指标")
    CONTAINER("container", "容器");

    @JsonValue
    @Schema(description = "指标类型代码", example = "mid")
    private final String code;

    @Schema(description = "指标类型描述", example = "中间件")
    private final String desc;

    IndicatorType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}