package com.cmpay.hacp.inspection.application.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;

import java.util.List;

public interface InspectionRuleService {


    IPage<InspectionRule> getRulePage(PageDTO<?> page, InspectionRule queryCondition);

    String createRule(InspectionRule inspectionRule);

    boolean updateRule(InspectionRule inspectionRule);

    void deleteRule(String ruleId);

    InspectionRule getRuleDetail(String ruleId);

    List<InspectionRule> getRuleList(InspectionRule queryCondition);
}
