package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptParameter;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

public interface PluginScriptParameterRepository {
    void removeByPluginId(@NotNull String pluginId);
    
    void saveBatch(List<PluginScriptParameter> parameters, @NotNull String pluginId);

    List<PluginScriptParameter> listByPluginId(@NotNull String pluginId);

    void removeByPluginIdAndParamNames(@NotNull String pluginId, List<String> deleteParamNames);

    void updateBatchByPluginIdAndParamName(List<PluginScriptParameter> parameters, @NotNull String pluginId);

    Map<String, Object> getParameterMapByPluginId(@NotNull String pluginId);
}
