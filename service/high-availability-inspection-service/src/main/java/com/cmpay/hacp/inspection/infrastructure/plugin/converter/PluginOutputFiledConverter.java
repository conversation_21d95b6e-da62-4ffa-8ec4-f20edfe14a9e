package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginOutputField;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginOutputFieldDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PluginOutputFiledConverter {
    @IgnoreAuditFields
    PluginOutputFieldDO toPluginOutputFieldDO(PluginOutputField scriptResult, String pluginId);

    List<PluginOutputField> toPluginOutputFieldList(List<PluginOutputFieldDO> list);

    PluginOutputField toPluginOutputField(PluginOutputFieldDO one);
}
