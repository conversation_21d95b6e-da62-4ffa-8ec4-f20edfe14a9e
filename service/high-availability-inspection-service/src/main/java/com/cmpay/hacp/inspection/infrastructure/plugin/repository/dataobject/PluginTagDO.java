package com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseInsertDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 插件标签关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_plugin_tag")
public class PluginTagDO extends BaseInsertDO {
    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 标签ID
     */
    private Long tagId;
}
