package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.cmpay.hacp.inspection.domain.plugin.model.*;
import org.springframework.beans.factory.InitializingBean;

import javax.validation.constraints.NotNull;

/**
 * <em style="color:#FF0000">不要直接使用，请使用PluginConfigStrategyFactory获取实例</em>
 * <br>
 * 插件配置仓库接口
 */
public interface PluginConfigRepository extends InitializingBean {

    void remove(@NotNull String pluginId);

    void savePluginConfig(InspectionPlugin inspectionPlugin,@NotNull String pluginId);

    void updatePluginConfig(InspectionPlugin inspectionPlugin,@NotNull String pluginId);

    PluginConfig queryPluginConfig(@NotNull String pluginId);

}
