package com.cmpay.hacp.inspection.domain.tag.repository;

import com.cmpay.hacp.inspection.domain.tag.model.Tag;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

public interface TagRepository {

    Long save(@NotNull String name);

    Map<Long, String> findPluginTagAll();

    Map<Long, String> findRuleTagAll();

    List<Tag> findTagByTagIds(@NotNull List<Long> tagIds);
}
