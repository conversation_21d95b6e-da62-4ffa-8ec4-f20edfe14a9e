package com.cmpay.hacp.inspection.domain.task.repository;

import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.task.model.Resource;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface TaskRuleResourceRepository {
    List<Resource> list(@NotNull String taskId, @NotNull String ruleId);

    List<Resource> listByTaskId(@NotNull String taskId);

    void saveBatch(@NotNull InspectionTask inspectionTask);

    void deleteByTaskId(@NotNull String taskId);
}
