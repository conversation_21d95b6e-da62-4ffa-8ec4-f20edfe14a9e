package com.cmpay.hacp.inspection.infrastructure.condition.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionGroupInfoDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface RuleConditionGroupInfoMapper extends BaseMapper<RuleConditionGroupInfoDO> {
    // 可添加自定义 SQL 方法
}
