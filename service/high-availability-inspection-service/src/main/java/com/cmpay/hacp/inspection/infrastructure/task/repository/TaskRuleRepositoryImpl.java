package com.cmpay.hacp.inspection.infrastructure.task.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.task.model.TaskRuleMapping;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRuleRepository;
import com.cmpay.hacp.inspection.infrastructure.task.converter.TaskRuleConverter;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskRuleDO;
import com.cmpay.hacp.inspection.infrastructure.task.repository.mapper.TaskRuleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class TaskRuleRepositoryImpl extends CrudRepository<TaskRuleMapper, TaskRuleDO> implements TaskRuleRepository {
    private final TaskRuleConverter taskRuleConverter;

    @Override
    public List<TaskRuleMapping> list(String taskId) {
        return taskRuleConverter.toTaskRuleMappingList(this.list(
                Wrappers.lambdaQuery(TaskRuleDO.class)
                        .eq(TaskRuleDO::getTaskId, taskId)));
    }

    @Override
    public List<TaskRuleMapping> listByTaskIds(List<String> taskIds) {
        return taskRuleConverter.toTaskRuleMappingList(this.list(
                Wrappers.lambdaQuery(TaskRuleDO.class)
                        .in(TaskRuleDO::getTaskId, taskIds)));
    }

    @Override
    public void saveBatch(List<TaskRuleMapping> taskRuleMappingList){
        if (taskRuleMappingList == null || taskRuleMappingList.isEmpty()) {
            return;
        }

        List<TaskRuleDO> results = taskRuleMappingList.stream()
                .map(taskRuleConverter::toTaskRuleDO)
                .collect(Collectors.toList());

        this.saveBatch(results, 10);
    }

    @Override
    public boolean removeByTaskId(String taskId) {
        return this.remove(Wrappers.lambdaUpdate(TaskRuleDO.class)
                .eq(TaskRuleDO::getTaskId, taskId));
    }
}
