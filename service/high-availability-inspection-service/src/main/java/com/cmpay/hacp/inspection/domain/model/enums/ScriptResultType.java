package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 脚本输出类型
 */
@Getter
public enum ScriptResultType {
    STRUCTURED(1, "结构化"),
    TEXT(2, "纯文本");

    @JsonValue
    private final Integer code;
    private final String desc;

    ScriptResultType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
