package com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.PluginOutputFieldType;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检插件脚本结构化输出字段定义
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_plugin_output_filed")
public class PluginOutputFieldDO extends BaseDO {
    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 字段名称(如cpu.usage)
     */
    private String fieldName;

    /**
     * 取值路径 JSONPath
     */
    private String fieldPath;

    /**
     * 示例值
     */
    private String exampleValue;

    /**
     * 单位(如%)
     */
    private String fieldUnit;

    /**
     * 字段类型(0:数值型, 1:字符串型, 2:布尔型)
     */
    private PluginOutputFieldType fieldType;

    /**
     * 描述
     */
    private String description;
}
