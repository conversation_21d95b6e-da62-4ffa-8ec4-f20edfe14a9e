package com.cmpay.hacp.inspection.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig {

    @Bean("asyncInspectionTaskExecutor")
    public Executor asyncInspectionTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("Task-");
        executor.initialize();
        return executor;
    }

    @Bean("asyncInspectionRuleExecutor")
    public Executor asyncInspectionRuleExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(40);
        executor.setThreadNamePrefix("Rule-");
        executor.initialize();
        return executor;
    }

    @Bean("asyncInspectionHostExecutor")
    public Executor asyncInspectionHostExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(40);
        executor.setThreadNamePrefix("Host-");
        executor.initialize();
        return executor;
    }

    @Bean("asyncInspectionReportExecutor")
    public Executor asyncInspectionReportExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setThreadNamePrefix("Report-");
        executor.initialize();
        return executor;
    }
}
