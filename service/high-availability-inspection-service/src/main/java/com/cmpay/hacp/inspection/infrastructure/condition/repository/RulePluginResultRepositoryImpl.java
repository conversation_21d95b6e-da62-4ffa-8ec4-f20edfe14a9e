package com.cmpay.hacp.inspection.infrastructure.condition.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.domain.condition.model.RuleConditionGroupInfo;
import com.cmpay.hacp.inspection.domain.condition.model.RulePluginResult;
import com.cmpay.hacp.inspection.domain.condition.repository.RulePluginResultRepository;
import com.cmpay.hacp.inspection.domain.model.enums.ConditionLogic;
import com.cmpay.hacp.inspection.infrastructure.condition.converter.RuleConditionConverter;
import com.cmpay.hacp.inspection.infrastructure.condition.converter.RuleConditionGroupInfoConverter;
import com.cmpay.hacp.inspection.infrastructure.condition.converter.RulePluginResultConverter;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionDO;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionGroupDO;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionGroupInfoDO;
import com.cmpay.hacp.inspection.infrastructure.utils.IdGenUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@Slf4j
@RequiredArgsConstructor
public class RulePluginResultRepositoryImpl implements RulePluginResultRepository {

    private final RuleConditionGroupRepository ruleConditionGroupRepository;
    private final RuleConditionGroupInfoRepository ruleConditionGroupInfoRepository;
    private final RuleConditionRepository ruleConditionRepository;
    private final RulePluginResultConverter rulePluginResultConverter;
    private final RuleConditionGroupInfoConverter ruleConditionGroupInfoConverter;
    private final RuleConditionConverter ruleConditionConverter;

    @Override
    public List<RulePluginResult> findByRuleId(String ruleId) {
        return this.findByRuleIds(Collections.singletonList(ruleId));
    }

    @Override
    public RulePluginResult findByRuleId(String ruleId,String pluginId) {
        List<RulePluginResult> results = this.findByRuleIds(Collections.singletonList(ruleId), Collections.singletonList(pluginId));
        if(JudgeUtils.isEmpty(results)){
            return null;
        }
        return results.get(0);
    }

    @Override
    public List<RulePluginResult> findByRuleIds(List<String> ruleIds){
        return this.findByRuleIds(ruleIds,null);
    }

    @Override
    public List<RulePluginResult> findByRuleIds(List<String> ruleIds,List<String> pluginIds) {
        if(JudgeUtils.isEmpty(ruleIds)){
            log.warn("ruleIds is empty");
            return null;
        }
        List<RuleConditionGroupDO> ruleConditionsGroupDOList = ruleConditionGroupRepository.list(Wrappers.lambdaQuery(RuleConditionGroupDO.class)
                .in(RuleConditionGroupDO::getRuleId, ruleIds)
                .in(CollectionUtils.isNotEmpty(pluginIds),RuleConditionGroupDO::getPluginId,pluginIds ));

        if(JudgeUtils.isEmpty(ruleConditionsGroupDOList)){
            return null;
        }

        List<String> groupIds = ruleConditionsGroupDOList.stream()
                .map(RuleConditionGroupDO::getConditionGroupId)
                .collect(Collectors.toList());
        List<RuleConditionGroupInfoDO> conditionGroupInfoDOList =  ruleConditionGroupInfoRepository.list(Wrappers.lambdaQuery(RuleConditionGroupInfoDO.class)
                .in(RuleConditionGroupInfoDO::getConditionGroupId, groupIds));
        List<RuleConditionDO> conditionDOList =  ruleConditionRepository.list(Wrappers.lambdaQuery(RuleConditionDO.class)
                .in(RuleConditionDO::getConditionGroupId, groupIds));

        Map<String, List<RuleConditionGroupInfoDO>> conditionGroupInfoMap =  conditionGroupInfoDOList.stream()
                .collect(Collectors.groupingBy(RuleConditionGroupInfoDO::getConditionGroupId));
        Map<String, List<RuleConditionDO>> conditionMap =  conditionDOList.stream()
                .collect(Collectors.groupingBy(RuleConditionDO::getConditionGroupId));
        return ruleConditionsGroupDOList.stream().map(m -> rulePluginResultConverter.toRulePluginResult(m,
                Optional.ofNullable(conditionGroupInfoMap.getOrDefault(m.getConditionGroupId(),null)).map(v->v.get(0)).orElse(null),
                conditionMap.get(m.getConditionGroupId()))).collect(Collectors.toList());
    }

    @Override
    public void removeByRuleId(String ruleId) {
        RuleConditionGroupDO ruleConditionGroupDO = ruleConditionGroupRepository.getOne(Wrappers.lambdaQuery(RuleConditionGroupDO.class)
                .eq(RuleConditionGroupDO::getRuleId, ruleId));
        if(JudgeUtils.isNull(ruleConditionGroupDO)){
            return;
        }
        ruleConditionRepository.remove(Wrappers.lambdaUpdate(RuleConditionDO.class)
                .eq(RuleConditionDO::getConditionGroupId, ruleConditionGroupDO.getConditionGroupId()));
        ruleConditionGroupInfoRepository.remove(Wrappers.lambdaUpdate(RuleConditionGroupInfoDO.class)
                .eq(RuleConditionGroupInfoDO::getConditionGroupId, ruleConditionGroupDO.getConditionGroupId()));
        ruleConditionGroupRepository.remove(Wrappers.lambdaUpdate(RuleConditionGroupDO.class)
                .eq(RuleConditionGroupDO::getConditionGroupId, ruleConditionGroupDO.getConditionGroupId()));
    }

    @Override
    public void saveBatch(List<RulePluginResult> pluginResults, String ruleId) {
        if(JudgeUtils.isEmpty(pluginResults)){
            return;
        }

        pluginResults.forEach(f -> {
            f.setConditionGroupId(IdGenUtil.generateTempId());
            f.setRuleId(ruleId);
            RuleConditionGroupDO entity = rulePluginResultConverter.toRuleConditionGroupDO(f);
            ruleConditionGroupRepository.save(entity);
            entity.setConditionGroupId(IdGenUtil.generateRuleGroupId(entity.getId()));
            ruleConditionGroupRepository.updateById(entity);

            RuleConditionGroupInfo ruleConditionGroupInfo = f.getRuleConditionInfo();
            RuleConditionGroupInfoDO ruleConditionInfoDO = ruleConditionGroupInfoConverter.toRuleConditionInfoDO(ruleConditionGroupInfo);
            ruleConditionInfoDO.setConditionGroupId(entity.getConditionGroupId());
            ruleConditionInfoDO.setConditionLogic(Optional.ofNullable(ruleConditionGroupInfo.getConditionLogic()).orElse(ConditionLogic.OR));
            ruleConditionGroupInfoRepository.save(ruleConditionInfoDO);
            List<RuleConditionDO> ruleConditionDOS = ruleConditionConverter.toRuleConditionDOList(f.getRuleConditions())
                    .stream()
                    .peek(m -> m.setConditionGroupId(entity.getConditionGroupId()))
                    .collect(Collectors.toList());
            ruleConditionRepository.saveBatch(ruleConditionDOS,20);
        });
    }
}
