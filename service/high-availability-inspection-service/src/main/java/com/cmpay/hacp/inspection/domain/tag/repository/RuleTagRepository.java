package com.cmpay.hacp.inspection.domain.tag.repository;

import com.cmpay.hacp.inspection.infrastructure.tag.repository.dataobject.RuleTagDO;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface RuleTagRepository {
    List<RuleTagDO> listAll();

    List<RuleTagDO> listByTagIds(List<Long> tagIds);

    List<RuleTagDO> listByRuleIds(List<String> ruleIds);

    void removeByRuleId(@NotNull String ruleId);

    List<RuleTagDO> listByRuleId(@NotNull String ruleId);

    void removeByTagIds(List<Long> tagIds);

    void saveBatch(List<RuleTagDO> tagsToInsert);

    void updateLinkRuleId(@NotNull String ruleId, List<Long> tagIds);

    void saveLinkRuleId(@NotNull String ruleId, List<Long> tagIds);
}
