package com.cmpay.hacp.inspection.infrastructure.rule.converter;

import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RulePluginParamDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RulePluginParamConverter {

    RulePluginParam toRulePluginParam(RulePluginParamDO rulePluginParamDO);
    List<RulePluginParam> toRulePluginParamList(List<RulePluginParamDO> rulePluginParamDO);

    @IgnoreAuditFields
    @Mapping(target = "ruleId", source = "ruleId")
    RulePluginParamDO toRulePluginParamDO(RulePluginParam rulePluginParam, String ruleId);
}
