package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.service.InspectionTaskExecutionService;
import com.cmpay.hacp.inspection.application.service.InspectionTaskMonitoringService;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import com.cmpay.hacp.inspection.domain.task.model.Task;
import com.cmpay.hacp.inspection.domain.task.model.TaskRuleMapping;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRepository;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRuleRepository;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 巡检执行服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionTaskExecutionServiceImpl implements InspectionTaskExecutionService {
    private final TaskRepository taskRepository;
    private final TaskRuleRepository taskRuleRepository;
    private final InspectionTaskMonitoringService monitoringService;
    private final AsyncTaskExecutionService asyncTaskExecutionService;

    @Override
    public void executeTask(String taskId, TriggerMode triggerMode) {
        // 获取任务信息
        Task task = taskRepository.getByTaskId(taskId);
        if (task == null) {
            log.error("Cannot execute task: Task not found with ID {}", taskId);
            BusinessException.throwBusinessException(ErrorCodeEnum.TASK_NAME_NOT_EXIST);
        }

        // 获取任务关联的规则列表
        List<TaskRuleMapping> taskRuleMappingList = taskRuleRepository.list(taskId);


        if (taskRuleMappingList.isEmpty()) {
            log.warn("No rules found for task: {}", taskId);
            return;
        }

        // 初始化任务执行状态
        String executionId = monitoringService.initializeTaskExecution(taskId, task.getName(), triggerMode, taskRuleMappingList.size());
        monitoringService.markTaskAsRunning(executionId);

        // 异步执行任务
        asyncTaskExecutionService.executeTaskAsync(taskId, TenantUtils.getWorkspaceId(), executionId, taskRuleMappingList);

        log.info("Task execution initiated asynchronously: {}", taskId);
    }

}
