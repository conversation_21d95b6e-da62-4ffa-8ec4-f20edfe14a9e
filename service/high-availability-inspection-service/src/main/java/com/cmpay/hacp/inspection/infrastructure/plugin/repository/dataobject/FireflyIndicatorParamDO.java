package com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Firefly指标参数配置数据对象
 */
@Data
@TableName("firefly_indicator_param")
public class FireflyIndicatorParamDO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 指标ID
     */
    private String indicatorId;

    /**
     * 参数顺序：1-10
     */
    private Integer paramOrder;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数代码
     */
    private String paramCode;

    /**
     * 是否必填：0-否，1-是
     */
    private Integer isRequired;

    /**
     * 参数描述
     */
    private String description;
}
