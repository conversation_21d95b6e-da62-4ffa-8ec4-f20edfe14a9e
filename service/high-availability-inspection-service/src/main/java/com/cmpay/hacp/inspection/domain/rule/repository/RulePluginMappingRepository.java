package com.cmpay.hacp.inspection.domain.rule.repository;

import com.cmpay.hacp.inspection.domain.rule.model.RulePluginMapping;

import javax.validation.constraints.NotNull;

public interface RulePluginMappingRepository {
    RulePluginMapping findByRuleId(@NotNull String ruleId);

    void save(RulePluginMapping rulePluginMapping);

    void updatePluginId(@NotNull String ruleId, String pluginId);


    boolean removeByRuleId(@NotNull String ruleId);
}
