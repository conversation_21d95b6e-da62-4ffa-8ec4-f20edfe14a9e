package com.cmpay.hacp.inspection.infrastructure.task.converter;

import com.cmpay.hacp.inspection.domain.task.model.TaskRuleMapping;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskRuleDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TaskRuleConverter {
    List<TaskRuleMapping> toTaskRuleMappingList(List<TaskRuleDO> list);

    @IgnoreAuditFields
    TaskRuleDO toTaskRuleDO(TaskRuleMapping taskRuleMapping);
}
