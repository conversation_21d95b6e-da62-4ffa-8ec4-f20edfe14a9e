package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
@Slf4j
public enum ClassType {


    STRING(1, "字符串",String.class ),
    INTEGER(2, "数值型",Integer.class ),
    BOOLEAN(3, "布尔型",Boolean.class ),
    Float(4, "浮点型",Float.class ),
    ;

    @JsonValue
    private final Integer code;
    private final String desc;
    private final Class<?> clazz;

    ClassType(Integer code, String desc,Class<?> clazz) {
        this.code = code;
        this.desc = desc;
        this.clazz = clazz;
    }


    private static final Map<Integer, ClassType> ENUM_MAP = Arrays.stream(ClassType.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static ClassType getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
