package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.model.task.TaskRuleExecution;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskDO;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskRuleDO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring")
public interface InspectionTaskMapper {
    @IgnoreAuditFields
    TaskDO toInspectionTaskDO(InspectionTask inspectionTask);

    @Mapping(target = "taskRuleExecutions", ignore = true)
    @Mapping(target = "scheduleConfig", ignore = true)
    @Mapping(target = "executionStatus", ignore = true)
    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    @Mapping(target = "executionPlan", ignore = true)
    @Mapping(target = "latestExecutionTime", ignore = true)
    @Mapping(target = "auditInfo", ignore = true)
    @Mapping(target = "alarmNotification", ignore = true)
    InspectionTask toInspectionTask(TaskDO taskDO);

//    @Mapping(target = "taskRuleExecutions", ignore = true)
//    @Mapping(target = "auditInfo.createdBy", source = "inspectionTaskDO.createdByName")
//    @Mapping(target = "auditInfo.createdTime", source = "inspectionTaskDO.createdTime")
//    @Mapping(target = "auditInfo.updatedBy", source = "inspectionTaskDO.updatedBy")
//    @Mapping(target = "auditInfo.updatedTime", source = "inspectionTaskDO.updatedTime")
//    @Mapping(target = "scheduleConfig.enabled", source = "taskScheduleConfigDO.enabled")
//    InspectionTask toInspectionTask(InspectionTaskDO inspectionTaskDO, TaskScheduleConfigDO taskScheduleConfigDO);


    @BeanMapping(unmappedTargetPolicy = ReportingPolicy.IGNORE)
    @Mapping(target = "ruleName", ignore = true)
    @Mapping(target = "targetEnvironmentId", ignore = true)
    @Mapping(target = "taskId", source = "taskId")
    @Mapping(target = "ruleId", source = "ruleId")
    List<TaskRuleExecution> toTaskRuleExecutions(List<TaskRuleDO> taskRuleDOS);

}
