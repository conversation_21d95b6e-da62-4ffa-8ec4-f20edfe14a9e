package com.cmpay.hacp.inspection.domain.condition.model;

import lombok.Data;

import java.util.List;

/**
 * 规则监控字段
 */
@Data
public class RulePluginResult {

    private String ruleId;

    private String pluginId;

    private String conditionGroupId;

    /**
     * 规则条件的公共信息
     */
    private RuleConditionGroupInfo ruleConditionInfo;

    /**
     * 规则条件
     */
    private List<RuleCondition> ruleConditions;

}
