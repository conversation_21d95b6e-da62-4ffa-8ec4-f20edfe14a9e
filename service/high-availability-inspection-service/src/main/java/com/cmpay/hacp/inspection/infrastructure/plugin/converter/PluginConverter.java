package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginDO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring")
public interface PluginConverter {

    @BeanMapping(unmappedTargetPolicy = ReportingPolicy.IGNORE)
    PluginDO toPluginDO(InspectionPlugin inspectionPlugin);

    @Mapping(target = "pluginConfig", ignore = true)
    @Mapping(target = "tagIds", ignore = true)
    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    @Mapping(target = "key", ignore = true)
    @Mapping(target = "auditInfo.createdBy", source = "pluginDO.createdByName")
    @Mapping(target = "auditInfo.createdTime", source = "pluginDO.createdTime")
    @Mapping(target = "auditInfo.updatedBy", source = "pluginDO.updatedByName")
    @Mapping(target = "auditInfo.updatedTime", source = "pluginDO.updatedTime")
    InspectionPlugin toInspectionPlugin(PluginDO pluginDO);
}
