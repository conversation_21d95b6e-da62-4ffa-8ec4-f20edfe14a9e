package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 触发方式
 */
@Getter
public enum TriggerMode {
    SCHEDULED(0, "定时触发"),
    MANUAL(1, "手动触发");

    @JsonValue
    private final Integer code;
    private final String desc;

    TriggerMode(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
