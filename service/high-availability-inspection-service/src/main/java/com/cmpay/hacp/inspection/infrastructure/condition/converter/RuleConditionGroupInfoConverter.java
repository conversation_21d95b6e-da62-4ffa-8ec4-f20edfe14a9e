package com.cmpay.hacp.inspection.infrastructure.condition.converter;

import com.cmpay.hacp.inspection.domain.condition.model.RuleConditionGroupInfo;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject.RuleConditionGroupInfoDO;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreInsertAuditFields;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface RuleConditionGroupInfoConverter {

    RuleConditionGroupInfo toRuleConditionInfo(RuleConditionGroupInfoDO reqDTO);

    @IgnoreInsertAuditFields
    RuleConditionGroupInfoDO toRuleConditionInfoDO(RuleConditionGroupInfo ruleConditionGroupInfo);
}