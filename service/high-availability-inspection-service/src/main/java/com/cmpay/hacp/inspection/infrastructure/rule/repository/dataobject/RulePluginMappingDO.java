package com.cmpay.hacp.inspection.infrastructure.rule.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则插件关联表实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_rule_plugin_mapping")
public class RulePluginMappingDO extends BaseDO {
    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 插件ID
     */
    private String pluginId;

}
