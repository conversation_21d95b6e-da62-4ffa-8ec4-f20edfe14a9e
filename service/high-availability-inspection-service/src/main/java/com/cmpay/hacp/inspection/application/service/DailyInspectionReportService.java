package com.cmpay.hacp.inspection.application.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReportDetail;

/**
 * 按日巡检报告查询服务接口
 * 负责查询和展示按日巡检报告数据（基于已生成的按日汇总表）
 */
public interface DailyInspectionReportService {

    /**
     * 分页查询按日巡检报告列表
     *
     * @param page 分页参数
     * @param queryCondition 查询条件
     * @return 分页结果
     */
    IPage<DailyInspectionReport> getReportPage(PageDTO<?> page, DailyInspectionReport queryCondition);

    /**
     * 根据报告ID获取按日巡检报告详细内容
     *
     * @param reportId 报告ID
     * @return 报告详细内容
     */
    DailyInspectionReportDetail getReportDetailContent(String reportId);
}
