package com.cmpay.hacp.inspection.infrastructure.scheduler;

import com.cmpay.hacp.inspection.domain.model.task.CronScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.FixedTimeScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.IntervalScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.ScheduleConfig;
import org.quartz.CronScheduleBuilder;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;

import java.time.ZoneId;
import java.util.Date;

public class ScheduleConfigQuartzAdapter {
    public static Trigger createTrigger(String jobName, ScheduleConfig config) {
        if (!config.isEnabled()) {
            throw new IllegalArgumentException("Cannot create trigger for disabled schedule");
        }

        TriggerBuilder<Trigger> triggerBuilder = TriggerBuilder.newTrigger()
                .withIdentity(jobName, "inspection-triggers");

        if (config instanceof CronScheduleConfig) {
            return triggerBuilder
                    .withSchedule(CronScheduleBuilder.cronSchedule(
                            ((CronScheduleConfig) config).getCronExpression()))
                    .build();
        }
        else if (config instanceof IntervalScheduleConfig) {
            IntervalScheduleConfig intervalConfig = (IntervalScheduleConfig) config;
            SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule();

            switch (intervalConfig.getIntervalUnit()) {
                case MINUTE:
                    scheduleBuilder.withIntervalInMinutes(intervalConfig.getIntervalValue());
                    break;
                case HOUR:
                    scheduleBuilder.withIntervalInHours(intervalConfig.getIntervalValue());
                    break;
                case DAY:
                    scheduleBuilder.withIntervalInHours(24 * intervalConfig.getIntervalValue());
                    break;
            }

            return triggerBuilder
                    .withSchedule(scheduleBuilder.repeatForever())
                    .build();
        }
        else if (config instanceof FixedTimeScheduleConfig) {
            FixedTimeScheduleConfig fixedConfig = (FixedTimeScheduleConfig) config;
            Date triggerDate = Date.from(fixedConfig.getExecutionDateTime().atZone(ZoneId.systemDefault()).toInstant());

            return triggerBuilder
                    .startAt(triggerDate)
                    .build();
        }

        throw new IllegalArgumentException("Unsupported schedule config type: " + config.getClass());
    }
}
