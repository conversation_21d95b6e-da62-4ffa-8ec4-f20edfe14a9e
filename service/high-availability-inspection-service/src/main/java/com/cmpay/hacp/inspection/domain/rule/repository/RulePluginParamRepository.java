package com.cmpay.hacp.inspection.domain.rule.repository;

import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface RulePluginParamRepository {
    List<RulePluginParam> findByRuleId(@NotNull String ruleId);

    List<RulePluginParam> findByRuleIds(List<String> ruleIds);

    void removeByRuleIdAndPluginId(@NotNull String ruleId,@NotNull String pluginId);

    void saveBatch(List<RulePluginParam> pluginParams, String ruleId, String sm4Key);

    void removeByRuleId(@NotNull String ruleId);
}
