package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginOutputField;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface PluginOutputFiledRepository {
    void saveBatch(List<PluginOutputField> pluginOutputFieldList, @NotNull String pluginId);

    List<PluginOutputField> listByPluginId(@NotNull String pluginId);

    void updateBatchByName(List<PluginOutputField> pluginOutputFieldList, @NotNull String pluginId);

    void removeByFieldNames(List<String> fieldNames, @NotNull String pluginId);

    void removeByPluginId(@NotNull String pluginId);

    PluginOutputField queryByFieldName(@NotNull String fieldName, @NotNull String pluginId);
}
