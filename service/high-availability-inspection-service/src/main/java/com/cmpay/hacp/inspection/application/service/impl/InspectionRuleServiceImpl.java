package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.service.InspectionCipherService;
import com.cmpay.hacp.inspection.application.service.InspectionRuleService;
import com.cmpay.hacp.inspection.domain.condition.model.RulePluginResult;
import com.cmpay.hacp.inspection.domain.condition.repository.RulePluginResultRepository;
import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginRepository;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginMapping;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginMappingRepository;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginParamRepository;
import com.cmpay.hacp.inspection.domain.rule.repository.RuleRepository;
import com.cmpay.hacp.inspection.domain.tag.repository.RuleTagRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleDO;
import com.cmpay.hacp.inspection.infrastructure.tag.repository.dataobject.RuleTagDO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionRuleServiceImpl implements InspectionRuleService {

    private final PluginRepository pluginRepository;
    private final RuleRepository ruleRepository;
    private final RuleTagRepository ruleTagRepository;
    private final RulePluginMappingRepository rulePluginMappingRepository;
    private final RulePluginParamRepository rulePluginParamRepository;
    private final RulePluginResultRepository rulePluginResultRepository;

    private final InspectionCipherService inspectionCipherService;


    @Override
    public IPage<InspectionRule> getRulePage(PageDTO<?> page, InspectionRule queryCondition) {
        Set<String> ruleIdSet = Collections.emptySet();
        if (JudgeUtils.isNotEmpty(queryCondition.getTagIds())) {
            List<RuleTagDO> list = ruleTagRepository.listByTagIds(queryCondition.getTagIds());
            if (JudgeUtils.isEmpty(list)) {
                return new Page<>();
            }
            ruleIdSet = list.stream().map(RuleTagDO::getRuleId).collect(Collectors.toSet());
        }

        // 构建MyBatis-Plus分页对象
        Page<RuleDO> mpPage = new Page<>(page.getCurrent(), page.getSize());

        // 执行分页查询
        IPage<InspectionRule> ruleIPage = ruleRepository.page(mpPage, queryCondition,ruleIdSet);


        // 处理关联数据
        if (!ruleIPage.getRecords().isEmpty()) {
            enrichInspectionRules(ruleIPage.getRecords());
        }

        return ruleIPage;
    }

    private void enrichInspectionRules(List<InspectionRule> records) {
        List<String> ruleIds = records.stream()
                .map(InspectionRule::getRuleId)
                .collect(Collectors.toList());

        // 批量查询关联数据
        List<RuleTagDO> tagsDOS = ruleTagRepository.listByRuleIds(ruleIds);


        List<RulePluginResult> pluginResultDOS = rulePluginResultRepository.findByRuleIds(ruleIds);

        List<RulePluginParam> pluginParamDOS = rulePluginParamRepository.findByRuleIds(ruleIds);

        // 构建关联数据映射
        Map<String, List<RuleTagDO>> tagsMap = tagsDOS.stream()
                .collect(Collectors.groupingBy(RuleTagDO::getRuleId));
        // 监控插件字段
        Map<String, List<RulePluginResult>> pluginResultsMap = pluginResultDOS.stream()
                .collect(Collectors.groupingBy(RulePluginResult::getRuleId));
        // 插件参数配置
        Map<String, List<RulePluginParam>> pluginParamMap = pluginParamDOS.stream()
                .collect(Collectors.groupingBy(RulePluginParam::getRuleId));

        // 填充关联数据
        records.forEach(record -> {
            String ruleId = record.getRuleId();

            // 设置插件ID
            RulePluginMapping rulePluginMapping = rulePluginMappingRepository.findByRuleId(ruleId);
            String pluginId = rulePluginMapping.getPluginId();
            InspectionPlugin inspectionPlugin = pluginRepository.getByPluginId(pluginId);
            record.setPluginId(pluginId);
            record.setPluginInfo(inspectionPlugin);

            // 设置标签ID列表
            List<Long> tagIds = Optional.ofNullable(tagsMap.get(ruleId))
                    .map(list -> list.stream().map(RuleTagDO::getTagId).collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            record.setTagIds(tagIds);

            // 设置执行规则
            record.setPluginResults(pluginResultsMap.getOrDefault(ruleId, null));

            // 设置规则插件参数列表
            record.setPluginParams(pluginParamMap.getOrDefault(ruleId, null));

        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRule(InspectionRule inspectionRule) {

        inspectionRule = ruleRepository.save(inspectionRule);

        // 如果有标签，保存标签关联
        ruleTagRepository.saveLinkRuleId(inspectionRule.getRuleId(), inspectionRule.getTagIds());

        // 保存规则插件关联表
        RulePluginMapping rulePluginMapping = new RulePluginMapping();
        rulePluginMapping.setPluginId(inspectionRule.getPluginId());
        rulePluginMapping.setRuleId(inspectionRule.getRuleId());
        rulePluginMappingRepository.save(rulePluginMapping);

        // 保存规则监控字段
        rulePluginResultRepository.saveBatch(inspectionRule.getPluginResults(), inspectionRule.getRuleId());

        String sm4Key = Optional.ofNullable(inspectionRule.getCacheKey()).map(inspectionCipherService::getSm4RandomSalt).orElse(null);
        rulePluginParamRepository.saveBatch(inspectionRule.getPluginParams(), inspectionRule.getRuleId(), sm4Key);

        return inspectionRule.getRuleId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRule(InspectionRule inspectionRule) {
        String ruleId = inspectionRule.getRuleId();
        if (JudgeUtils.isNull(ruleRepository.findByRuleId(ruleId))) {
            BusinessException.throwBusinessException(ErrorCodeEnum.RULE_INFO_IS_NULL);
        }
        // 更新重名校验
        if (ruleRepository.checkNameExist(ruleId, inspectionRule.getName())) {
            BusinessException.throwBusinessException(ErrorCodeEnum.RULE_NAME_IS_EXIST);
        }


        boolean updateRule = ruleRepository.update(inspectionRule);

        if (!updateRule) {
            log.error("Failed to update rule with ID: {}", ruleId);
            return false;
        }

        // 2.更新标签关联
        ruleTagRepository.updateLinkRuleId(ruleId,inspectionRule.getTagIds());

        // 修改规则插件关联表
        rulePluginMappingRepository.updatePluginId(ruleId, inspectionRule.getPluginId());

        // 修改规则配置
        rulePluginResultRepository.removeByRuleId(ruleId);

        rulePluginResultRepository.saveBatch(inspectionRule.getPluginResults(), ruleId);

        String sm4Key = Optional.ofNullable(inspectionRule.getCacheKey()).map(inspectionCipherService::getSm4RandomSalt).orElse(null);
        rulePluginParamRepository.removeByRuleIdAndPluginId(ruleId, inspectionRule.getPluginId());
        rulePluginParamRepository.saveBatch(inspectionRule.getPluginParams(), ruleId, sm4Key);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRule(String ruleId) {
        // 删除基本信息
        ruleRepository.removeByRuleId(ruleId);

        // 删除相关信息
        ruleTagRepository.removeByRuleId(ruleId);

        rulePluginMappingRepository.removeByRuleId(ruleId);

        rulePluginResultRepository.removeByRuleId(ruleId);
        rulePluginParamRepository.removeByRuleId(ruleId);
    }

    @Override
    public InspectionRule getRuleDetail(String ruleId) {
        // 基本信息
        InspectionRule inspectionRule = ruleRepository.getByRuleId(ruleId);
        if (inspectionRule == null) {
            return null;
        }

        // 相关信息
        List<RuleTagDO> tagsDOS = ruleTagRepository.listByRuleId(ruleId);
        RulePluginMapping rulePluginMapping = rulePluginMappingRepository.findByRuleId(ruleId);

        List<RulePluginResult> pluginResults = rulePluginResultRepository.findByRuleId(ruleId);

        List<RulePluginParam> rulePluginParams = rulePluginParamRepository.findByRuleId(ruleId);
        inspectionRule.setPluginResults(pluginResults);
        inspectionRule.setPluginParams(rulePluginParams);

        InspectionPlugin inspectionPlugin = pluginRepository.getByPluginId(rulePluginMapping.getPluginId());

        inspectionRule.setPluginId(rulePluginMapping.getPluginId());
        inspectionRule.setPluginInfo(inspectionPlugin);
        inspectionRule.setTagIds(tagsDOS.stream().map(RuleTagDO::getTagId).collect(Collectors.toList()));

        return inspectionRule;
    }

    @Override
    public List<InspectionRule> getRuleList(InspectionRule queryCondition) {
        // 执行分页查询
        List<InspectionRule> ruleList = ruleRepository.queryList(queryCondition);

        // 处理关联数据
        if (JudgeUtils.isNotEmpty(ruleList)) {
            enrichInspectionRules(ruleList);
        }
        return ruleList;
    }


}
