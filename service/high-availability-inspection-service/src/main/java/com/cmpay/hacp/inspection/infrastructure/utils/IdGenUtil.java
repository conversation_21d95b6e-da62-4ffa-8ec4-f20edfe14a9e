package com.cmpay.hacp.inspection.infrastructure.utils;

import com.cmpay.hacp.utils.RandomUtil;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class IdGenUtil {

    private static final String TEMP_ID_FORMAT = "TEMP-%s";
    private static final String PLUGIN_ID_FORMAT = "PLUGIN-%s-%06d";
    private static final String RULE_ID_FORMAT = "RULE-%s-%06d";
    private static final String TASK_ID_FORMAT = "TASK-%s-%06d";
    private static final String RULE_GROUP_ID_FORMAT = "RULE-GROUP-%s-%06d";

    public static String generateTempId(){
        return String.format(TEMP_ID_FORMAT,RandomUtil.getCharacterAndNumber(8));
    }

    public static String generatePluginId(Long id){
        return String.format(PLUGIN_ID_FORMAT,RandomUtil.getCharacterAndNumber(6),id);
    }

    public static String generateRuleId(Long id){
        return String.format(RULE_ID_FORMAT,RandomUtil.getCharacterAndNumber(6),id);
    }

    public static String generateTaskId(Long id){
        return String.format(TASK_ID_FORMAT,RandomUtil.getCharacterAndNumber(6),id);
    }

    public static String generateRuleGroupId(Long id){
        return String.format(RULE_GROUP_ID_FORMAT,RandomUtil.getCharacterAndNumber(6),id);
    }
}
