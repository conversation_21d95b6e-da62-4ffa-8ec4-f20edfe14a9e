package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginConfigScript;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScript;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginScriptDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface PluginScriptConverter {
    PluginScript toPluginScript(PluginScriptDO pluginScriptDO);

    @IgnoreAuditFields
    PluginScriptDO pluginConfigScriptToPluginScriptDO(PluginConfigScript pluginConfigScript, String pluginId);

    @IgnoreAuditFields
    PluginScriptDO toPluginScriptDO(PluginScript pluginScript);
}
