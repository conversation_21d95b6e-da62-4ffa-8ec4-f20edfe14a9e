package com.cmpay.hacp.inspection.domain.execution.gateway;

import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryResponse;
import com.cmpay.hacp.inspection.domain.model.firefly.FireflyToken;

/**
 * 萤火虫网关接口
 * 提供与萤火虫系统交互的能力
 */
public interface FireflyGateway {

    /**
     * 获取认证令牌
     *
     * @return 萤火虫认证令牌
     * @throws RuntimeException 当认证失败时抛出异常
     */
    FireflyToken getAuthToken();

    /**
     * 查询指标数据列表
     *
     * @param request 指标查询请求
     * @return 指标查询响应
     * @throws RuntimeException 当查询失败时抛出异常
     */
    IndicatorQueryResponse getIndicatorDataList(IndicatorQueryRequest request);
}
