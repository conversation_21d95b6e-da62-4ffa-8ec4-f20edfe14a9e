package com.cmpay.hacp.inspection.infrastructure.execution.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * K8s执行配置属性
 */
@ConfigurationProperties(prefix = "inspection.k8s")
@Data
public class K8sProperties {
    
    /**
     * 连接超时时间（毫秒），默认5秒
     */
    private int connectionTimeout = 5000;
    
    /**
     * 执行超时时间（毫秒），默认30秒
     */
    private int executionTimeout = 30000;
    
    /**
     * 默认kubeconfig文件路径
     */
    private String defaultKubeconfigPath = "~/.kube/config";
    
    /**
     * kubectl命令路径，默认使用PATH中的kubectl
     */
    private String kubectlPath = "kubectl";
    
    /**
     * 默认工作目录（在Pod中执行脚本的目录）
     */
    private String defaultWorkingDirectory = "/tmp";
    
    /**
     * 临时文件前缀
     */
    private String tempFilePrefix = "hacp_script_";
    
    /**
     * 是否在执行完成后清理临时文件
     */
    private boolean cleanupTempFiles = true;
    
    /**
     * 脚本执行重试次数
     */
    private int retryCount = 0;
    
    /**
     * 重试间隔（毫秒）
     */
    private int retryInterval = 1000;
}
