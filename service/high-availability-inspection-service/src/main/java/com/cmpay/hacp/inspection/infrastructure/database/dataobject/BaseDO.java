package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 基础实体类，包含所有实体共有的字段
 */
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class BaseDO extends BaseInsertDO{

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedByName;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updatedTime;
}
