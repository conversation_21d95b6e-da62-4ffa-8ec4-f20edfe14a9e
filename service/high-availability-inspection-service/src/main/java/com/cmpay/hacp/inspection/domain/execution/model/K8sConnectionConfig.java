package com.cmpay.hacp.inspection.domain.execution.model;

import lombok.Builder;
import lombok.Data;

/**
 * K8s连接配置
 * 用于配置kubectl exec执行脚本时的连接参数
 */
@Data
@Builder
public class K8sConnectionConfig {
    
    /**
     * 集群名称
     */
    private String cluster;
    
    /**
     * 命名空间
     */
    private String namespace;
    
    /**
     * Pod名称
     */
    private String podName;
    
    /**
     * 容器名称（可选，如果Pod有多个容器时需要指定）
     */
    private String container;
    
    /**
     * kubeconfig文件路径（可选，默认使用~/.kube/config）
     */
    private String kubeconfigPath;
    
    /**
     * kubectl context（可选，用于多集群环境）
     */
    private String context;
    
    /**
     * kubectl命令路径（可选，默认使用PATH中的kubectl）
     */
    private String kubectlPath;
    
    /**
     * 工作目录（在Pod中执行脚本的目录，默认为/tmp）
     */
    private String workingDirectory;
}
