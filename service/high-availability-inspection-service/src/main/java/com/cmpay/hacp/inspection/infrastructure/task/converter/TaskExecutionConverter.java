package com.cmpay.hacp.inspection.infrastructure.task.converter;

import com.cmpay.hacp.inspection.domain.task.model.TaskExecution;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface TaskExecutionConverter {

    /**
     * 将TaskExecution领域对象转换为TaskExecutionDO
     *
     * @param taskExecution 任务执行领域对象
     * @return TaskExecutionDO
     */
    @IgnoreAuditFields
    TaskExecutionDO toTaskExecutionDO(TaskExecution taskExecution);

    /**
     * 将TaskExecutionDO转换为TaskExecution领域对象
     *
     * @param taskExecutionDO 任务执行数据对象
     * @return TaskExecution领域对象
     */
    TaskExecution toTaskExecution(TaskExecutionDO taskExecutionDO);
}
