package com.cmpay.hacp.inspection.application.plugin;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;

import java.util.List;

/**
 * 指标元数据服务接口
 */
public interface IndicatorMetadataService {
    
    /**
     * 根据指标类型查询指标定义列表
     *
     * @param indicatorType 指标类型
     * @return 指标定义列表
     */
    List<IndicatorDefinition> findIndicatorsByType(IndicatorType indicatorType);

    /**
     * 根据指标类型查询指标定义列表
     *
     * @param indicatorName 指标名称
     * @return 指标定义
     */
    IndicatorDefinition findIndicatorByName(String indicatorName);
}
