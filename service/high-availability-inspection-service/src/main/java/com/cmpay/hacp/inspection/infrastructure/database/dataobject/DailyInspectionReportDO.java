package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 按日巡检汇总报告表 - daily_inspection_report
 * 基于按次巡检报告汇总生成的日级别分析报告
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_daily_report")
public class DailyInspectionReportDO extends BaseDO {

    /**
     * 报告日期（YYYY-MM-DD）
     */
    private LocalDate reportDate;

    /**
     * 日报告编号（唯一标识，如：DR20250324）
     */
    private String reportId;

    /**
     * 规则数（显示在列表页）
     */
    private Integer ruleCount;

    /**
     * 执行数（总检查数，显示在列表页）
     */
    private Integer executionCount;

    /**
     * 告警数（显示在列表页）
     */
    private Integer warningCount;

    /**
     * 错误数（显示在列表页）
     */
    private Integer errorCount;

    /**
     * 通过率（百分比，如：92.2）
     */
    private Double passRate;

    /**
     * 通过率变化（相对昨日，百分点）
     * 正数表示上升，负数表示下降
     */
    private Double passRateChange;

    /**
     * 平均响应时间（毫秒）
     */
    private Long averageResponseTime;

    /**
     * 响应时间变化（相对昨日，毫秒）
     * 正数表示增加，负数表示减少
     */
    private Long responseTimeChange;

    /**
     * 异常总数
     */
    private Integer totalExceptionCount;

    /**
     * 报告状态（生成中、已完成、生成失败）
     */
    private String reportStatus;
}
