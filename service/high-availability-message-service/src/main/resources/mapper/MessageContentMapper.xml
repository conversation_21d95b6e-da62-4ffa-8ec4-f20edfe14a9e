<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.message.dao.IMessageContentDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.message.entity.MessageContentDO" >
        <id column="message_content_id" property="messageContentId" jdbcType="INTEGER" />
        <result column="extra_id" property="extraId" jdbcType="VARCHAR" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="message_type" property="messageType" jdbcType="VARCHAR" />
        <result column="message_title" property="messageTitle" jdbcType="VARCHAR" />
        <result column="message_content" property="messageContent" jdbcType="VARCHAR" />
        <result column="scope" property="scope" jdbcType="VARCHAR" />
        <result column="message_child_type" property="messageChildType" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="BIT" />
        <result column="extra_params" property="extraParams" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        message_content_id, extra_id, tenant_id, workspace_id, message_type, message_title, 
        message_content, scope, message_child_type, create_time, operator_name, operator_id, 
        status, extra_params
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from message_content
        where message_content_id = #{messageContentId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from message_content
        where message_content_id = #{messageContentId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.message.entity.MessageContentDO" useGeneratedKeys="true" keyProperty="messageContentId" >
        insert into message_content
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="extraId != null" >
                extra_id,
            </if>
            <if test="tenantId != null" >
                tenant_id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="messageType != null" >
                message_type,
            </if>
            <if test="messageTitle != null" >
                message_title,
            </if>
            <if test="messageContent != null" >
                message_content,
            </if>
            <if test="scope != null" >
                scope,
            </if>
            <if test="messageChildType != null" >
                message_child_type,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="extraParams != null" >
                extra_params,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="extraId != null" >
                #{extraId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="messageType != null" >
                #{messageType,jdbcType=VARCHAR},
            </if>
            <if test="messageTitle != null" >
                #{messageTitle,jdbcType=VARCHAR},
            </if>
            <if test="messageContent != null" >
                #{messageContent,jdbcType=VARCHAR},
            </if>
            <if test="scope != null" >
                #{scope,jdbcType=VARCHAR},
            </if>
            <if test="messageChildType != null" >
                #{messageChildType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=BIT},
            </if>
            <if test="extraParams != null" >
                #{extraParams,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.message.entity.MessageContentDO" >
        update message_content
        <set >
            <if test="extraId != null" >
                extra_id = #{extraId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null" >
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="messageType != null" >
                message_type = #{messageType,jdbcType=VARCHAR},
            </if>
            <if test="messageTitle != null" >
                message_title = #{messageTitle,jdbcType=VARCHAR},
            </if>
            <if test="messageContent != null" >
                message_content = #{messageContent,jdbcType=VARCHAR},
            </if>
            <if test="scope != null" >
                scope = #{scope,jdbcType=VARCHAR},
            </if>
            <if test="messageChildType != null" >
                message_child_type = #{messageChildType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=BIT},
            </if>
            <if test="extraParams != null" >
                extra_params = #{extraParams,jdbcType=VARCHAR},
            </if>
        </set>
        where message_content_id = #{messageContentId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.message.entity.MessageContentDO" >
        select 
        <include refid="Base_Column_List" />
        from message_content
        <where >
            <if test="messageContentId != null" >
                and message_content_id = #{messageContentId,jdbcType=INTEGER}
            </if>
            <if test="extraId != null" >
                and extra_id = #{extraId,jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null" >
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="messageType != null" >
                and message_type = #{messageType,jdbcType=VARCHAR}
            </if>
            <if test="messageTitle != null" >
                and message_title = #{messageTitle,jdbcType=VARCHAR}
            </if>
            <if test="messageContent != null" >
                and message_content = #{messageContent,jdbcType=VARCHAR}
            </if>
            <if test="scope != null" >
                and scope = #{scope,jdbcType=VARCHAR}
            </if>
            <if test="messageChildType != null" >
                and message_child_type = #{messageChildType,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=BIT}
            </if>
            <if test="extraParams != null" >
                and extra_params = #{extraParams,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>