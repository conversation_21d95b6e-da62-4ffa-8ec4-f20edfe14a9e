<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.message.dao.IMessageContentUserExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.message.entity.MessageContentUserDO" >
        <id column="message_id" property="messageId" jdbcType="BIGINT" />
        <result column="message_content_id" property="messageContentId" jdbcType="INTEGER" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="BIT" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="BaseResultExtMap" type="com.cmpay.hacp.message.bo.MessageContentExtBO">
        <id column="message_id" property="messageId" jdbcType="BIGINT" />
        <result column="message_content_id" property="messageContentId" jdbcType="INTEGER" />
        <result column="status" property="status" jdbcType="BIT" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="extra_id" property="extraId" jdbcType="VARCHAR" />
        <result column="extra_params" property="extraParams" jdbcType="VARCHAR" />
        <result column="message_type" property="messageType" jdbcType="VARCHAR" />
        <result column="message_title" property="messageTitle" jdbcType="VARCHAR" />
        <result column="message_content" property="messageContent" jdbcType="VARCHAR" />
        <result column="scope" property="scope" jdbcType="VARCHAR" />
        <result column="message_child_type" property="messageChildType" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="BIT" />
    </resultMap>

    <sql id="Base_Column_List" >
        message_id, message_content_id, user_id, `status`, update_time
    </sql>

    <insert id="insertBatch">
        insert into message_content_user(message_content_id, user_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.messageContentId}, #{item.userId})
        </foreach>
    </insert>

    <select id="getNumberOfUnreadMessages" resultType="java.lang.Integer">
        select
            count(1)
        from message_content_user mcu left join message_content mc on mcu.message_content_id = mc.message_content_id
        where
            mc.`status` = 0
            and mc.message_type = 'messages'
            and mcu.`status` = 1
            and mcu.user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="queryMessage" resultMap="BaseResultExtMap" parameterType="com.cmpay.hacp.message.bo.MessageContentUserBO">
        select
            mcu.message_id, mcu.message_content_id, mcu.user_id, mcu.`status`, mcu.update_time,
            mc.message_title, mc.message_content, mc.create_time, mc.operator_name, mc.message_child_type, mc.message_type,
            mc.extra_id,mc.extra_params,mc.scope, mc.tenant_id, mc.workspace_id, mc.operator_id
        from message_content_user mcu
        left join message_content mc on mcu.message_content_id = mc.message_content_id
        where
            mc.`status` = 0
            and mc.message_type = 'messages'
          <if test="mcu.status != null">
            and mcu.`status` = #{mcu.status,jdbcType=BIT}
          </if>
            and mcu.user_id = #{mcu.userId,jdbcType=VARCHAR}
        order by mc.create_time desc
    </select>

    <update id="readMessage">
        update message_content_user
        set `status` = 0,
            update_time = #{mcu.updateTime,jdbcType=TIMESTAMP}
        where
            user_id = #{mcu.userId,jdbcType=VARCHAR}
        <if test="mcu.messageId != null">
            and message_id = #{mcu.messageId,jdbcType=BIGINT}
        </if>
    </update>
    <update id="readMessageContent">
        update message_content_user
        set `status` = 0,
        update_time = #{mcu.updateTime,jdbcType=TIMESTAMP}
        where
        user_id = #{mcu.userId,jdbcType=VARCHAR}
        and message_content_id = #{mcu.messageContentId,jdbcType=BIGINT}
    </update>

</mapper>