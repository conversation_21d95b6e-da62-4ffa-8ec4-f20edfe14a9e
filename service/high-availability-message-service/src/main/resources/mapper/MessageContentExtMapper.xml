<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.message.dao.IMessageContentExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.message.entity.MessageContentDO" >
        <id column="message_content_id" property="messageContentId" jdbcType="INTEGER" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="message_type" property="messageType" jdbcType="VARCHAR" />
        <result column="message_title" property="messageTitle" jdbcType="VARCHAR" />
        <result column="message_content" property="messageContent" jdbcType="VARCHAR" />
        <result column="scope" property="scope" jdbcType="VARCHAR" />
        <result column="message_child_type" property="messageChildType" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="BIT" />
    </resultMap>

    <sql id="Base_Column_List" >
        message_content_id, tenant_id, workspace_id, message_type, message_title, message_content, 
        scope, message_child_type, create_time, operator_name,
        operator_id, `status`
    </sql>

    <select id="findExt" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.message.entity.MessageContentDO" >
        select
        <include refid="Base_Column_List" />
        from message_content
        <where >
            <if test="messageContentId != null" >
                and message_content_id = #{messageContentId,jdbcType=INTEGER}
            </if>
            <if test="extraId != null" >
                and extra_id = #{extraId,jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null" >
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="messageType != null" >
                and message_type = #{messageType,jdbcType=VARCHAR}
            </if>
            <if test="messageTitle != null" >
                and message_title = #{messageTitle,jdbcType=VARCHAR}
            </if>
            <if test="messageContent != null" >
                and message_content = #{messageContent,jdbcType=VARCHAR}
            </if>
            <if test="scope != null" >
                and scope = #{scope,jdbcType=VARCHAR}
            </if>
            <if test="messageChildType != null" >
                and message_child_type = #{messageChildType,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=BIT}
            </if>
            <if test="extraParams != null" >
                and extra_params = #{extraParams,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>