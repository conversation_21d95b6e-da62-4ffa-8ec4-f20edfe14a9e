package com.cmpay.hacp.message.utils;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.activation.DataSource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <AUTHOR>
 */
public class EmailHelper {
    private final static Logger logger = LoggerFactory.getLogger(EmailHelper.class);
    /**
     * 邮箱服务器地址
     */
    private String host;
    /**
     * 端口
     */
    private String port;
    /**
     * 用户名
     */
    private String user;
    /**
     * 密钥
     */
    private String secret;
    /**
     * 发件人别称
     */
    private String personal;
    /**
     * 收件人地址
     */
    private String to;
    /**
     * 主题
     */
    private String subject;
    private Set<String> addresses;

    public EmailHelper() {
    }

    public EmailHelper(String host, String port, String user, String secret, String personal) {
        this.host = host;
        this.port = port;
        this.user = user;
        this.secret = secret;
        this.personal = personal;
    }

    public void setAddress(String to, String subject) {
        this.to = to;
        this.subject = subject;
    }

    public void setAddress(String to, Set<String> addresses, String subject) {
        this.to = to;
        this.addresses = addresses;
        this.subject = subject;
    }

    public void send(String content) {
        logger.info("##EmailHelper#send content: {}", content);
        Properties props = new Properties();
        // 设置发送邮件的邮件服务器的属性（这里使用网易的smtp服务器）
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.port", port);
        // 需要经过授权，也就是有户名和密码的校验，这样才能通过验证（一定要有这一条）
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        // 用刚刚设置好的props对象构建一个session
        Session session = Session.getDefaultInstance(props);
        // 有了这句便可以在发送邮件的过程中在console处显示过程信息，供调试使
        // 用（你可以在控制台（console)上看到发送邮件的过程）
        session.setDebug(true);
        // 用session为参数定义消息对象
        MimeMessage message = new MimeMessage(session);
        try {
            // 加载发件人地址
            message.setFrom(new InternetAddress(user, personal, "UTF-8"));
            for (String addressee : to.split(CommonConstant.SEMICOLON)) {
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(addressee, addressee, "UTF-8"));
            }
            // 加载收件人地址
            //抄送
            if (JudgeUtils.isNotEmpty(addresses)) {
                Set<Address> emails = new HashSet<>();
                addresses.stream().forEach(email -> {
                    try {
                        Address address = new InternetAddress(email, email, "UTF-8");
                        emails.add(address);
                    } catch (UnsupportedEncodingException e) {
                        logger.error("##EmailHelper#send Exception", e);
                    }
                });
                message.addRecipients(Message.RecipientType.CC, emails.toArray(emails.toArray(new Address[0])));
            }
            // 加载标题
            message.setSubject(subject, "UTF-8");

            // 将multipart对象放到message中
            // 2.4设置邮件内容
            message.setContent(content, "text/html;charset=UTF-8");
            // 保存邮件
            message.saveChanges();
            // 发送邮件
            Transport transport = session.getTransport("smtp");
            // 连接服务器的邮箱
            transport.connect(host, user, secret);
            // 把邮件发送出去
            transport.sendMessage(message, message.getAllRecipients());
            transport.close();
        } catch (MessagingException e) {
            logger.error("##EmailHelper#send Exception", e);
            BusinessException.throwBusinessException(MsgEnum.EMAIL_MASSAGE_SEND_TIMEOUT_FAILED);
        }catch (UnsupportedEncodingException e) {
            logger.error("##EmailHelper#send Exception", e);
            BusinessException.throwBusinessException(MsgEnum.EMAIL_MASSAGE_SEND_FAILURE_FAILED);
        }
    }


    public void send(String content, HashMap<String, ByteArrayInputStream> excelMap) {
        logger.info("##EmailHelper#send content: {}", content);
        Properties props = new Properties();
        // 设置发送邮件的邮件服务器的属性（这里使用网易的smtp服务器）
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.port", port);
        // 需要经过授权，也就是有户名和密码的校验，这样才能通过验证（一定要有这一条）
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        // 用刚刚设置好的props对象构建一个session
        Session session = Session.getDefaultInstance(props);
        // 有了这句便可以在发送邮件的过程中在console处显示过程信息，供调试使
        // 用（你可以在控制台（console)上看到发送邮件的过程）
        session.setDebug(true);
        // 用session为参数定义消息对象
        MimeMessage message = new MimeMessage(session);
        try {
            // 加载发件人地址
            message.setFrom(new InternetAddress(user, personal, "UTF-8"));
            // 加载发件人地址
            message.setFrom(new InternetAddress(user, personal, "UTF-8"));
            for (String addressee : to.split(CommonConstant.SEMICOLON)) {
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(addressee, addressee, "UTF-8"));
            }
            //抄送
            if (JudgeUtils.isNotEmpty(addresses)) {
                Set<Address> emails = new HashSet<>();
                addresses.stream().forEach(email -> {
                    try {
                        Address address = new InternetAddress(email, email, "UTF-8");
                        emails.add(address);
                    } catch (UnsupportedEncodingException e) {
                        logger.error("##EmailHelper#send Exception", e);
                    }
                });
                message.addRecipients(Message.RecipientType.CC, emails.toArray(emails.toArray(new Address[0])));
            }
            // 加载标题
            message.setSubject(subject, "UTF-8");

            // 通过MimeMessageHelper设置正文和附件，否则会导致两者显示不全
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "utf-8");
            helper.setText(content, true);
            helper.setEncodeFilenames(true);
            //设置邮件附件
            for (Map.Entry<String, ByteArrayInputStream> entry : excelMap.entrySet()) {
                String k = entry.getKey();
                ByteArrayInputStream v = entry.getValue();
                DataSource dataSource = new ByteArrayDataSource(v, "application/excel");
                helper.addAttachment(k, dataSource);
            }
            // 保存邮件
            message.saveChanges();
            // 发送邮件
            Transport transport = session.getTransport("smtp");
            // 连接服务器的邮箱
            transport.connect(host, user, secret);
            // 把邮件发送出去
            transport.sendMessage(message, message.getAllRecipients());
            transport.close();
        } catch (Exception e) {
            logger.error("##EmailHelper#send Exception", e);
        }
    }
}
