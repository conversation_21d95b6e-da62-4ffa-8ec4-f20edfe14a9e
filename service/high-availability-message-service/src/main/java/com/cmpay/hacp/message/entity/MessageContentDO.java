/*
 * @ClassName MessageContentDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-29 13:57:06
 */
package com.cmpay.hacp.message.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.hacp.message.enums.MessageChildTypeEnum;
import com.cmpay.hacp.message.enums.MessageTypeEnum;
import com.cmpay.hacp.message.enums.ScopeMessageEnum;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class MessageContentDO extends BaseDO {
    /**
     * @Fields messageContentId 主键
     */
    private Integer messageContentId;
    /**
     * @Fields extraId 拓展id
     */
    private String extraId;
    /**
     * @Fields tenantId 租户id
     */
    private String tenantId;
    /**
     * @Fields workspaceId 项目id
     */
    private String workspaceId;
    /**
     * @Fields messageType 通知类型
     */
    private MessageTypeEnum messageType;
    /**
     * @Fields messageTitle 标题
     */
    private String messageTitle;
    /**
     * @Fields messageContent 内容
     */
    private String messageContent;
    /**
     * @Fields scope 范围
     */
    private ScopeMessageEnum scope;
    /**
     * @Fields messageChildType 子类型
     */
    private MessageChildTypeEnum messageChildType;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields status 删除标记，1删除
     */
    private Boolean status;
    /**
     * @Fields extraParams 扩展参数
     */
    private String extraParams;

    public Integer getMessageContentId() {
        return messageContentId;
    }

    public void setMessageContentId(Integer messageContentId) {
        this.messageContentId = messageContentId;
    }

    public String getExtraId() {
        return extraId;
    }

    public void setExtraId(String extraId) {
        this.extraId = extraId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public MessageTypeEnum getMessageType() {
        return messageType;
    }

    public void setMessageType(MessageTypeEnum messageType) {
        this.messageType = messageType;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public ScopeMessageEnum getScope() {
        return scope;
    }

    public void setScope(ScopeMessageEnum scope) {
        this.scope = scope;
    }

    public MessageChildTypeEnum getMessageChildType() {
        return messageChildType;
    }

    public void setMessageChildType(MessageChildTypeEnum messageChildType) {
        this.messageChildType = messageChildType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public String getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(String extraParams) {
        this.extraParams = extraParams;
    }
}