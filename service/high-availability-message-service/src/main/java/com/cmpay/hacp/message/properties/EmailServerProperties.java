package com.cmpay.hacp.message.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "hacp.admin.email-server")
public class EmailServerProperties {

    private String host;

    private String port;

    private String user;

    private String secret;

    private String personal;

    private String adminEmail = "<EMAIL>";

}
