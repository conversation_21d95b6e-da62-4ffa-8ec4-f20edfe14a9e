package com.cmpay.hacp.message.bo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/09/14 15:21
 * @since 1.0.0
 */

public interface ExtenalMessageCapable {
    String getMessageContent();
    String getMessageTitle();

    /**
     * 通知局部必传
     * @return
     */
    List<String> getMessageUserIds();
    Map<String, String> getExtraParams();

    String getExtraId();

    /**
     * 局部通知的，都需要传递该字段
     */
    String getWorkspaceId();

    default void checkParams(){};
}
