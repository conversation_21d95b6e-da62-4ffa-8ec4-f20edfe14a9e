package com.cmpay.hacp.message.service.adapter;

import com.cmpay.hacp.message.bo.ExtenalMessageCapable;
import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.tenant.bo.TenantWorkspaceBO;
import com.cmpay.hacp.message.dao.IMessageContentExtDao;
import com.cmpay.hacp.message.dao.IMessageContentUserExtDao;
import com.cmpay.hacp.message.entity.MessageContentDO;
import com.cmpay.hacp.message.entity.MessageContentUserDO;
import com.cmpay.hacp.message.enums.MessageTypeEnum;
import com.cmpay.hacp.message.enums.ScopeMessageEnum;
import com.cmpay.hacp.message.service.MessageSendService;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.hacp.tenant.service.TenantWorkspaceService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/09/14 14:54
 * @since 1.0.0
 */

public abstract class MessageSendAdapter implements MessageSendService {

    @Autowired
    protected SystemUserService systemUserService;

    @Autowired
    protected IMessageContentUserExtDao messageContentUserExtDao;

    @Autowired
    protected IMessageContentExtDao messageContentExtDao;

    @Autowired
    protected TenantWorkspaceService tenantWorkspaceService;

    protected MessageTypeEnum messageType;

    protected ExtenalMessageCapable message;

    public MessageSendAdapter(SystemUserService systemUserService,
            IMessageContentUserExtDao messageContentUserExtDao,
            IMessageContentExtDao messageContentExtDao,
            MessageTypeEnum messageType) {
        this.systemUserService = systemUserService;
        this.messageContentUserExtDao = messageContentUserExtDao;
        this.messageContentExtDao = messageContentExtDao;
        this.messageType = messageType;
    }

    @Override
    @Transactional(propagation = Propagation.NESTED, rollbackFor = BusinessException.class)
    public void sendMessage(ExtenalMessageCapable message)  throws Exception{
        this.message = message;
        message.checkParams();
        sendCustomizeMessage();
        if(!shouldSaveToDatabase()){
            return;
        }
        MessageContentDO data = BeanConvertUtil.convert(message, MessageContentDO.class);
        saveBefore(data);
        saveContent(data);
        List<String> userIds = saveMessageUser(data);
        saveMessageAfter(userIds);
    }

    public void saveBefore(MessageContentDO data) {

    }

    protected abstract void sendCustomizeMessage() throws Exception;

    /**
     * 是否需要持久化数据库
     * @return
     */
    protected abstract boolean shouldSaveToDatabase();

    private void saveContent(MessageContentDO data) {
        data.setMessageType(messageType);
        if(JudgeUtils.isNotBlank(data.getWorkspaceId())){
            TenantWorkspaceBO workspaceInfo = tenantWorkspaceService.getWorkspaceInfo(data.getWorkspaceId());
            data.setTenantId(workspaceInfo.getTenantId());
        }
        messageContentExtDao.insert(data);
    }

    private List<String> saveMessageUser(MessageContentDO data) {
        List<String> messageUserIds = message.getMessageUserIds();
        if (JudgeUtils.isEmpty(messageUserIds)) {
            List<UserBO> allUsers = systemUserService.getAllUsers(null);
            messageUserIds = allUsers.stream().map(UserBO::getUserId).collect(Collectors.toList());
        }

        // 批量插入
        List<MessageContentUserDO> list = new ArrayList<>(messageUserIds.size() + 1);
        messageUserIds.forEach(f -> {
            MessageContentUserDO messageVO = new MessageContentUserDO();
            messageVO.setUserId(f);
            messageVO.setMessageContentId(data.getMessageContentId());
            list.add(messageVO);
        });
        messageContentUserExtDao.insertBatch(list);
        return messageUserIds;
    }

    protected void saveMessageAfter(List<String> userIds){

    }
}
