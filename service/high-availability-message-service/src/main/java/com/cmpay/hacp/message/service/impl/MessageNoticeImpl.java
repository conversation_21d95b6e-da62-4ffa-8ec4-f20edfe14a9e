package com.cmpay.hacp.message.service.impl;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.message.bo.MessageContentExtBO;
import com.cmpay.hacp.message.bo.MessageContentUserBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.message.bo.SendMessageEmailBO;
import com.cmpay.hacp.message.dao.IMessageContentExtDao;
import com.cmpay.hacp.message.dao.IMessageContentUserExtDao;
import com.cmpay.hacp.message.entity.MessageContentDO;
import com.cmpay.hacp.message.entity.MessageContentUserDO;
import com.cmpay.hacp.message.enums.MessageTypeEnum;
import com.cmpay.hacp.message.service.MessageNoticeService;
import com.cmpay.hacp.message.service.MessageSendService;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.exception.ErrorMsgCode;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/09/29 9:35
 * @since 1.0.0
 */
@Slf4j
@Service
public class MessageNoticeImpl implements MessageNoticeService {

    @Autowired
    private IMessageContentUserExtDao messageContentUserExtDao;

    @Autowired
    private IMessageContentExtDao messageContentExtDao;

    @Autowired
    private SystemCacheService systemCacheService;

    @Autowired
    private Map<String, MessageSendService> messageSendServiceMap;

    @Override
    public int getNumberOfUnreadMessages(String loginUserId) {
        Integer value = (Integer) systemCacheService.getValue(CommonConstant.MESSAGE_KEY + loginUserId);
        if(JudgeUtils.isNotNull(value)){
            return value;
        }
        int result = messageContentUserExtDao.getNumberOfUnreadMessages(loginUserId);
        systemCacheService.setValue(CommonConstant.MESSAGE_KEY + loginUserId, result, 60 * 5, java.util.concurrent.TimeUnit.SECONDS);
        return result;
    }

    @Override
    public PageInfo<MessageContentExtBO> queryMessage(int pageNum,int pageSize, MessageContentUserBO messageContentExtBO){
        MessageContentUserDO query = BeanConvertUtil.convert(messageContentExtBO, MessageContentUserDO.class);
        PageInfo<MessageContentExtBO> pageInfo;
        if(pageNum==0 || pageSize==0){
            List<MessageContentExtBO> data = messageContentUserExtDao.queryMessage(query);
            pageInfo =new PageInfo<>(data);
        }else{
            pageInfo = PageUtils.pageQueryWithCount(pageNum, pageSize, () -> messageContentUserExtDao.queryMessage(query));
        }
        return JudgeUtils.isEmpty(pageInfo.getList()) ? new PageInfo<>(new ArrayList<>()) : pageInfo;
    }

    @Override
    public void readMessage(Long messageId, String loginUserId) {
        MessageContentUserDO messageContentUserDO = new MessageContentUserDO();
        messageContentUserDO.setUserId(loginUserId);
        messageContentUserDO.setMessageId(messageId);
        messageContentUserDO.setUpdateTime(LocalDateTime.now());
        log.info("messageId：{} is null read all message",messageId);
        messageContentUserExtDao.readMessage(messageContentUserDO);
        systemCacheService.delete(CommonConstant.MESSAGE_KEY + loginUserId);
    }

    @Override
    public void readMessageExtraId(String extraId, String loginUserId,String workspaceId) {
        MessageContentDO entity = new MessageContentDO();
        entity.setExtraId(extraId);
        entity.setWorkspaceId(workspaceId);
        List<MessageContentDO> messageContentDOS = messageContentExtDao.findExt(entity);
        if(JudgeUtils.isEmpty(messageContentDOS)){
            return;
        }
        MessageContentUserDO messageContentUserDO = new MessageContentUserDO();
        messageContentUserDO.setUserId(loginUserId);
        messageContentUserDO.setMessageContentId(messageContentDOS.get(0).getMessageContentId());
        messageContentUserDO.setUpdateTime(LocalDateTime.now());
        log.info("messageId：{} is null read all message",messageContentDOS.get(0).getMessageContentId());
        messageContentUserExtDao.readMessageContent(messageContentUserDO);
        systemCacheService.delete(CommonConstant.MESSAGE_KEY + loginUserId);
    }

    @Override
    public void sendEmail(SendMessageEmailBO bo) {
        MessageSendService messageSendService = messageSendServiceMap.get(MessageTypeEnum.EMAIL.getBeanName());
        if(JudgeUtils.isNull(messageSendService)){
            log.error("MessageSendService email beanName is null");
            BusinessException.throwBusinessException(MsgEnum.EMAIL_MASSAGE_SEND_FAILED);
        }
        try {
            messageSendService.sendMessage(bo);
        } catch (Exception e) {
            log.error("send message email error ",e.getCause());
            BusinessException.throwBusinessException(MsgEnum.EMAIL_MASSAGE_SEND_FAILED);
        }
    }
}
