package com.cmpay.hacp.message.service.adapter.impl;

import com.cmpay.hacp.message.bo.InteriorMessageBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.message.dao.IMessageContentExtDao;
import com.cmpay.hacp.message.dao.IMessageContentUserExtDao;
import com.cmpay.hacp.message.enums.MessageTypeEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.message.service.adapter.MessageSendAdapter;
import com.cmpay.hacp.message.utils.InteriorMessageUtils;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.lemon.common.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 使用{@link InteriorMessageUtils}构建消息
 * <AUTHOR>
 * @create 2024/09/14 14:57
 * @since 1.0.0
 */
@Service("interiorMessageServiceImpl")
public class InteriorMessageSendServiceImpl extends MessageSendAdapter {

    private final SystemCacheService systemCacheService;

    public InteriorMessageSendServiceImpl(SystemUserService systemUserService,
            IMessageContentUserExtDao messageContentUserExtDao,
            IMessageContentExtDao messageContentExtDao,
            SystemCacheService systemCacheService) {
        super(systemUserService, messageContentUserExtDao, messageContentExtDao, MessageTypeEnum.MESSAGES);
        this.systemCacheService = systemCacheService;
    }

    @Override
    protected void sendCustomizeMessage() {
        if(!(message instanceof InteriorMessageBO)){
            BusinessException.throwBusinessException(MsgEnum.MESSAGE_CLASS_ERROR);
        }
    }

    @Override
    protected boolean shouldSaveToDatabase() {
        return true;
    }

    @Override
    protected void saveMessageAfter(List<String> userIds) {
        // 清空未读消息数量缓存
        userIds.forEach(f-> systemCacheService.delete(CommonConstant.MESSAGE_KEY + f));
    }
}
