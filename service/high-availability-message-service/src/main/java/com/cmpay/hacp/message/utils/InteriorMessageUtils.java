package com.cmpay.hacp.message.utils;

import com.cmpay.hacp.message.bo.InteriorMessageBO;
import com.cmpay.hacp.message.enums.MessageChildTypeEnum;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/10/11 9:30
 * @since 1.0.0
 */
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class InteriorMessageUtils {

    /**
     * 构建内部消息
     * @param title 标题
     * @param content 内容
     * @param childType 子类型
     * @param userIds 接收人
     * @param operationId 操作人
     * @param operationName 操作人名
     * @param workspaceId 项目ID
     * @param extraParams 扩展参数
     * @return 内部消息BO
     */
    public static InteriorMessageBO buildMessage(String title,String content,MessageChildTypeEnum childType,
            List<String> userIds,String operationId,String operationName,String workspaceId, Map<String,String> extraParams){
        InteriorMessageBO messageParamBO = new InteriorMessageBO();
        messageParamBO.setMessageContent(content);
        messageParamBO.setMessageUserIds(userIds);
        messageParamBO.setWorkspaceId(workspaceId);
        messageParamBO.setMessageChildType(childType);
        messageParamBO.setOperationId(operationId);
        messageParamBO.setOperationName(operationName);
        messageParamBO.setMessageTitle(title);
        messageParamBO.setExtraParams(extraParams);
        return messageParamBO;
    }

    /**
     *
     * @param title 标题
     * @param content 内容
     * @param childType 子类型
     * @param userIds 接收人
     * @param operationId 操作人
     * @param operationName 操作人名
     * @param extraParams 扩展参数
     * @return 内部消息BO
     */
    public static InteriorMessageBO buildMessageNonWorkspaceId(String title,String content,MessageChildTypeEnum childType,
            List<String> userIds,String operationId,String operationName,Map<String,String> extraParams){
        return buildMessage(title,content,childType,userIds,operationId,operationName,null,extraParams);
    }

    /**
     * 构建局部系统消息
     * @param title 标题
     * @param content 内容
     * @param childType 子类型
     * @param userIds 接收人
     * @param workspaceId 项目ID
     * @param extraParams 扩展参数
     * @return 内部消息BO
     */
    public static InteriorMessageBO buildSystemMessage(String title,String content,MessageChildTypeEnum childType, List<String> userIds,String workspaceId,Map<String,String> extraParams){
        return buildMessage(title,content,childType,userIds,"SYSTEM","系统",workspaceId,extraParams);
    }

    /**
     * 构建系统非局部消息
     * @param title 标题
     * @param content 内容
     * @param childType 子类型
     * @param userIds 接收人
     * @param extraParams 扩展参数
     * @return 内部消息BO
     */
    public static InteriorMessageBO buildSystemMessageNonWorkspaceId(String title,String content,MessageChildTypeEnum childType, List<String> userIds,Map<String,String> extraParams){
        return buildMessageNonWorkspaceId(title,content,childType,userIds,"SYSTEM","系统",extraParams);
    }


}
