package com.cmpay.hacp.message.enums;

import com.cmpay.lemon.framework.valuable.Valuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/09/14 14:30
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ScopeMessageEnum implements Valuable<String> {
    ALL("all", "全部"),
    /**
     * 非全部则是其他，一对一通知，一对多通知
     */
    OTHER("other", "其他"),
    ;
    private final String value;

    private final String desc;

    public static List<String> getNoticePersonnel(String value) {
        return ALL.getValue().equals(value) ? Collections.singletonList("ALL") : null;
    }

}
