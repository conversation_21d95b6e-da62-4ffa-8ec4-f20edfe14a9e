package com.cmpay.hacp.message.enums;

import com.cmpay.hacp.message.service.MessageSendService;
import com.cmpay.hacp.message.service.adapter.impl.EmailMessageSendServiceImpl;
import com.cmpay.hacp.message.service.adapter.impl.InteriorMessageSendServiceImpl;
import com.cmpay.hacp.message.service.adapter.impl.SmsMessageSendServiceImpl;
import com.cmpay.lemon.framework.valuable.Valuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/09/14 9:12
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum implements Valuable<String> {
    SMS("sms", "短信","smsMessageServiceImpl", SmsMessageSendServiceImpl.class ),
    EMAIL("email", "邮件","emailMessageServiceImpl", EmailMessageSendServiceImpl.class),
    MESSAGES("messages", "站内信","interiorMessageServiceImpl", InteriorMessageSendServiceImpl.class),
    ;
    private final String value;

    private final String desc;

    private final String beanName;

    private final Class<? extends MessageSendService> clazz;
}
