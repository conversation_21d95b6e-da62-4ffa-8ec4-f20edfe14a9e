package com.cmpay.hacp.message.service;

import com.cmpay.hacp.message.bo.ExtenalMessageCapable;

/**
 * 通过 {@link cn.hutool.extra.spring.SpringUtil#getBean(com.cmpay.hacp.enums.MessageTypeEnum#getBeanName, MessageSendService ); }
 * <AUTHOR>
 * @create 2024/09/14 9:30
 * @since 1.0.0
 */

public interface MessageSendService {

    /**
     * 消息通用接口
     * @param message
     */
    void sendMessage(ExtenalMessageCapable message) throws Exception;

}
