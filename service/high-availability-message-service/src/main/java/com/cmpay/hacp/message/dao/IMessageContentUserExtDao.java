/*
 * @ClassName IMessageContentUserDao
 * @Description 
 * @version 1.0
 * @Date 2024-09-29 11:27:22
 */
package com.cmpay.hacp.message.dao;

import com.cmpay.hacp.message.bo.MessageContentExtBO;
import com.cmpay.hacp.message.entity.MessageContentUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IMessageContentUserExtDao extends IMessageContentUserDao {
    int getNumberOfUnreadMessages(@Param("userId")String userId);

     List<MessageContentExtBO> queryMessage(@Param("mcu") MessageContentUserDO messageContentUserDO);

    int readMessage(@Param("mcu") MessageContentUserDO messageContentUserDO);

    int insertBatch(List<MessageContentUserDO> list);

    void readMessageContent(@Param("mcu") MessageContentUserDO messageContentUserDO);
}