package com.cmpay.hacp.message.bo;

import com.cmpay.hacp.message.enums.MessageTypeEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/09/14 14:16
 * @since 1.0.0
 */
@Data
public class BaseMessageBO implements ExtenalMessageCapable{

    private MessageTypeEnum messageType;

    private String messageContent;

    private String messageTitle;

    private List<String> messageUserIds;

    private Map<String,String> extraParams;

    private String workspaceId;

    private String extraId;
}
