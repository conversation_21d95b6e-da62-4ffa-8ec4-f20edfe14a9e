package com.cmpay.hacp.message.service.adapter.impl;

import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.message.dao.IMessageContentExtDao;
import com.cmpay.hacp.message.dao.IMessageContentUserExtDao;
import com.cmpay.hacp.message.entity.MessageContentDO;
import com.cmpay.hacp.message.enums.MessageTypeEnum;
import com.cmpay.hacp.message.enums.ScopeMessageEnum;
import com.cmpay.hacp.message.service.adapter.MessageSendAdapter;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/09/14 9:31
 * @since 1.0.0
 */
@Service("smsMessageServiceImpl")
public class SmsMessageSendServiceImpl extends MessageSendAdapter {

    public SmsMessageSendServiceImpl(SystemUserService systemUserService,
            IMessageContentUserExtDao messageContentUserExtDao,
            IMessageContentExtDao messageContentExtDao) {
        super(systemUserService, messageContentUserExtDao, messageContentExtDao, MessageTypeEnum.SMS);
    }

    @Override
    protected void sendCustomizeMessage() {
        List<UserBO> users = systemUserService.getUsersByUserId(message.getMessageUserIds());
        String[] tos = users.stream().map(UserBO::getMobile).filter(JudgeUtils::isNotBlank).toArray(String[]::new);
    }

    @Override
    protected boolean shouldSaveToDatabase() {
        return true;
    }

    @Override
    public void saveBefore(MessageContentDO data) {
        data.setScope(JudgeUtils.isEmpty(message.getMessageUserIds()) ? ScopeMessageEnum.ALL : ScopeMessageEnum.OTHER);
        data.setOperatorId("system");
        data.setOperatorName("system");
    }
}
