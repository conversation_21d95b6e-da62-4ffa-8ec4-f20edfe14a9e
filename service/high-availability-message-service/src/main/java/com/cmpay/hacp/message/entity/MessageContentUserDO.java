/*
 * @ClassName MessageContentUserDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-29 13:57:06
 */
package com.cmpay.hacp.message.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class MessageContentUserDO extends BaseDO {
    /**
     * @Fields messageId 主键
     */
    private Long messageId;
    /**
     * @Fields messageContentId 消息内容
     */
    private Integer messageContentId;
    /**
     * @Fields userId 通知类型
     */
    private String userId;
    /**
     * @Fields status 消息状态，1未读，0已读
     */
    private Boolean status;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;

    public Long getMessageId() {
        return messageId;
    }

    public void setMessageId(Long messageId) {
        this.messageId = messageId;
    }

    public Integer getMessageContentId() {
        return messageContentId;
    }

    public void setMessageContentId(Integer messageContentId) {
        this.messageContentId = messageContentId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}