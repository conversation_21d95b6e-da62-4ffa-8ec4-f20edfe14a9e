package com.cmpay.hacp.message.bo;

import com.cmpay.hacp.message.entity.MessageContentDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2024/09/29 13:02
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageContentExtBO extends MessageContentDO {

    private String messageId;

    private LocalDateTime updateTime;

}
