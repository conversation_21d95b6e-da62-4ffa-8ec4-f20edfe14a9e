package com.cmpay.hacp.tenant.controller;

import com.cmpay.hacp.system.bo.menu.MenuTreeBO;
import com.cmpay.hacp.system.bo.system.SystemUserBO;
import com.cmpay.hacp.dto.system.MenuTreeRspDTO;
import com.cmpay.hacp.dto.system.SystemUserDTO;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.service.SystemService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/tenant")
@Api(tags = "租户系统管理")
@Validated
public class TenantSystemController {

    @Resource
    private SystemService systemService;

    /**
     * 查询系统用户列表
     *
     * @return 系统用户列表
     */
    @GetMapping("/sys/users")
    @ApiOperation(value = "系统用户列表", notes = "系统用户列表")
    @ApiResponse(code = 200, message = "系统用户列表")
    @LogNoneRecord
    public DefaultRspDTO<List<SystemUserDTO>> getSystemUsers() {
        List<SystemUserBO> users = systemService.getSystemUsers();
        List<SystemUserDTO> result = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(users)) {
            result = BeanConvertUtil.convertList(users, SystemUserDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(result);
    }

    /**
     * 查询系统树形菜单
     *
     * @return 系统树形菜单
     */
    @GetMapping("/sys/menus")
    @ApiOperation(value = "查询系统树形菜单", notes = "查询系统树形菜单")
    @ApiResponse(code = 200, message = "系统树形菜单")
    @LogNoneRecord
    public DefaultRspDTO<List<MenuTreeRspDTO>> getSystemTreeMenus() {
        List<MenuTreeBO> menuTreeBOS = systemService.getSystemTreeMenus();
        List<MenuTreeRspDTO> systemTreeMenus = BeanConvertUtil.convertList(menuTreeBOS, MenuTreeRspDTO.class);
        return DefaultRspDTO.newSuccessInstance(systemTreeMenus);
    }

}
