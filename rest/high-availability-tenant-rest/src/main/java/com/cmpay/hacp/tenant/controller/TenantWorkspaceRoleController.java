package com.cmpay.hacp.tenant.controller;

import com.cmpay.hacp.tenant.bo.TenantWorkspaceRoleBO;
import com.cmpay.hacp.dto.tenant.*;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.service.TenantWorkspaceRoleService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目角色控制器
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
@RestController
@RequestMapping("/v1/tenant/workspace/role")
@Api(tags = "项目角色管理")
@Validated
public class TenantWorkspaceRoleController {

    @Resource
    private TenantWorkspaceRoleService tenantWorkspaceRoleService;

    /**
     * 新增项目角色
     *
     * @param workspaceRoleAddDto 项目角色详情
     * @return 成功、失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增项目角色", notes = "新增项目角色")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "新增项目角色", action = "新增")
    @PreAuthorize("hasPermission('TenantWorkspaceRoleController','tenant:workspace:role:add')")
    public DefaultRspDTO<NoBody> addWorkspaceRole(@Validated @RequestBody TenantWorkspaceRoleAddDTO workspaceRoleAddDto) {
        if (JudgeUtils.isEmpty(workspaceRoleAddDto.getMenuIds()) && JudgeUtils.isBlank(workspaceRoleAddDto.getWorkspaceRoleId())) {
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_ROLE_MENU_ID_NOT_NULL);
        }
        TenantWorkspaceRoleBO tenantWorkspaceRole = new TenantWorkspaceRoleBO();
        BeanUtils.copyProperties(tenantWorkspaceRole, workspaceRoleAddDto);
        if (JudgeUtils.isNotEmpty(workspaceRoleAddDto.getMenuIds())) {
            tenantWorkspaceRole.setMenuIds(workspaceRoleAddDto.getMenuIds());
        }
        tenantWorkspaceRoleService.addWorkspaceRole(SecurityUtils.getLoginName(), tenantWorkspaceRole);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 修改项目角色
     *
     * @param tenantWorkspaceRoleUpdateDto 项目角色详情
     * @return 成功、失败
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改项目角色", notes = "修改项目角色")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "修改项目角色", action = "修改")
    @PreAuthorize("hasPermission('TenantWorkspaceRoleController','tenant:workspace:role:update')")
    public DefaultRspDTO<NoBody> updateWorkspaceRole(@Validated @RequestBody TenantWorkspaceRoleUpdateDTO tenantWorkspaceRoleUpdateDto) {
        TenantWorkspaceRoleBO tenantWorkspaceRole = new TenantWorkspaceRoleBO();
        BeanUtils.copyProperties(tenantWorkspaceRole, tenantWorkspaceRoleUpdateDto);
        if (JudgeUtils.isNotEmpty(tenantWorkspaceRoleUpdateDto.getMenuIds())) {
            tenantWorkspaceRole.setMenuIds(tenantWorkspaceRoleUpdateDto.getMenuIds());
        }
        tenantWorkspaceRoleService.updateWorkspaceRole(SecurityUtils.getLoginName(), tenantWorkspaceRole);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除项目角色
     *
     * @param tenantWorkspaceRoleDeleteDto 项目角色ID
     * @return 成功、失败
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除项目角色", notes = "删除项目角色")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "删除项目角色", action = "删除")
    @PreAuthorize("hasPermission('TenantWorkspaceRoleController','tenant:workspace:role:delete')")
    public DefaultRspDTO<NoBody> deleteWorkspaceRole(@Validated @RequestBody TenantWorkspaceRoleDeleteDTO tenantWorkspaceRoleDeleteDto) {
        tenantWorkspaceRoleService.deleteWorkspaceRole(tenantWorkspaceRoleDeleteDto.getWorkspaceRoleId());
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 批量删除项目角色
     *
     * @param tenantWorkspaceRoleDeletesDto 项目角色ID集合
     * @return 成功、失败
     */
    @PostMapping("/deletes")
    @ApiOperation(value = "批量删除项目角色", notes = "批量删除项目角色")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "批量删除项目角色", action = "删除")
    @PreAuthorize("hasPermission('TenantWorkspaceRoleController','tenant:workspace:role:delete')")
    public DefaultRspDTO<NoBody> deleteWorkspaceRoles(@Validated @RequestBody TenantWorkspaceRoleDeletesDTO tenantWorkspaceRoleDeletesDto) {
        tenantWorkspaceRoleService.deleteWorkspaceRoles(tenantWorkspaceRoleDeletesDto.getWorkspaceRoleIds());
        return DefaultRspDTO.newSuccessInstance();
    }


    /**
     * 查询项目角色详情
     *
     * @param workspaceRoleId 项目角色ID
     * @return 项目角色详情
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询项目角色详情", notes = "查询项目角色详情")
    @ApiResponse(code = 200, message = "项目角色详情")
    @LogRecord(title = "查询项目角色详情", action = "查询")
    @PreAuthorize("hasPermission('TenantWorkspaceRoleController','tenant:workspace:role:query')")
    public DefaultRspDTO<TenantWorkspaceRoleDTO> getWorkspaceRoleInfo(
            @ApiParam(name = "workspaceRoleId", value = "项目角色ID", required = true, example = "1239035dc433428c84ff434379374157")
            @RequestParam("workspaceRoleId") String workspaceRoleId) {
        TenantWorkspaceRoleDTO tenantWorkspaceRoleDto = new TenantWorkspaceRoleDTO();
        TenantWorkspaceRoleBO tenantWorkspaceRole = tenantWorkspaceRoleService.getWorkspaceRoleInfo(workspaceRoleId);
        if (JudgeUtils.isNotNull(tenantWorkspaceRole)) {
            BeanUtils.copyProperties(tenantWorkspaceRoleDto, tenantWorkspaceRole);
            if (JudgeUtils.isNotEmpty(tenantWorkspaceRole.getMenuIds())) {
                tenantWorkspaceRoleDto.setMenuIds(tenantWorkspaceRole.getMenuIds());
            }
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaceRoleDto);
    }

    /**
     * 查询项目角色列表
     *
     * @param workspaceId       项目ID
     * @param workspaceRoleName 项目角色名称
     * @return 项目角色列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询项目角色列表", notes = "查询项目角色列表")
    @ApiResponse(code = 200, message = "项目角色列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('TenantWorkspaceRoleController','tenant:workspace:role:query')")
    public DefaultRspDTO<List<TenantWorkspaceRoleDTO>> getWorkspaceRoleList(@ApiParam(name = "workspaceId",
            value = "项目ID",
            required = true,
            example = "2339035dc433428c84ff434379374157")
    @RequestParam("workspaceId") String workspaceId,
            @ApiParam(name = "workspaceRoleName", value = "项目角色名称", example = "项目角色名称")
            @RequestParam(name = "workspaceRoleName", required = false) String workspaceRoleName) {
        TenantWorkspaceRoleBO tenantWorkspaceRole = new TenantWorkspaceRoleBO();
        tenantWorkspaceRole.setWorkspaceId(workspaceId);
        tenantWorkspaceRole.setWorkspaceRoleName(workspaceRoleName);
        List<TenantWorkspaceRoleBO> workspaceRoles = tenantWorkspaceRoleService.getDetailWorkspaceRoles(tenantWorkspaceRole);
        List<TenantWorkspaceRoleDTO> tenantWorkspaceRoles = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(workspaceRoles)) {
            tenantWorkspaceRoles = BeanConvertUtil.convertList(workspaceRoles, TenantWorkspaceRoleDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaceRoles);
    }

    /**
     * 分页查询项目角色列表
     *
     * @param workspaceId       项目ID
     * @param workspaceRoleName 项目角色名称
     * @param pageNum           第几页
     * @param pageSize          一页多少条
     * @return
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询项目角色列表", notes = "分页查询项目角色列表")
    @ApiResponse(code = 200, message = "分页项目角色列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('TenantWorkspaceRoleController','tenant:workspace:role:query')")
    public DefaultRspDTO<PageInfo<TenantWorkspaceRoleDTO>> getWorkspaceRoleListByPage(@ApiParam(name = "workspaceId",
            value = "项目ID",
            required = true,
            example = "2339035dc433428c84ff434379374157")
    @RequestParam("workspaceId") String workspaceId,
            @ApiParam(name = "workspaceRoleName", value = "项目角色名称", example = "项目角色名称")
            @RequestParam(name = "workspaceRoleName", required = false) String workspaceRoleName,
            @ApiParam(name = "pageNum", value = "第几页", defaultValue = "1", example = "1")
            @RequestParam(name = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(name = "pageSize", value = "一页多少条", defaultValue = "10", example = "10")
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        TenantWorkspaceRoleBO tenantWorkspaceRole = new TenantWorkspaceRoleBO();
        tenantWorkspaceRole.setWorkspaceRoleName(workspaceRoleName);
        tenantWorkspaceRole.setWorkspaceId(workspaceId);
        PageInfo<TenantWorkspaceRoleBO> workspaceRolePage = tenantWorkspaceRoleService.getDetailWorkspaceRolesByPage(pageNum,
                pageSize,
                tenantWorkspaceRole);
        PageInfo pageInfo = new PageInfo<>(new ArrayList<>());
        BeanUtils.copyProperties(pageInfo, workspaceRolePage);
        if (JudgeUtils.isNotEmpty(workspaceRolePage.getList())) {
            pageInfo.setList(BeanConvertUtil.convertList(workspaceRolePage.getList(), TenantWorkspaceRoleDTO.class));
        }
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }


}
