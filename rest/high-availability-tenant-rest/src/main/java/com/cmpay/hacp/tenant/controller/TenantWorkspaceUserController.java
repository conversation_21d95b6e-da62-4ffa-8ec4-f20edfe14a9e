package com.cmpay.hacp.tenant.controller;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.tenant.bo.TenantWorkspaceUserBO;
import com.cmpay.hacp.dto.tenant.*;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.service.TenantWorkspaceUserService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目成员管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/tenant/workspace/user")
@Api(tags = "项目成员管理")
@Validated
public class TenantWorkspaceUserController {

    @Resource
    private TenantWorkspaceUserService tenantWorkspaceUserService;

    /**
     * 新增项目成员
     *
     * @param tenantWorkspaceUserAddDto 项目成员详情
     * @return 成功\失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增项目成员", notes = "新增项目成员")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "新增项目成员", action = "新增")
    @PreAuthorize("hasPermission('TenantWorkspaceUserController','tenant:workspace:member:add')")
    public DefaultRspDTO<NoBody> addWorkspaceUser(@Validated @RequestBody TenantWorkspaceUserAddDTO tenantWorkspaceUserAddDto) {
        TenantWorkspaceUserBO tenantWorkspaceUser = new TenantWorkspaceUserBO();
        BeanUtils.copyProperties(tenantWorkspaceUser, tenantWorkspaceUserAddDto);
        if (JudgeUtils.isNotEmpty(tenantWorkspaceUserAddDto.getWorkspaceRoleIds())) {
            tenantWorkspaceUser.setWorkspaceRoleIds(tenantWorkspaceUserAddDto.getWorkspaceRoleIds());
        }
        //判断是否是项目成员
        TenantWorkspaceUserBO tenantWorkspaceUserInfo = tenantWorkspaceUserService.getTenantWorkspaceUserInfo(tenantWorkspaceUser.getWorkspaceId(),
                tenantWorkspaceUser.getUserId());
        if (JudgeUtils.isNotNull(tenantWorkspaceUserInfo)) {
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_USER_EXIST);
        }
        tenantWorkspaceUserService.addWorkspaceUser(SecurityUtils.getLoginName(), tenantWorkspaceUser);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 修改项目成员
     *
     * @param tenantWorkspaceUserUpdateDto 项目成员详情
     * @return 成功\失败
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改项目成员", notes = "修改项目成员")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "修改项目成员", action = "修改")
    @PreAuthorize("hasPermission('TenantWorkspaceUserController','tenant:workspace:member:update')")
    public DefaultRspDTO<NoBody> updateWorkspaceUser(@Validated @RequestBody TenantWorkspaceUserUpdateDTO tenantWorkspaceUserUpdateDto) {
        TenantWorkspaceUserBO tenantWorkspaceUser = new TenantWorkspaceUserBO();
        BeanUtils.copyProperties(tenantWorkspaceUser, tenantWorkspaceUserUpdateDto);
        if (JudgeUtils.isNotEmpty(tenantWorkspaceUserUpdateDto.getWorkspaceRoleIds())) {
            tenantWorkspaceUser.setWorkspaceRoleIds(tenantWorkspaceUserUpdateDto.getWorkspaceRoleIds());
        }
        tenantWorkspaceUserService.updateWorkspaceUser(SecurityUtils.getLoginName(), tenantWorkspaceUser);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 批量删除项目成员
     *
     * @param tenantWorkspaceUserDeletesDto 项目成员ID集合
     * @return 成功\失败
     */
    @PostMapping("/deletes")
    @ApiOperation(value = "批量删除项目成员", notes = "批量删除项目成员")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "批量删除项目成员", action = "删除")
    @PreAuthorize("hasPermission('TenantWorkspaceUserController','tenant:workspace:member:delete')")
    public DefaultRspDTO<NoBody> deletesWorkspaceUser(@Validated @RequestBody TenantWorkspaceUserDeletesDTO tenantWorkspaceUserDeletesDto) {
        tenantWorkspaceUserService.deletesWorkspaceUser(tenantWorkspaceUserDeletesDto.getIds());
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除项目成员
     *
     * @param tenantWorkspaceUserDeleteDto 项目成员ID
     * @return 成功\失败
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除项目成员", notes = "删除项目成员")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "删除项目成员", action = "删除")
    @PreAuthorize("hasPermission('TenantWorkspaceUserController','tenant:workspace:member:delete')")
    public DefaultRspDTO<NoBody> deleteWorkspaceUser(@Validated @RequestBody TenantWorkspaceUserDeleteDTO tenantWorkspaceUserDeleteDto) {
        tenantWorkspaceUserService.deleteWorkspaceUser(tenantWorkspaceUserDeleteDto.getId());
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 查询项目成员详情
     *
     * @param id 主键ID
     * @return 项目成员详情
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询项目成员详情", notes = "查询项目成员详情")
    @ApiResponse(code = 200, message = "项目成员详情")
    @LogRecord(title = "查询项目成员详情", action = "查询")
    @PreAuthorize("hasPermission('TenantWorkspaceUserController','tenant:workspace:member:query')")
    public DefaultRspDTO<TenantWorkspaceUserDTO> getWorkspaceUserInfo(@ApiParam(name = "id",
            value = "主键ID",
            required = true,
            example = "0179035dc433428c84ff434379374157")
    @RequestParam(name = "id", required = true) @NotBlank(message = "HAC00009") String id) {
        TenantWorkspaceUserBO tenantWorkspaceUser = tenantWorkspaceUserService.getWorkspaceUserInfo(id);
        TenantWorkspaceUserDTO tenantWorkspaceUserDto = new TenantWorkspaceUserDTO();
        if (JudgeUtils.isNotNull(tenantWorkspaceUser)) {
            BeanUtils.copyProperties(tenantWorkspaceUserDto, tenantWorkspaceUser);
            if (JudgeUtils.isNotEmpty(tenantWorkspaceUser.getWorkspaceRoleIds())) {
                tenantWorkspaceUser.setWorkspaceRoleIds(tenantWorkspaceUser.getWorkspaceRoleIds());
            }
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaceUserDto);
    }

    /**
     * 查询项目成员列表
     *
     * @param workspaceRoleId 项目角色ID
     * @return 项目成员列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询项目成员列表", notes = "查询项目成员列表")
    @ApiResponse(code = 200, message = "项目成员列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('TenantWorkspaceUserController','tenant:workspace:member:query')")
    public DefaultRspDTO<List<TenantWorkspaceUserDTO>> getWorkspaceUsers(
            @ApiParam(name = "workspaceId", value = "项目ID", required = true, example = "3279035dc433428c84ff434379374157")
            @RequestParam(name = "workspaceId", required = true) @NotBlank(message = "HAC00004") String workspaceId,
            @ApiParam(name = "workspaceRoleId", value = "项目角色ID", required = false, example = "3279035dc433428c84ff434379374157")
            @RequestParam(name = "workspaceRoleId", required = false) String workspaceRoleId) {
        TenantWorkspaceUserBO tenantWorkspaceUser = new TenantWorkspaceUserBO();
        tenantWorkspaceUser.setWorkspaceId(workspaceId);
        tenantWorkspaceUser.setWorkspaceRoleId(workspaceRoleId);
        List<TenantWorkspaceUserBO> workspaceUsers = tenantWorkspaceUserService.getWorkspaceUsers(tenantWorkspaceUser);
        List<TenantWorkspaceUserDTO> tenantWorkspaceUsers = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(workspaceUsers)) {
            tenantWorkspaceUsers = BeanConvertUtil.convertList(workspaceUsers, TenantWorkspaceUserDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaceUsers);
    }

    /**
     * 分页查询项目成员列表
     *
     * @param workspaceId     项目ID
     * @param workspaceRoleId 项目角色ID
     * @return 项目成员列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询项目成员列表", notes = "分页查询项目成员列表")
    @ApiResponse(code = 200, message = "项目成员列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('TenantWorkspaceUserController','tenant:workspace:member:query')")
    public DefaultRspDTO<PageInfo<TenantWorkspaceUserDTO>> getWorkspaceUsersByPage(
            @ApiParam(name = "workspaceId", value = "项目ID", required = true, example = "3279035dc433428c84ff434379374157")
            @RequestParam(name = "workspaceId", required = true) @NotBlank(message = "HAC00004") String workspaceId,
            @ApiParam(name = "workspaceRoleId", value = "项目角色ID", required = false, example = "3279035dc433428c84ff434379374157")
            @RequestParam(name = "workspaceRoleId", required = false) String workspaceRoleId,
            @ApiParam(name = "pageNum", value = "第几页", required = false, defaultValue = "1", example = "1")
            @RequestParam(name = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(name = "pageSize", value = "一页多少条", required = false, defaultValue = "10", example = "10")
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        TenantWorkspaceUserBO tenantWorkspaceUser = new TenantWorkspaceUserBO();
        tenantWorkspaceUser.setWorkspaceId(workspaceId);
        tenantWorkspaceUser.setWorkspaceRoleId(workspaceRoleId);
        PageInfo<TenantWorkspaceUserBO> workspaceUserPage = tenantWorkspaceUserService.getWorkspaceUsersByPage(pageNum,
                pageSize,
                tenantWorkspaceUser);
        PageInfo pageInfo = new PageInfo<>(new ArrayList<>());
        if (JudgeUtils.isNull(workspaceUserPage)) {
            return DefaultRspDTO.newSuccessInstance(pageInfo);
        }
        BeanUtils.copyProperties(pageInfo, workspaceUserPage);
        if (JudgeUtils.isEmpty(workspaceUserPage.getList())) {
            return DefaultRspDTO.newSuccessInstance(pageInfo);
        }
        List<TenantWorkspaceUserDTO> tenantWorkspaceUsers = new ArrayList<>();
        workspaceUserPage.getList().stream().forEach(workspaceUser -> {
            TenantWorkspaceUserDTO tenantWorkspaceUserDto = new TenantWorkspaceUserDTO();
            BeanUtils.copyProperties(tenantWorkspaceUserDto, workspaceUser);
            if (JudgeUtils.isNotEmpty(workspaceUser.getWorkspaceRoles())) {
                tenantWorkspaceUserDto.setWorkspaceRoles(BeanConvertUtil.convertList(workspaceUser.getWorkspaceRoles(),
                        TenantWorkspaceRole.class));
            }
            tenantWorkspaceUsers.add(tenantWorkspaceUserDto);
        });
        pageInfo.setList(tenantWorkspaceUsers);
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    /**
     * 用戶是否是项目成员
     *
     * @param workspaceId 项目ID
     * @param userId      用户ID
     * @return 是、不是
     */
    @GetMapping("/exist")
    @ApiOperation(value = "用戶是否是项目成员", notes = "用戶是否是项目成员")
    @ApiResponse(code = 200, message = "是、不是")
    @LogNoneRecord
    public DefaultRspDTO<NoBody> existWorkspaceUser(
            @ApiParam(name = "workspaceId", value = "项目ID", required = true, example = "3279035dc433428c84ff434379374157")
            @RequestParam(name = "workspaceId", required = true) @NotBlank(message = "HAC00004") String workspaceId,
            @ApiParam(name = "userId", value = "项目成员ID", required = true, example = "3279035dc433428c84ff434379374157")
            @RequestParam(name = "userId", required = true) @NotBlank(message = "HAC00022") String userId) {
        tenantWorkspaceUserService.existWorkspaceUser(workspaceId, userId);
        return DefaultRspDTO.newSuccessInstance();
    }

}
