package com.cmpay.hacp.inspection.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 规则监控字段DTO
 */
@Data
@Schema(description = "规则监控字段DTO")
public class RulePluginResultDTO {

    @Schema(description = "插件ID", required = true, example = "1")
    @NotBlank(message = "HAI10001")
    private String pluginId;

    @NotNull(message = "HAI50308")
    @Valid
    private RuleConditionGroupInfoDTO ruleConditionInfo;


    @NotEmpty(message = "HAI50307")
    @Valid
    private List<RuleConditionDTO> ruleConditions;

}
