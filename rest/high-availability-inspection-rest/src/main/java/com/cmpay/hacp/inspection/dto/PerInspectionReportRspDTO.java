package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 按次巡检报告响应DTO
 */
@Data
@Schema(description = "按次巡检报告响应DTO")
public class PerInspectionReportRspDTO {

    @Schema(description = "报告ID", example = "RPT-202503240003")
    private String reportId;

    @Schema(description = "关联的任务执行记录ID", example = "EXEC-202503240003")
    private String taskExecutionId;

    @Schema(description = "任务ID", example = "TASK-000001")
    private String taskId;

    @Schema(description = "任务名称", example = "网络连通性检测")
    private String taskName;

    @Schema(description = "触发方式：0-定时触发，1-手动触发", example = "0")
    private TriggerMode triggerMode;

    @Schema(description = "执行状态：0-待执行，1-执行中，2-已完成，3-执行失败，4-已取消，5-告警，6-超时，7-已停止", example = "2")
    private ExecutionStatus executionStatus;

    @Schema(description = "开始时间", example = "2025-03-24T10:00:00")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2025-03-24T10:05:30")
    private LocalDateTime endTime;

    @Schema(description = "执行耗时（毫秒）", example = "330000")
    private Long executionDuration;

    @Schema(description = "执行耗时（格式化字符串）", example = "5分30秒")
    private String executionTimeStr;

    @Schema(description = "执行结果统计", example = "{\"passed\": 3, \"warning\": 1, \"failed\": 2}")
    private String resultStats;

    @Schema(description = "报告生成时间", example = "2025-03-24T10:06:00")
    private LocalDateTime generateTime;
}
