package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.task.model.ContainerResource;
import com.cmpay.hacp.inspection.domain.task.model.Resource;
import com.cmpay.hacp.inspection.domain.task.model.VmResource;
import com.cmpay.hacp.inspection.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.ObjectFactory;
import org.mapstruct.SubclassMapping;

@Mapper(componentModel = "spring")
public interface ResourceDTOMapper {
    @SubclassMapping(source = VmResourceDTO.class, target = VmResource.class)
    @SubclassMapping(source = ContainerResourceDTO.class, target = ContainerResource.class)
    Resource toResource(ResourceDTO dto);

    @ObjectFactory
    default Resource createResource(ResourceDTO dto) {
        throw new IllegalArgumentException("Unknown DTO type: " + dto.getClass().getName());
    }

    @SubclassMapping(source = VmResource.class, target = VmResourceDTO.class)
    @SubclassMapping(source = ContainerResource.class, target = ContainerResourceDTO.class)
    ResourceDTO toResourceDTO(Resource config);

    @ObjectFactory
    default ResourceDTO createResourceDTO(Resource scheduleConfig) {
        throw new IllegalArgumentException("Unknown ScheduleConfig type: " + scheduleConfig.getClass().getName());
    }
}
