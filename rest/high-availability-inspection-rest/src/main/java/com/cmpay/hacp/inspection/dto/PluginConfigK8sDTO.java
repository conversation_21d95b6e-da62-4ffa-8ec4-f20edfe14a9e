package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.fasterxml.jackson.annotation.JsonTypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("3")
public class PluginConfigK8sDTO extends PluginConfigDTO{

    public PluginConfigK8sDTO() {
        this.setType(PluginType.K8S_DEPLOYMENT);
    }

}
