package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ConditionLogic;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class RuleConditionGroupInfoDTO {

    private String conditionGroupId;

    /**
     * 条件逻辑
     */
    private ConditionLogic conditionLogic;

    @Schema(description = "治理建议", required = true, example = "请输入当规则触发时的治理建议")
    @NotBlank(message = "HAI50510")
    private String suggest;

}
