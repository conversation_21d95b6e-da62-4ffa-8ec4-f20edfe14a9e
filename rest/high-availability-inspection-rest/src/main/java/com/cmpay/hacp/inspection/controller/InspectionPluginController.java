package com.cmpay.hacp.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.plugin.IndicatorMetadataService;
import com.cmpay.hacp.inspection.application.service.InspectionPluginService;
import com.cmpay.hacp.inspection.application.service.TagService;
import com.cmpay.hacp.inspection.assembler.IndicatorDTOMapper;
import com.cmpay.hacp.inspection.assembler.IndicatorParamDTOMapper;
import com.cmpay.hacp.inspection.assembler.InspectionPluginDTOMapper;
import com.cmpay.hacp.inspection.assembler.TagDTOMapper;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;
import com.cmpay.hacp.inspection.dto.*;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 巡检插件管理
 */
@RestController
@RequestMapping(VersionApi.VERSION_V1 + "/inspection/plugin")
@Tag(name = "巡检插件管理")
@RequiredArgsConstructor
public class InspectionPluginController {

    private final InspectionPluginService inspectionPluginService;
    private final IndicatorMetadataService indicatorMetadataService;
    private final TagService tagService;
    private final InspectionPluginDTOMapper inspectionPluginDTOMapper;
    private final IndicatorParamDTOMapper indicatorParamDTOMapper;
    private final IndicatorDTOMapper indicatorDTOMapper;
    private final TagDTOMapper tagDTOMapper;

    /**
     * 创建巡检插件
     *
     * @param reqDTO 请求参数
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建巡检插件", description = "创建新的巡检插件")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:create')")
    public DefaultRspDTO<String> createPlugin(@Validated @RequestBody InspectionPluginReqDTO reqDTO) {
        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPlugin(reqDTO);

        String pluginId = inspectionPluginService.createPlugin(inspectionPlugin);

        return DefaultRspDTO.newSuccessInstance(pluginId);
    }

    /**
     * 更新巡检插件
     *
     * @param reqDTO 请求参数
     * @return 更新结果
     */
    @PostMapping("/update")
    @Operation(summary = "更新巡检插件", description = "更新现有的巡检插件")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:update')")
    public DefaultRspDTO<NoBody> updatePlugin(@Validated @RequestBody InspectionPluginReqDTO reqDTO) {
        // 验证插件ID
        if (reqDTO.getPluginId() == null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_ID_REQUIRED);
        }

        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPlugin(reqDTO);

        // 调用服务更新插件
        inspectionPluginService.updatePlugin(inspectionPlugin);

        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除巡检插件
     *
     * @param pluginId 插件ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{pluginId}")
    @Operation(summary = "删除巡检插件", description = "删除指定的巡检插件")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:delete')")
    public DefaultRspDTO<NoBody> deletePlugin(
            @Parameter(name = "pluginId", description = "插件ID", required = true)
            @PathVariable("pluginId") String pluginId) {
        inspectionPluginService.deletePlugin(pluginId);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 获取巡检插件详情
     *
     * @param pluginId 插件ID
     * @return 插件详情
     */
    @GetMapping("/detail/{pluginId}")
    @Operation(summary = "获取巡检插件详情", description = "获取指定巡检插件的详细信息")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<InspectionPluginRspDTO> getPluginDetail(
            @Parameter(name = "pluginId", description = "插件ID", required = true)
            @PathVariable("pluginId") String pluginId) {

        // 获取插件详情
        InspectionPlugin inspectionPlugin = inspectionPluginService.getPluginDetail(pluginId);

        if (inspectionPlugin == null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_INFO_NOT_EXIST);
        }

        List<Long> tagIds = inspectionPlugin.getTagIds();
        List<com.cmpay.hacp.inspection.domain.tag.model.Tag> tags = tagService.getTagByTagIds(tagIds);
        InspectionPluginRspDTO rspDTO = inspectionPluginDTOMapper.toInspectionPluginRspDTO(inspectionPlugin);
        rspDTO.setTags(tagDTOMapper.toTagDTOList(tags));

        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }

    /**
     * 获取巡检插件详情，插件规则调用
     *
     * @param pluginId 插件ID
     * @return 插件详情
     */
    @GetMapping("/detail/params/{pluginId}")
    @Operation(summary = "获取巡检插件参数详情-插件规则所需", description = "获取指定巡检插件的详细信息")
    @ApiResponse(responseCode = "200", description = "成功")
    public DefaultRspDTO<InspectionPluginRspDTO> getPluginParamsDetail(
            @Parameter(name = "pluginId", description = "插件ID", required = true)
            @PathVariable("pluginId") String pluginId) {
        return this.getPluginDetail(pluginId);
    }


    /**
     * 分页查询巡检插件列表
     *
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询巡检插件列表", description = "根据条件分页查询巡检插件列表")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<PageDTO<InspectionPluginRspDTO>> getPluginPage(@Validated @RequestBody InspectionPluginQueryReqDTO reqDTO) {
        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPlugin(reqDTO);

        IPage<InspectionPlugin> page = inspectionPluginService.getPluginPage(reqDTO.getPage(), inspectionPlugin);
        List<InspectionPluginRspDTO> rspDTOList = inspectionPluginDTOMapper.toInspectionPluginRspDTOList(page.getRecords());

        // 批量查询并设置标签
        if (!page.getRecords().isEmpty()) {
            giveRspDTOTags(page.getRecords(), rspDTOList);
        }

        PageDTO<InspectionPluginRspDTO> result = new PageDTO<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(rspDTOList);

        return DefaultRspDTO.newSuccessInstance(result);
    }

    @PostMapping("/list")
    @Operation(summary = "查询巡检插件列表", description = "根据条件查询巡检插件列表")
    @ApiResponse(responseCode = "200", description = "成功")
    @LogNoneRecord
    public DefaultRspDTO<List<InspectionPluginRspDTO>> getPluginList(@Validated @RequestBody InspectionPluginQueryListReqDTO reqDTO) {
        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPluginQueryList(reqDTO);

        List<InspectionPlugin> List = inspectionPluginService.getPluginList(inspectionPlugin);
        List<InspectionPluginRspDTO> result = inspectionPluginDTOMapper.toInspectionPluginRspDTOList(List);

        return DefaultRspDTO.newSuccessInstance(result);
    }

    @GetMapping("/indicator/{indicatorId}")
    @Operation(
            summary = "获取指标参数定义",
            description = "根据指标ID获取该指标的所有参数定义信息，包括参数名称、顺序、描述等详细信息"
    )
    @ApiResponse(responseCode = "200", description = "成功获取指标参数定义",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = DefaultRspDTO.class))
    )
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<List<IndicatorParamDTO>> findIndicatorParameters(
            @Parameter(name = "indicatorName", description = "指标唯一标识符，用于查询特定指标的参数定义", required = true, example = "cpu_usage_rate")
            @PathVariable("indicatorName") String indicatorName) {
        IndicatorDefinition indicatorDefinition = indicatorMetadataService.findIndicatorByName(indicatorName);
        return DefaultRspDTO.newSuccessInstance(indicatorParamDTOMapper.toIndicatorParamDTO(indicatorDefinition.getInputParams()));
    }

    @GetMapping("/indicator/type/{indicatorType}")
    @Operation(
            summary = "获取指标定义列表",
            description = "根据指标类型获取该类型下所有可用的指标定义列表，支持中间件、主机、容器三种类型"
    )
    @ApiResponse(responseCode = "200", description = "成功获取指标定义列表",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = DefaultRspDTO.class))
    )
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<List<IndicatorDTO>> findIndicatorsByType(
            @Parameter(name = "indicatorType", description = "指标类型，支持的值：mid(中间件)、host(主机)、container(容器)", required = true, example = "mid",
                    schema = @Schema(type = "string", allowableValues = {"mid", "host", "container"})
            )
            @PathVariable("indicatorType") IndicatorType indicatorType) {
        List<IndicatorDefinition> indicatorDefinitions = indicatorMetadataService.findIndicatorsByType(indicatorType);
        return DefaultRspDTO.newSuccessInstance(indicatorDTOMapper.toIndicatorDTOList(indicatorDefinitions));
    }

    private void giveRspDTOTags(List<InspectionPlugin> records, List<InspectionPluginRspDTO> rspDTOList) {
        // 1. 构建pluginId到tagIds的映射
        Map<String, List<Long>> pluginTagIdsMap = records.stream()
                .collect(Collectors.toMap(
                        InspectionPlugin::getPluginId,
                        plugin -> plugin.getTagIds() != null ? plugin.getTagIds() : Collections.emptyList()
                ));

        // 2. 批量查询标签数据
        Map<String, List<TagDTO>> pluginTagsMap = new HashMap<>();
        pluginTagIdsMap.forEach((pluginID, tagIds) -> {
            pluginTagsMap.put(pluginID, inspectionPluginDTOMapper.toTagDtoList(tagService.getTagByTagIds(tagIds)));
        });

        // 3. 填充到响应DTO
        rspDTOList.forEach(dto -> {
            List<TagDTO> tags = pluginTagsMap.getOrDefault(dto.getPluginId(), Collections.emptyList());
            dto.setTags(tags);
        });
    }

}
