package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.DiscriminatorMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Schema(
        description = "调度配置基类DTO",
        discriminatorProperty = "type",
        discriminatorMapping = {
                @DiscriminatorMapping(value = "1", schema = PluginConfigScriptDTO.class),
                @DiscriminatorMapping(value = "2", schema = PluginConfigScriptDTO.class),
                @DiscriminatorMapping(value = "3", schema = PluginConfigK8sDTO.class),
        }
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = PluginConfigScriptDTO.class, names = {"1","2"}),
        @JsonSubTypes.Type(value = PluginConfigK8sDTO.class, names = {"3"}),
})
public class PluginConfigDTO {
    private PluginType type;

    @Schema(description = "输出字段定义列表")
    @NotEmpty(message = "HAI10022")
    @Valid
    private List<PluginOutputFieldDTO> results;
}
