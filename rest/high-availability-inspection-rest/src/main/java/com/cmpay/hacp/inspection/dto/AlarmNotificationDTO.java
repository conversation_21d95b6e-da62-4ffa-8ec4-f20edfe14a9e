package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.AlertCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AlarmNotificationDTO {

    @Schema(description = "告警通知条件")
    @NotNull(message = "HAI50410")
    @Valid
    private List<AlertCondition> alertConditions;

    @Schema(description = "邮件通知，分号分割")
    private String email;
}
