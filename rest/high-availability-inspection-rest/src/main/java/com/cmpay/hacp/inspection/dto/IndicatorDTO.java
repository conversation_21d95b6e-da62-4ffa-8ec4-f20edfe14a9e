package com.cmpay.hacp.inspection.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "指标定义DTO")
public class IndicatorDTO {
    /**
     * 指标名
     */
    @Schema(description = "指标名称", example = "CPU使用率")
    private String indicatorName;

    /**
     * 指标描述
     */
    @Schema(description = "指标描述", example = "监控系统CPU使用率指标")
    private String description;
}
