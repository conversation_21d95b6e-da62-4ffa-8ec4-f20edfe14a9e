package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.tag.model.Tag;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 巡检插件对象转换器
 */
@Mapper(componentModel = "spring",uses = PluginConfigDTOMapper.class)
public interface InspectionPluginDTOMapper {

    @Mapping(target = "auditInfo", ignore = true)
    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    InspectionPlugin toInspectionPlugin(InspectionPluginReqDTO reqDTO);

    @Mapping(target = "tags", ignore = true)
    InspectionPluginRspDTO toInspectionPluginRspDTO(InspectionPlugin inspectionPlugin);

    @Mapping(target = "deployEnvs", ignore = true)
    @Mapping(target = "pluginId", ignore = true)
    @Mapping(target = "auditInfo", ignore = true)
    @Mapping(target = "key", ignore = true)
    @Mapping(target = "pluginConfig", ignore = true)
    InspectionPlugin toInspectionPlugin(InspectionPluginQueryReqDTO reqDTO);

    @Mapping(target = "pluginId", ignore = true)
    @Mapping(target = "auditInfo", ignore = true)
    @Mapping(target = "key", ignore = true)
    @Mapping(target = "name", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "tagIds", ignore = true)
    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    @Mapping(target = "pluginConfig", ignore = true)
    @Mapping(target = "deployEnvs", ignore = true)
    InspectionPlugin toInspectionPluginQueryList(InspectionPluginQueryListReqDTO reqDTO);

    List<InspectionPluginRspDTO> toInspectionPluginRspDTOList(List<InspectionPlugin> inspectionPluginList);

    List<TagDTO> toTagDtoList(List<Tag> tagByTagIds);
}
