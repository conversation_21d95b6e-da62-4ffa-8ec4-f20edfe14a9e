package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.condition.model.RulePluginResult;
import com.cmpay.hacp.inspection.dto.RulePluginResultDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring",uses = {RuleConditionGroupInfoDTOMapper.class,RuleConditionDTOMapper.class})
public interface RulePluginResultDTOMapper {

    @Mapping(target = "ruleId", ignore = true)
    @Mapping(target = "conditionGroupId", ignore = true )
    RulePluginResult toRulePluginResult(RulePluginResultDTO reqDTO);

    RulePluginResultDTO toRulePluginResultDTO(RulePluginResult result);
}
