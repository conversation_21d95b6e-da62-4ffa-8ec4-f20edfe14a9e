package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 巡检插件查询请求DTO
 */
@Data
@Schema(description = "巡检插件查询请求DTO")
public class InspectionPluginQueryListReqDTO {

    @Schema(description = "插件类型：1-SHELL脚本, 2-PYTHON脚本, 3-页面检查, 4-自动化测试", example = "1")
    @NotNull(message = "HAI10010")
    private PluginType type;

}
