package com.cmpay.hacp.inspection.controller;

import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.inspection.application.service.CommonConfigService;
import com.cmpay.lemon.framework.controller.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "巡检公共配置获取")
@RestController
@RequestMapping(VersionApi.VERSION_V1 +"/inspection/common")
@RequiredArgsConstructor
@Slf4j
public class CommonConfigController extends BaseController {

    private final CommonConfigService commonConfigService;

}
