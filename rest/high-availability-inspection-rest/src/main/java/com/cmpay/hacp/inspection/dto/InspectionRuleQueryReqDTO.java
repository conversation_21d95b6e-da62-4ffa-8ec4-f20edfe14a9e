package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.enums.RuleStatusEnum;
import com.cmpay.hacp.inspection.domain.model.enums.RuleType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "巡检规则查询请求DTO")
public class InspectionRuleQueryReqDTO {


    @Schema(description = "规则名称", example = "CPU 使用率阈值检查")
    private String name;

    @Schema(description = "规则状态：0-禁用,1-启用", required = true, example = "0")
    private RuleStatusEnum status;

    /**
     * @see RuleType
     */
    @Schema(description = "规则类型：1-指标、2-日志、3-可用性", example = "1")
    private RuleType type;

    /**
     * 规则标签关联
     */
    @Schema(description = "标签ID列表", example = "[1, 2, 3]")
    private List<Long> tagIds;


    @Schema(description = "开始时间", example = "2025-06-19 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2025-06-19 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    private PageDTO<?> page = new PageDTO<>();

}
