package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("2")
@Schema(description = "容器配置")
public class ContainerResourceDTO extends ResourceDTO {

    private String cluster;

    private String namespace;

    private String workspace;

    public ContainerResourceDTO() {
        setResourceType(ResourceType.CONTAINER);
    }
}
