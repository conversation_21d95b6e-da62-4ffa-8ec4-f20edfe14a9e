package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.dto.IndicatorParamDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface IndicatorParamDTOMapper {
    List<IndicatorParamDTO> toIndicatorParamDTO(List<IndicatorDefinition.IndicatorParam> indicatorParamList);
}
