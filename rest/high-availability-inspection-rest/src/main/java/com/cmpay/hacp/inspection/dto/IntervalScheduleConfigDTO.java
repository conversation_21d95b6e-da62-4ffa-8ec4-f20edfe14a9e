package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.IntervalUnit;
import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 固定间隔调度配置DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("INTERVAL")
@Schema(description = "固定间隔调度配置DTO")
public class IntervalScheduleConfigDTO extends ScheduleConfigDTO {

    @Schema(description = "间隔值", required = true, example = "30")
    @NotNull(message = "HAI50407")
    @Min(value = 1, message = "HAI50408")
    private Integer intervalValue;

    @Schema(description = "间隔单位", required = true, example = "MINUTE")
    @NotNull(message = "HAI50409")
    private IntervalUnit intervalUnit;

    public IntervalScheduleConfigDTO() {
        setType(ScheduleType.FIXED_INTERVAL);
    }
}
