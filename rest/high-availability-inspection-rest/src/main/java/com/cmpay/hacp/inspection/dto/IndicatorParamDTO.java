package com.cmpay.hacp.inspection.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "指标参数DTO")
public class IndicatorParamDTO {
    /**
     * 参数顺序
     */
    @Schema(description = "参数顺序", example = "1", required = true)
    private Integer paramOrder;

    /**
     * 参数名称
     */
    @Schema(description = "参数名称", example = "threshold", required = true)
    private String paramName;

    /**
     * 参数描述
     */
    @Schema(description = "参数描述", example = "CPU使用率阈值，超过此值将触发告警")
    private String description;
}
