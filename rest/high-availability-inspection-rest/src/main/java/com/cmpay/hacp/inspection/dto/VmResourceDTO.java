package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("1")
@Schema(description = "虚拟机配置")
public class VmResourceDTO extends ResourceDTO {

    private Integer resourceId;

    public VmResourceDTO() {
        setResourceType(ResourceType.VIRTUAL_MACHINE);
    }
}
