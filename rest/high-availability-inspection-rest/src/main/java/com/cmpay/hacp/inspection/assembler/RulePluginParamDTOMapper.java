package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;
import com.cmpay.hacp.inspection.dto.RulePluginParamDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface RulePluginParamDTOMapper {

    @Mapping(target = "ruleId", ignore = true)
    RulePluginParam toRulePluginParam(RulePluginParamDTO dto);
}
