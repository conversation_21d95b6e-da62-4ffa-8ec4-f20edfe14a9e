package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReportDetail;
import com.cmpay.hacp.inspection.dto.PerInspectionReportDetailRspDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportQueryReqDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportRspDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 按次巡检报告对象转换器
 */
@Mapper(componentModel = "spring")
public interface PerInspectionReportDTOMapper {

    /**
     * 查询请求DTO转领域对象
     *
     * @param reqDTO 查询请求DTO
     * @return 领域对象
     */
    @Mapping(target = "taskExecutionId", ignore = true)
    @Mapping(target = "executionDuration", ignore = true)
    @Mapping(target = "executionTimeStr", ignore = true)
    @Mapping(target = "resultStats", ignore = true)
    @Mapping(target = "generateTime", ignore = true)
    PerInspectionReport toPerInspectionReport(PerInspectionReportQueryReqDTO reqDTO);

    /**
     * 领域对象列表转响应DTO列表
     *
     * @param reportList 领域对象列表
     * @return 响应DTO列表
     */
    List<PerInspectionReportRspDTO> toPerInspectionReportRspDTOList(List<PerInspectionReport> reportList);

    /**
     * 详细内容领域对象转响应DTO
     *
     * @param detail 详细内容领域对象
     * @return 详细内容响应DTO
     */
    PerInspectionReportDetailRspDTO toPerInspectionReportDetailRspDTO(PerInspectionReportDetail detail);
}
