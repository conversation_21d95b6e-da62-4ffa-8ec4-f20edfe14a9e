package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.enums.PluginStatus;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检插件查询请求DTO
 */
@Data
@Schema(description = "巡检插件查询请求DTO")
public class InspectionPluginQueryReqDTO {

    @Schema(description = "插件名称", example = "CPU使用率检查")
    private String name;

    @Schema(description = "插件描述", example = "CPU使用率检查")
    private String description;

    @Schema(description = "插件类型：1-SHELL脚本, 2-PYTHON脚本, 3-页面检查, 4-自动化测试", example = "1")
    private PluginType type;

    @Schema(description = "插件状态：0-禁用，1-启用", example = "1")
    private PluginStatus status;

    @Schema(description = "标签ID列表")
    private List<Long> tagIds;

    @Schema(description = "开始时间", example = "2025-06-19 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2025-06-19 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    private PageDTO<?> page = new PageDTO<>();
}
