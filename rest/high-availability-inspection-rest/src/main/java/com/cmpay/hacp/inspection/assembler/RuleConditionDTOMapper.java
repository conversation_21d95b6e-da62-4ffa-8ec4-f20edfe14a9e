package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.condition.model.RuleCondition;
import com.cmpay.hacp.inspection.dto.RuleConditionDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface RuleConditionDTOMapper {
    @Mapping(target = "conditionGroupId", ignore = true)
    RuleCondition toRuleCondition(RuleConditionDTO reqDTO);

    RuleConditionDTO toRuleConditionDTO(RuleCondition ruleCondition);
}