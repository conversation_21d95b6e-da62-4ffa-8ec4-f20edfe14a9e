package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.PluginOutputFieldType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 插件脚本输出字段定义DTO
 */
@Data
@Schema(description = "插件脚本输出字段定义DTO")
public class PluginOutputFieldDTO {
    /**
     * 字段名称(如cpu.usage)
     */
    @Schema(description = "字段名称", required = true, example = "disk.usage")
    @NotBlank(message = "HAI50505")
    private String fieldName;

    /**
     * 取值路径 JSONPath
     */
    @Schema(description = "取值路径", required = true, example = "disk.usage")
    @NotBlank(message = "HAI50513")
    private String fieldPath;

    /**
     * 示例值
     */
    @Schema(description = "示例值", required = true, example = "85.5")
    @NotBlank(message = "HAI50503")
    private String exampleValue;

    /**
     * 单位(如%)
     */
    @Schema(description = "单位", example = "%")
    private String fieldUnit;

    @Schema(description = "字段类型：1-数值型字段, 2-字符串型字段, 3-布尔型字段", required = true, example = "1")
    @NotNull(message = "HAI50506")
    private PluginOutputFieldType fieldType;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "磁盘使用率")
    private String description;
}
