package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.task.CronScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.FixedTimeScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.IntervalScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.ScheduleConfig;
import com.cmpay.hacp.inspection.dto.CronScheduleConfigDTO;
import com.cmpay.hacp.inspection.dto.FixedTimeScheduleConfigDTO;
import com.cmpay.hacp.inspection.dto.IntervalScheduleConfigDTO;
import com.cmpay.hacp.inspection.dto.ScheduleConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ObjectFactory;
import org.mapstruct.SubclassMapping;

@Mapper(componentModel = "spring")
public interface ScheduleConfigDTOMapper {
    @Mapping(target = "taskId", ignore = true)
    @SubclassMapping(source = CronScheduleConfigDTO.class, target = CronScheduleConfig.class)
    @SubclassMapping(source = IntervalScheduleConfigDTO.class, target = IntervalScheduleConfig.class)
    @SubclassMapping(source = FixedTimeScheduleConfigDTO.class, target = FixedTimeScheduleConfig.class)
    ScheduleConfig toScheduleConfig(ScheduleConfigDTO dto);

    @ObjectFactory
    default ScheduleConfig createScheduleConfig(ScheduleConfigDTO dto) {
        throw new IllegalArgumentException("Unknown DTO type: " + dto.getClass().getName());
    }

    @SubclassMapping(source = CronScheduleConfig.class, target = CronScheduleConfigDTO.class)
    @SubclassMapping(source = IntervalScheduleConfig.class, target = IntervalScheduleConfigDTO.class)
    @SubclassMapping(source = FixedTimeScheduleConfig.class, target = FixedTimeScheduleConfigDTO.class)
    ScheduleConfigDTO toScheduleConfigDTO(ScheduleConfig config);

    @ObjectFactory
    default ScheduleConfigDTO createScheduleConfigDTO(ScheduleConfig scheduleConfig) {
        throw new IllegalArgumentException("Unknown ScheduleConfig type: " + scheduleConfig.getClass().getName());
    }
}
