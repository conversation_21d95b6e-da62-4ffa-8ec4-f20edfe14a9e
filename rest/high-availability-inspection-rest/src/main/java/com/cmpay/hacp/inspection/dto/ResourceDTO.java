package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.ResourceExecutionStrategyEnum;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.DiscriminatorMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(
        description = "调度配置基类DTO",
        discriminatorProperty = "type",
        discriminatorMapping = {
                @DiscriminatorMapping(value = "1", schema = VmResourceDTO.class),
                @DiscriminatorMapping(value = "2", schema = ContainerResourceDTO.class)
        }
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "resourceType")
@JsonSubTypes({
        @JsonSubTypes.Type(value = VmResourceDTO.class, name = "1"),
        @JsonSubTypes.Type(value = ContainerResourceDTO.class, name = "2")
})
public class ResourceDTO {
    @NotNull(message = "HAI50512")
    private ResourceType resourceType;

    private ResourceExecutionStrategyEnum strategy;

    private String resourceName;
}
