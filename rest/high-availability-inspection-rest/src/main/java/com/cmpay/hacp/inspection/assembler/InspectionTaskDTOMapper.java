package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.model.task.TaskRuleExecution;
import com.cmpay.hacp.inspection.dto.InspectionTaskQueryReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionTaskReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionTaskRspDTO;
import com.cmpay.hacp.inspection.dto.TaskRuleExecutionDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 巡检任务对象转换器
 */
@Mapper(componentModel = "spring", uses = {ScheduleConfigDTOMapper.class,AlarmNotificationDTOMapper.class,ResourceDTOMapper.class})
public interface InspectionTaskDTOMapper {

    /**
     * 请求DTO转领域对象
     *
     * @param reqDTO 请求DTO
     * @return 领域对象
     */
    @Mapping(target = "executionStatus", ignore = true)
    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    @Mapping(target = "auditInfo", ignore = true)
    @Mapping(target = "executionPlan", ignore = true)
    @Mapping(target = "latestExecutionTime", ignore = true)
    InspectionTask toInspectionTask(InspectionTaskReqDTO reqDTO);

    /**
     * 领域对象转响应DTO
     *
     * @param inspectionTask 领域对象
     * @return 响应DTO
     */
    InspectionTaskRspDTO toInspectionTaskRspDTO(InspectionTask inspectionTask);

    /**
     * 规则执行DTO转领域对象
     *
     * @param dto DTO
     * @return 领域对象
     */
    TaskRuleExecution toRuleExecution(TaskRuleExecutionDTO dto);

    /**
     * 规则执行领域对象转DTO
     *
     * @param taskRuleExecution 领域对象
     * @return DTO
     */
    TaskRuleExecutionDTO toRuleExecutionDTO(TaskRuleExecution taskRuleExecution);

    /**
     * 规则执行DTO列表转领域对象列表
     *
     * @param dtoList DTO列表
     * @return 领域对象列表
     */
    List<TaskRuleExecution> toRuleExecutionList(List<TaskRuleExecutionDTO> dtoList);

    /**
     * 规则执行领域对象列表转DTO列表
     *
     * @param taskRuleExecutionList 领域对象列表
     * @return DTO列表
     */
    List<TaskRuleExecutionDTO> toRuleExecutionDTOList(List<TaskRuleExecution> taskRuleExecutionList);

    List<InspectionTaskRspDTO> toInspectionTaskRspDTOList(List<InspectionTask> records);

    @Mapping(target = "description", ignore = true)
    @Mapping(target = "alarmNotification", ignore = true)
    @Mapping(target = "taskRuleExecutions", ignore = true)
    @Mapping(target = "scheduleConfig", ignore = true)
    @Mapping(target = "executionPlan", ignore = true)
    @Mapping(target = "latestExecutionTime", ignore = true)
    @Mapping(target = "auditInfo", ignore = true)
    @Mapping(target = "taskId", ignore = true)
    InspectionTask toInspectionTask(InspectionTaskQueryReqDTO reqDTO);


}
