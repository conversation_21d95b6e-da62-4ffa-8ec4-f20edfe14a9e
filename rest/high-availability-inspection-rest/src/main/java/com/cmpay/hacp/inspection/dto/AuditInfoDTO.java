package com.cmpay.hacp.inspection.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 审计信息DTO
 */
@Data
@Schema(description = "审计信息DTO")
public class AuditInfoDTO {

    @Schema(description = "创建人", example = "admin")
    private String createdBy;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @Schema(description = "更新人", example = "admin")
    private String updatedBy;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
}
