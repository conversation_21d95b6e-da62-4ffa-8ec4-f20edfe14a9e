package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.fasterxml.jackson.annotation.JsonTypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("1")
public class PluginConfigShellScriptDTO extends PluginConfigScriptDTO{

    public PluginConfigShellScriptDTO() {
        this.setType(PluginType.SHELL_SCRIPT);
    }

}
