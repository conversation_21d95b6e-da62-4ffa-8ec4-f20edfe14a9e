package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.condition.model.RuleConditionGroupInfo;
import com.cmpay.hacp.inspection.dto.RuleConditionGroupInfoDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface RuleConditionGroupInfoDTOMapper {

    RuleConditionGroupInfo toRuleConditionInfo(RuleConditionGroupInfoDTO reqDTO);

    RuleConditionGroupInfoDTO toRuleConditionInfoDTO(RuleConditionGroupInfo ruleConditionGroupInfo);
}