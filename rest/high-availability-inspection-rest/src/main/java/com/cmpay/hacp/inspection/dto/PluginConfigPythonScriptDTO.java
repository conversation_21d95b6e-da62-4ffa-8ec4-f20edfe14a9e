package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.fasterxml.jackson.annotation.JsonTypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("2")
public class PluginConfigPythonScriptDTO extends PluginConfigScriptDTO{

    public PluginConfigPythonScriptDTO() {
        this.setType(PluginType.PYTHON_SCRIPT);
    }

}
