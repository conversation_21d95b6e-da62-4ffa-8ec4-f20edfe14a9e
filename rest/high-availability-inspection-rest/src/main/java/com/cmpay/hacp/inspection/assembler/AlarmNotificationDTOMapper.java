package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.task.AlarmNotification;
import com.cmpay.hacp.inspection.dto.AlarmNotificationDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface AlarmNotificationDTOMapper {
    AlarmNotification toAlarmNotification(AlarmNotificationDTO dto);

    AlarmNotificationDTO toAlarmNotificationDTO(AlarmNotification alarmNotification);
}
