package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.IntervalUnit;
import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ScheduleConfigDTO Jackson序列化与反序列化的单元测试
 */
class ScheduleConfigDTOSerializationTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        // 注册Java 8日期时间模块以支持LocalDate和LocalTime
        objectMapper.findAndRegisterModules();
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }

    @Test
    @DisplayName("测试CRON类型调度配置的序列化与反序列化")
    void testCronScheduleConfigSerialization() throws Exception {
        // 创建测试对象
        CronScheduleConfigDTO cronConfig = new CronScheduleConfigDTO();
        cronConfig.setEnabled(true);
        cronConfig.setType(ScheduleType.CRON_EXPRESSION);
        cronConfig.setCronExpression("0 0 12 * * ?");

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(cronConfig);

        System.out.println(json);

        // 验证JSON包含预期的字段
        assertTrue(json.contains("\"type\":1"));
        assertTrue(json.contains("\"enabled\":true"));
        assertTrue(json.contains("\"cronExpression\":\"0 0 12 * * ?\""));

        // 反序列化回对象
        ScheduleConfigDTO deserializedConfig = objectMapper.readValue(json, ScheduleConfigDTO.class);

        // 验证反序列化结果
        assertInstanceOf(CronScheduleConfigDTO.class, deserializedConfig);
        CronScheduleConfigDTO deserializedCronConfig = (CronScheduleConfigDTO) deserializedConfig;
        assertTrue(deserializedCronConfig.isEnabled());
        assertEquals(ScheduleType.CRON_EXPRESSION, deserializedCronConfig.getType());
        assertEquals("0 0 12 * * ?", deserializedCronConfig.getCronExpression());
    }

    @Test
    @DisplayName("测试INTERVAL类型调度配置的序列化与反序列化")
    void testIntervalScheduleConfigSerialization() throws Exception {
        // 创建测试对象
        IntervalScheduleConfigDTO intervalConfig = new IntervalScheduleConfigDTO();
        intervalConfig.setEnabled(true);
        intervalConfig.setType(ScheduleType.FIXED_INTERVAL);
        intervalConfig.setIntervalValue(30);
        intervalConfig.setIntervalUnit(IntervalUnit.MINUTE);

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(intervalConfig);

        // 验证JSON包含预期的字段
        assertTrue(json.contains("\"type\":2"));
        assertTrue(json.contains("\"enabled\":true"));
        assertTrue(json.contains("\"intervalValue\":30"));
        assertTrue(json.contains("\"intervalUnit\":1"));

        // 反序列化回对象
        ScheduleConfigDTO deserializedConfig = objectMapper.readValue(json, ScheduleConfigDTO.class);

        // 验证反序列化结果
        assertInstanceOf(IntervalScheduleConfigDTO.class, deserializedConfig);
        IntervalScheduleConfigDTO deserializedIntervalConfig = (IntervalScheduleConfigDTO) deserializedConfig;
        assertTrue(deserializedIntervalConfig.isEnabled());
        assertEquals(ScheduleType.FIXED_INTERVAL, deserializedIntervalConfig.getType());
        assertEquals(30, deserializedIntervalConfig.getIntervalValue());
        assertEquals(IntervalUnit.MINUTE, deserializedIntervalConfig.getIntervalUnit());
    }

    @Test
    @DisplayName("测试FIXED_TIME类型调度配置的序列化与反序列化")
    void testFixedTimeScheduleConfigSerialization() throws Exception {
        // 创建测试对象
        FixedTimeScheduleConfigDTO fixedTimeConfig = new FixedTimeScheduleConfigDTO();
        fixedTimeConfig.setEnabled(true);
        fixedTimeConfig.setType(ScheduleType.FIXED_TIME);
        LocalDateTime executionDate = LocalDateTime.of(2024, 10, 15,14, 30,0);
        fixedTimeConfig.setExecutionDateTime(executionDate);

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(fixedTimeConfig);

        // 验证JSON包含预期的字段
        assertTrue(json.contains("\"type\":3"));
        assertTrue(json.contains("\"enabled\":true"));
        assertTrue(json.contains("\"executionDateTime\":\"2024-10-15 14:30:00\""));

        // 反序列化回对象
        ScheduleConfigDTO deserializedConfig = objectMapper.readValue(json, ScheduleConfigDTO.class);

        // 验证反序列化结果
        assertInstanceOf(FixedTimeScheduleConfigDTO.class, deserializedConfig);
        FixedTimeScheduleConfigDTO deserializedFixedTimeConfig = (FixedTimeScheduleConfigDTO) deserializedConfig;
        assertTrue(deserializedFixedTimeConfig.isEnabled());
        assertEquals(ScheduleType.FIXED_TIME, deserializedFixedTimeConfig.getType());
        assertEquals(executionDate, deserializedFixedTimeConfig.getExecutionDateTime());
    }

    @Test
    @DisplayName("测试多态反序列化 - 根据type字段选择正确的子类")
    void testPolymorphicDeserialization() throws Exception {
        // 准备三种不同类型的JSON
        String cronJson = "{\"type\":1,\"enabled\":true,\"cronExpression\":\"0 0 12 * * ?\"}";
        String intervalJson = "{\"type\":2,\"enabled\":true,\"intervalValue\":30,\"intervalUnit\":1}";
        String fixedTimeJson = "{\"type\":3,\"enabled\":true,\"executionDateTime\":\"2024-10-15 14:30:00\"}";

        // 反序列化并验证类型
        ScheduleConfigDTO cronConfig = objectMapper.readValue(cronJson, ScheduleConfigDTO.class);
        ScheduleConfigDTO intervalConfig = objectMapper.readValue(intervalJson, ScheduleConfigDTO.class);
        ScheduleConfigDTO fixedTimeConfig = objectMapper.readValue(fixedTimeJson, ScheduleConfigDTO.class);

        assertInstanceOf(CronScheduleConfigDTO.class, cronConfig);
        assertInstanceOf(IntervalScheduleConfigDTO.class, intervalConfig);
        assertInstanceOf(FixedTimeScheduleConfigDTO.class, fixedTimeConfig);

        // 验证字段值
        assertEquals("0 0 12 * * ?", ((CronScheduleConfigDTO) cronConfig).getCronExpression());
        assertEquals(30, ((IntervalScheduleConfigDTO) intervalConfig).getIntervalValue());
        assertEquals(LocalDateTime.of(2024, 10, 15,14, 30,0), ((FixedTimeScheduleConfigDTO) fixedTimeConfig).getExecutionDateTime());
    }

    @Test
    @DisplayName("测试缺少type字段时的反序列化异常")
    void testDeserializationWithoutTypeField() {
        // 准备缺少type字段的JSON
        String invalidJson = "{\"enabled\":true,\"cronExpression\":\"0 0 12 * * ?\"}";

        // 验证反序列化时抛出异常
        Exception exception = assertThrows(Exception.class, () -> {
            objectMapper.readValue(invalidJson, ScheduleConfigDTO.class);
        });

        // 验证异常消息包含关于缺少type字段的信息
        String exceptionMessage = exception.getMessage();
        assertTrue(exceptionMessage.contains("type") ||
                   exceptionMessage.contains("missing") ||
                   exceptionMessage.contains("property"));
    }

    @Test
    @DisplayName("测试无效type值的反序列化异常")
    void testDeserializationWithInvalidTypeValue() {
        // 准备包含无效type值的JSON
        String invalidJson = "{\"type\":\"INVALID_TYPE\",\"enabled\":true}";

        // 验证反序列化时抛出异常
        Exception exception = assertThrows(Exception.class, () -> {
            objectMapper.readValue(invalidJson, ScheduleConfigDTO.class);
        });

        // 验证异常消息包含关于无效type值的信息
        String exceptionMessage = exception.getMessage();
        assertTrue(exceptionMessage.contains("INVALID_TYPE") ||
                   exceptionMessage.contains("subtype") ||
                   exceptionMessage.contains("type"));
    }
}
