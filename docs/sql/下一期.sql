ALTER TABLE `inspection_plugin_script`
	MODIFY COLUMN `script_result_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '输出类型: 1=结构化数据, 2=文本数据' AFTER `script_content`;
ALTER TABLE `inspection_plugin` ADD COLUMN `source` tinyint(1) NOT NULL COMMENT '支持环境' AFTER `type`;
ALTER TABLE `inspection_plugin` ADD COLUMN `deploy_envs` varchar(64) NOT NULL COMMENT '支持环境' AFTER `source`;

DROP TABLE IF EXISTS firefly_zone_config;
DROP TABLE IF EXISTS firefly_indicator_param;
DROP TABLE IF EXISTS firefly_indicator_definition;
RENAME TABLE `inspection_plugin_script_output_filed` TO `inspection_plugin_output_filed`;
ALTER TABLE `inspection_plugin_output_filed`
	ADD COLUMN `field_path` varchar(254) NOT NULL COMMENT '取值路径' AFTER `field_name`;