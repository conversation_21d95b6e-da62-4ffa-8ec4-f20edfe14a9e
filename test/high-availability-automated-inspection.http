# 测试顺序，high-availability-control-platform.http文件中的：验证码-> 加密密码-> 登录
# 获取插件标签
GET {{ inspection-url}}/v1/inspection/tag/pluginTags
Token: {{token}}
workspaceId: {{workspaceId}}

### 加密数据(非登录接口，容器等接口账号信息加密)->dev生效，等同于/v1/sys/cipher/keys+前端加密
GET {{ url }}/v1/sys/encrypt-password?username={{captchaReqId}}&password=1234&captchaReqId={{captchaReqId}}

> {%
    client.global.set("not_longin_password",response.body.body)
%}

### 插件新增,存在加密数据时先请求加密数据
POST {{inspection-url}}/v1/inspection/plugin/create
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "name": "test插件1111",
  "type": 2,
  "status": 0,
  "description": "测试插件",
  "scriptResultType": 2,
  "tagIds": [

  ],
  "pluginConfig": {
  "scriptContent": "xx",
    "type": 2,
    "results": [
      {
        "fieldName": "test",
        "exampleValue": "13",
        "fieldUnitL": "%",
        "fieldType": 1 ,
        "description": "描述"
      }
    ],
    "parameters": [
      {
        "paramName": "test(未加密)",
        "paramType": 1,
        "regexPattern": "^[0-9a-zA-Z.]+$",
        "paramValue": "123",
        "paramDesc": "未加密描述",
        "isEncrypted": false
      },
      {
        "paramName": "test(加密)",
        "paramType": 1,
        "regexPattern": "^[0-9a-zA-Z.]+$",
        "paramValue": "ss",
        "paramDesc": "加密描述",
        "isEncrypted": false
      }
    ]
  },
  "cacheKey": "{{captchaReqId}}"
}

> {%
    client.global.set("id",JSON.parse(response.body).body)
 %}

### 插件列表
#   "name": "test",
# "type": 3,
#  "description": "测试插件",
# "status": 1,
#  "startTime": "2025-06-24 10:45:11",
#  "endTime": "2025-06-24 10:45:11",
#  "tagIds": [25],
POST {{inspection-url}}/v1/inspection/plugin/page
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "startTime": "2025-06-24 10:45:11",
  "page": {
    "pageNum": 1,
    "pageSize": 10
  }
}

### 获取插件列表
POST {{inspection-url}}/v1/inspection/plugin/list
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "type": 1
}

### 获取插件详情
GET {{inspection-url}}/v1/inspection/plugin/detail/{{pluginId}}
Token: {{token}}
workspaceId: {{workspaceId}}


### 插件更新,先请求加密数据
POST {{inspection-url}}/v1/inspection/plugin/update
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "pluginId": "{{id}}",
  "name": "test插件3",
  "type": 1,
  "status": 0,
  "description": "测试插件",
  "scriptContent": "#!/bin/bashecho \"CPU使用率检查\"",
  "scriptResultType": 2,
  "tagIds": [
    25
  ],
  "results": [
    {
      "fieldName": "test",
      "exampleValue": "13",
      "fieldUnitL": "%",
      "fieldType": 1 ,
      "description": "描述"
    }
  ],
  "parameters": [
    {
      "paramName": "test(未加密)",
      "paramType": 1,
      "regexPattern": "^[0-9a-zA-Z.]+$",
      "paramValue": "1234",
      "paramDesc": "未加密描述",
      "isEncrypted": false
    },
    {
      "paramName": "test(加密)",
      "paramType": 1,
      "regexPattern": "^[0-9a-zA-Z.]+$",
      "paramValue": "{{not_longin_password}}",
      "paramDesc": "加密描述",
      "isEncrypted": true
    }
  ],
  "key": "{{captchaReqId}}"
}


### 插件删除
DELETE {{inspection-url}}/v1/inspection/plugin/delete/{{id}}
Token: {{token}}
workspaceId: {{workspaceId}}

### 新增巡检规则
POST {{inspection-url}}/v1/inspection/rule/create
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "name": "test规则",
  "description": "测试规则描述",
  "tagIds": [],
  "status": 1,
  "type": 1,
  "pluginId": "{{id}}",
  "deployEnv": 1,
  "pluginParams": [
      {
      "pluginId": "{{id}}",
      "pluginParamName": "threshold",
      "pluginParamValue": "90",
      "isEncrypted": false
    }
  ],
  "pluginResult": {
    "pluginId": "{{id}}",
    "pluginOutputFiledName": "disk.usage",
    "comparisonOperator": 1,
    "comparisonValue": "80",
    "duration": "300",
    "checkInterval": "60",
    "checkPeak": 0,
    "specificProcesses": "java",
    "rulePreview": "当 disk.usage 大于 80时，持续300秒，则报警",
    "suggest": "治理建议"
  }
}


### 更新巡检规则
POST {{inspection-url}}/v1/inspection/rule/update
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "ruleId": "{{ruleId}}",
  "name": "test规则",
  "description": "测试规则描述",
  "tagIds": [],
  "level": 1,
  "status": 1,
  "type": 1,
  "pluginId": "{{id}}",
  "deployEnv": 1,
  "pluginParams": [
      {
      "pluginId": "{{id}}",
      "pluginParamName": "threshold",
      "pluginParamValue": "90",
      "isEncrypted": false
    }
  ],
  "pluginResult": {
    "pluginId": "{{id}}",
    "pluginOutputFiledName": "disk.usage",
    "comparisonOperator": 1,
    "comparisonValue": "80",
    "duration": "300",
    "checkInterval": "60",
    "checkPeak": 0,
    "specificProcesses": "java",
    "rulePreview": "当 disk.usage 大于 80时，持续300秒，则报警",
    "suggest": "治理建议"
  }
}

### 获取规则列表
POST {{inspection-url}}/v1/inspection/rule/page
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "page": {
    "pageNum": 1,
    "pageSize": 10
  }
}

### 获取规则详情
GET {{inspection-url}}/v1/inspection/rule/detail/{{ruleId}}
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

### 获取插件详情
GET {{inspection-url}}/v1/inspection/plugin/detail/params/{{pluginId}}
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

### 创建任务
POST {{inspection-url}}/v1/inspection/task/create
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "name": "test task",
  "description": "task desc",
  "status": 0,
  "taskRuleExecutions": [
    {
      "ruleId": "{{ruleId}}",
      "resourceList": [
        {
          "resourceType": "1",
          "resourceId": "20",
          "resourceName": "vm"
        },
        {
          "resourceType": 2,
          "cluster": "testzapp",
          "namespace": "hacp",
          "kind": "service",
          "resourceName": "hacp"
        }
      ]
    }
  ],
  "scheduleConfig": {
    "type": 1,
    "enable": true,
    "cronExpression": "*/5 * * ? * * "
  },
  "alarmNotification": {
    "alertConditions": [
      1,
      2
    ],
    "email": "<EMAIL>"
  }
}


### 任务分页列表
POST {{inspection-url}}/v1/inspection/task/page
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "page": {
    "pageNum": 1,
    "pageSize": 10
  }
}

### 查询任务详情
GET {{inspection-url}}/v1/inspection/task/TASK-000008
Token: {{token}}
workspaceId: {{workspaceId}}


###
POST {{inspection-url}}/v1/inspection/rule/list
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
}