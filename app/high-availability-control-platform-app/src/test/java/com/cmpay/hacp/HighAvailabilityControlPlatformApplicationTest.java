package com.cmpay.hacp;

import com.cmpay.hacp.emergency.utils.ShellScriptExec;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 本地测试启动参数如下：
 * VM:   -Dlemon.logger.level=INFO
 * args: --spring.profiles.active=dev
 * <AUTHOR>
 */
@SpringBootTest
class HighAvailabilityControlPlatformApplicationTest {
    static String str="SM4EncryptorUtil.encryptEcb(sm4RandomSalt, encrypt)";
    static String str2="c74ff85723086efb96c05bb0dde4e928";

        public static void main(String[] args) throws Exception {
            // SM4EncryptorUtil.encryptCbc("c74ff85723086efb96c05bb0dde4e928","MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9y80x5tU2uARYRLeA5JqeCs38RXjjaUjCV4jjnYQ8hwMwI+zPtCXU4hkq+6QR9zytweR9es2F56su1WttUIsDENEvpazMJwjx43/xwaajzgReymXyTTv9siI2mg7bPMn02i1RI/Yw4fysosTCG/M9wYaOq3Fxmsw2IL1mkFhArQIDAQAB");
            // String base64PrivateKey = SM4EncryptorUtil.decryptCompatible("c74ff85723086efb96c05bb0dde4e928", "3e5ac65ffff88fb2f223c7805f5c2a41cdd2b95beffed949126073d01ec90cc32d9b9159623190befa30774143351c0dca770f1fae83534ea2790b025e4ed14cdc78c230cb237be833f6916dafcd782e");
            // String passWord = PasswordUtil.createPassWord("系统管理员", str2);
            // System.out.println(passWord);

            ShellScriptExec shellScriptExec;
                shellScriptExec = new ShellScriptExec("***********:22",
                        "tenant",
                        "C:\\Enterprise\\Cmpay\\hpaddle\\hacp\\app\\high-availability-control-platform-app\\src\\test\\java\\com\\cmpay\\hacp\\id_rsa(1)",
                        ShellScriptExec.ConnectMethod.USERNAME_NOT_PASSWORD);
            shellScriptExec.execCommand("ps", null);

        }

}
