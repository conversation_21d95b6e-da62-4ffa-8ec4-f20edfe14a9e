package com.cmpay.hacp.system.log.firefly.logger;

import com.cmpay.hacp.system.log.firefly.entity.FireflyLog;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;

/**
 * <AUTHOR>
 * 动态日志输出
 */
public class FireflyDynamicAccessLogger extends FireflyLogger {

    public FireflyDynamicAccessLogger(ObjectMapper objectMapper, Logger logger) {
        super(objectMapper, logger);
    }

    @Override
    public void println(FireflyLog fireflyLog) {
        try {
            this.logger.info(this.objectMapper.writeValueAsString(fireflyLog));
        } catch (Exception e) {
            this.logger.info("Failed to formatting log {}", fireflyLog.toString());
        }
    }
}
