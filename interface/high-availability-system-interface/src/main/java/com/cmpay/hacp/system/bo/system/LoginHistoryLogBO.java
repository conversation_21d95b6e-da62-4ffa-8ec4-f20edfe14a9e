
package com.cmpay.hacp.system.bo.system;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class LoginHistoryLogBO {
    /**
     * @Fields id 主键ID
     */
    private String id;
    /**
     * @Fields userId 用户ID
     */
    private String userId;

    /**
     * @Fields userName 用户名
     */
    private String userName;
    /**
     * @Fields name 姓名
     */
    private String name;
    /**
     * @Fields mobile 手机号码
     */
    private String mobile;
    /**
     * @Fields loginIp 登录IP
     */
    private String loginIp;
    /**
     * @Fields loginTerminal 登录终端设备信息 user_agent
     */
    private String loginTerminal;
    /**
     * @Fields loginFrom 登录来源
     */
    private String loginFrom;
    /**
     * @Fields loginDate 登录日期
     */
    private LocalDate loginDate;
    /**
     * @Fields loginTime 登录时间
     */
    private LocalDateTime loginTime;
    /**
     * @Fields requestId 日志流水号
     */
    private String requestId;
    /**
     * @Fields isUse 是否可用(0-否,1-是)
     */
    private Short isUse;


    /**
     * @Fields beginDate 开始时间
     */
    private String beginDate;

    /**
     * @Fields endDate 结束时间
     */
    private String endDate;

}
