package com.cmpay.hacp.system.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;


@Data
@ConfigurationProperties(prefix = "hacp.web.admin")
public class HacpWebAdminProperties {


    private String aesKey = "2729edb6d1260dd18ac37ee66bd94417";

    private Session session = new Session();

    private String cacheType;

    private Rest rest = new Rest();

    private Sensitive sensitive = new Sensitive();

    @Data
    public class Rest {

        private int connectTimeout = 15 * 1000;

        private int readTimeout = 15 * 1000;
    }

    @Data
    public class Session {

        private boolean control = true;

    }

    @Data
    public class Sensitive {
        /**
         * 默认数据aeskey加解密key
         */
        private String aesKey = "123456abc";

        /**
         * 默认数据SM4Key加解密key
         */
        private String SM4Key = "45fac8b21c810d7e679d306a71cf1876";

        /**
         * 提升性能，减少不必要的解析
         * 数据库需要加解密的dao类的包名、类名、方法名 eg: com.lemon.web.admin.dao 配合@CryptField注解使用
         */
        private List<String> basePackages;
    }
}
