package com.cmpay.hacp.system.bo.system;

import com.cmpay.hacp.bo.system.SessionTokenVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @author: tnwning
 * @Date: 2020/02/15
 * 权限认证登陆请求DTO
 */
@Data
public class LoginBO implements Serializable {

    private String userName;

    private String password;

    /**
     * 图片验证码请求ID
     */
    private String captchaReqId;

    /**
     * 图片验证码
     */
    private String captchaCode;

    /**
     * 设备ID
     */
    private String equipmentId;

    /**
     * 请求IP
     */
    private String requestIp;

    /**
     * 请求参数
     */
    private Map<String, Object> paramsMap;

    /**
     * 会话Token
     */
    private SessionTokenVO sessionTokenVO;

    /**
     * 上次登陆时间
     */
    private String lastLoginTime;

    /**
     * 是否需要提醒用户修改密码
     */
    private String pwdNeedToModify;
}
