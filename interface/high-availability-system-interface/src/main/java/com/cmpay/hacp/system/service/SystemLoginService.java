package com.cmpay.hacp.system.service;


import com.cmpay.hacp.system.bo.system.LoginBO;
import com.cmpay.hacp.system.bo.system.UserLoginBO;

/**
 * <AUTHOR>
 */
public interface SystemLoginService {
    /**
     * 登录
     *
     * @param userLoginBO
     * @return
     */
    UserLoginBO login(UserLoginBO userLoginBO);


    /**
     * 转为登录信息
     *
     * @param loginRspDTO
     * @return
     */
    UserLoginBO getUserLoginBO(LoginBO loginRspDTO);


    /**
     * 用户名、密码验证登录
     *
     * @param loginBO
     * @return
     */
    LoginBO passwordLogin(LoginBO loginBO);


}
