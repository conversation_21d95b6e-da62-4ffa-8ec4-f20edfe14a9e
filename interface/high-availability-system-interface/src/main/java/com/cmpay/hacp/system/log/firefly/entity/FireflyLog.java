package com.cmpay.hacp.system.log.firefly.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class FireflyLog {

    /**
     * @Fields requestId 日志编号
     */
    private String requestId;

    /**
     * @Fields applicationName 应用名称/ID
     */
    private String applicationName;

    /**
     * @Fields duration 耗时(毫秒-millisecond）
     */
    private Long duration;

    /**
     * @Fields endTime 结束时间
     */
    private LocalDateTime endTime;

}
