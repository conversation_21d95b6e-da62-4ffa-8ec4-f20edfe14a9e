package com.cmpay.hacp.system.log.aspect;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.system.log.bo.StaticDataSizeBO;
import com.cmpay.hacp.system.log.bo.SysStaticLogBO;
import com.cmpay.hacp.system.log.firefly.entity.FireflyStaticAccessLog;
import com.cmpay.hacp.system.log.firefly.enums.AccessLogTypeEnums;
import com.cmpay.hacp.system.log.firefly.enums.FireflyLogDataTypeEnum;
import com.cmpay.hacp.system.log.firefly.enums.FireflyLogTypeEnum;
import com.cmpay.hacp.system.log.firefly.logger.FireflyLogger;
import com.cmpay.hacp.system.log.service.SystemStaticLogService;
import com.cmpay.hacp.system.properties.HacpWebAdminLogProperties;
import com.cmpay.hacp.utils.network.IpUtil;
import com.cmpay.lemon.common.LemonConstants;
import com.cmpay.lemon.common.context.LemonContext;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.ReflectionUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.cmpay.lemon.framework.security.UserInfoBase;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.lemon.framework.utils.WebUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.http.ResponseEntity;
import org.springframework.util.AntPathMatcher;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.cmpay.hacp.constant.CommonConstant.STATIC_ACCESS_LOG;

/**
 * 日志切面
 */
@Slf4j
@Aspect
public class StaticAccessLogAspect {

    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();

    private final SystemStaticLogService systemStaticLogService;

    private final HacpWebAdminLogProperties.StaticAccess staticAccess;

    private final ObjectMapper resultObjectMapper;

    private final ObjectMapper paramsObjectMapper;

    private final String dataIt;

    private final String applicationName;

    private static final String TITLE = "TITLE";

    public static final String USER_AGENT = "User-Agent";
    /**
     * 不登记日志白名单
     */
    private String[] permitUrls;

    private FireflyLogger fireflyLogger;

    /**
     * 不登记日志的API
     */
    private String[] permitAddLogUrls = {"/upms/v1/captcha/getCaptcha", "/v1/system/captcha/getCaptcha", "/v1/system/menu/nav", "/v1/system/user/info"};


    public StaticAccessLogAspect(SystemStaticLogService systemStaticLogService,
            ObjectMapper resultObjectMapper,
            FireflyLogger fireflyLogger,
            HacpWebAdminLogProperties hacpWebAdminLogProperties,
            String applicationName) {
        this.staticAccess = hacpWebAdminLogProperties.getStaticAccess();
        this.systemStaticLogService = systemStaticLogService;
        this.resultObjectMapper = resultObjectMapper;
        this.paramsObjectMapper = resultObjectMapper;
        this.paramsObjectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        this.dataIt = hacpWebAdminLogProperties.getDataIt();
        this.applicationName = applicationName;
        this.fireflyLogger = fireflyLogger;
        this.permitUrls = Optional.ofNullable(this.staticAccess.getPermitUrl())
                .filter(StringUtils::isNotBlank).map(urls -> urls.split(LemonConstants.COMMA))
                .orElse(new String[0]);
    }

    @Pointcut("execution (* com.cmpay..*Controller.*(..))")
    public void cmpayLog() {

    }

    @Pointcut("@annotation(com.cmpay.hacp.system.log.annotation.LogRecord)")
    public void anyLogRecordMethod() {

    }

    @Pointcut("@annotation(com.cmpay.hacp.system.log.annotation.LogNoneRecord)")
    public void logNoneRecordMethod() {

    }

    @Around("cmpayLog()&&!logNoneRecordMethod()||anyLogRecordMethod()")
    public Object lemonWebAdminLogPoint(ProceedingJoinPoint pjp) throws Throwable {
        Object responseObject = null;
        HttpServletRequest request = WebUtils.getHttpServletRequest();
        SysStaticLogBO logBO = this.beforeProceed(pjp, request);
        try {
            responseObject = pjp.proceed();
            logBO.setMsgCd(this.getMessageCode(responseObject));
        } catch (Throwable throwable) {
            //业务异常
            this.throwableProceed(throwable, logBO);
            throw throwable;
        } finally {
            this.finallyProceed(pjp, request, responseObject, logBO);
        }
        return responseObject;
    }


    /**
     * 不登记日志的API
     *
     * @param request
     * @return
     */
    private boolean isNotRecordLogApi(HttpServletRequest request) {
        for (int i = 0, len = this.permitAddLogUrls.length; i < len; i++) {
            if (PATH_MATCHER.match(this.permitAddLogUrls[i], request.getRequestURI())) {
                return true;
            }
        }
        for (int i = 0, len = this.permitUrls.length; i < len; i++) {
            if (PATH_MATCHER.match(this.permitUrls[i], request.getRequestURI())) {
                return true;
            }
        }
        return false;
    }

    private String getTitle(LogRecord logRecord, String requestUri) {
        return Optional.ofNullable(logRecord).map(LogRecord::title)
                .orElseGet(() -> LemonContext.getCurrentContext().containsKey(TITLE) ? LemonContext.getCurrentContext()
                        .getString(TITLE) : requestUri);
    }

    private String getMethod(LogRecord logRecord, String requestMethod) {
        return Optional.ofNullable(logRecord).map(LogRecord::action)
                .orElse(requestMethod);
    }

    private String getDataIt(LogRecord logRecord) {
        return Optional.ofNullable(logRecord).map(LogRecord::dataIt).filter(StringUtils::isNotBlank).orElse(this.dataIt);
    }

    private SysStaticLogBO beforeProceed(ProceedingJoinPoint pjp, HttpServletRequest request) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        LogRecord logRecord = signature.getMethod().getAnnotation(LogRecord.class);
        LocalDateTime currentLocalDateTime = DateTimeUtils.getCurrentLocalDateTime();
        SysStaticLogBO logBO = new SysStaticLogBO();
        logBO.setRequestUri(request.getRequestURI());
        logBO.setTitle(this.getTitle(logRecord, logBO.getRequestUri()));
        logBO.setRemoteAddr(IpUtil.getIpAddr(request));
        logBO.setUserAgent(request.getHeader(USER_AGENT));
        logBO.setApplicationName(this.applicationName);
        logBO.setDataIt(this.getDataIt(logRecord));
        logBO.setMethod(this.getMethod(logRecord, request.getMethod()));
        logBO.setCreateDate(currentLocalDateTime);
        logBO.setType(AccessLogTypeEnums.STATIC_ACCESS.getCode());
        logBO.setLogId(LemonUtils.getRequestId());
        logBO.setMsgCd(MsgEnum.SUCCESS.getMsgCd());
        if (JudgeUtils.isNotNull(SecurityUtils.getLoginUser())) {
            UserInfoBase loginUser = SecurityUtils.getLoginUser();
            logBO.setCreateBy(loginUser.getUserId());
            logBO.setUserId(loginUser.getUserId());
            logBO.setUserName(loginUser.getLoginName());
            logBO.setMobile(loginUser.getMblNo());
        }
        LemonContext.getCurrentContext().put(STATIC_ACCESS_LOG, logBO);
        return logBO;
    }

    private String getParameters(ProceedingJoinPoint pjp) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        String[] parameterNames = signature.getParameterNames();
        Object[] args = pjp.getArgs();
        Map<String, Object> params = new HashMap<>();
        for (int i = 0; i < parameterNames.length; i++) {
            params.put(parameterNames[i], args[i]);
        }
        String parameters = null;
        try {
            parameters = paramsObjectMapper.writeValueAsString(params);
        } catch (Exception exception) {
            log.warn("getParameters Exception {}", exception.getMessage());
        }
        return parameters;
    }

    private String getMessageCode(Object responseObject) {
        String messageCode = MsgEnum.SUCCESS.getMsgCd();
        //void返回处理
        if (JudgeUtils.isNull(responseObject)) {
            return messageCode;
        }
        //文件下载处理
        if (responseObject instanceof ResponseEntity) {
            ResponseEntity responseEntity = (ResponseEntity) responseObject;
            messageCode = responseEntity.getStatusCodeValue() == 200 ? MsgEnum.SUCCESS.getMsgCd() : MsgEnum.FAIL.getMsgCd();
        } else {
            Class<?> clazz = responseObject.getClass();
            try {
                Field declaredField = clazz.getDeclaredField(this.staticAccess.getMessageCode());
                ReflectionUtils.makeAccessible(declaredField);
                messageCode = (String) declaredField.get(responseObject);
            } catch (Exception exception) {
                log.warn("getMessageCode Exception {}", exception.getMessage());
            }
        }

        return messageCode;
    }

    private void finallyProceed(ProceedingJoinPoint pjp, HttpServletRequest request, Object responseObject, SysStaticLogBO logBO) {
        if (isNotRecordLogApi(request)) {
            return;
        }
        if (this.staticAccess.isRecordNeedLogin()
                && JudgeUtils.isBlank(logBO.getCreateBy())) {
            return;
        }
        logBO.setParams(this.getParameters(pjp));
        logBO.setEndTime(DateTimeUtils.getCurrentLocalDateTime());
        logBO.setDuration(ChronoUnit.MILLIS.between(logBO.getCreateDate(), logBO.getEndTime()));
        this.getStaticRspDataSize(responseObject, logBO);
        fireflyLogger.println(convertFireflyStaticAccessLog(logBO));
        systemStaticLogService.add(logBO);
        LemonContext.getCurrentContext().remove(TITLE);
        LemonContext.getCurrentContext().remove(STATIC_ACCESS_LOG);
    }

    private void getStaticRspDataSize(Object responseObject, SysStaticLogBO sysStaticLogBO) {
        try {
            StaticDataSizeBO staticRspDataSize = systemStaticLogService.getStaticRspDataSize();
            if (JudgeUtils.isNotNull(staticRspDataSize)) {
                sysStaticLogBO.setRspDataSizeType(staticRspDataSize.getRspDataSizeType());
                sysStaticLogBO.setRspDataSize(staticRspDataSize.getRspDataSize());
            } else {
                sysStaticLogBO.setRspDataSizeType(FireflyLogDataTypeEnum.bytes.name());
                sysStaticLogBO.setRspDataSize((long) resultObjectMapper.writeValueAsString(responseObject)
                        .getBytes(StandardCharsets.UTF_8).length);
            }
        } catch (Exception exception) {
            log.warn("getStaticRspDataSize Exception {}", exception.getMessage());
        }
    }

    private FireflyStaticAccessLog convertFireflyStaticAccessLog(SysStaticLogBO logBO) {
        FireflyStaticAccessLog fireflyStaticAccessLog = new FireflyStaticAccessLog();
        fireflyStaticAccessLog.setLogType(FireflyLogTypeEnum.staticLog.name());
        fireflyStaticAccessLog.setOperator(logBO.getUserName());
        fireflyStaticAccessLog.setOperationAction(logBO.getTitle());
        fireflyStaticAccessLog.setExecutionTarget(logBO.getRequestUri());
        fireflyStaticAccessLog.setOperatorIp(logBO.getRemoteAddr());
        fireflyStaticAccessLog.setOperatorTime(logBO.getCreateDate());
        fireflyStaticAccessLog.setOperatorStatus(logBO.getMsgCd());
        fireflyStaticAccessLog.setDataIt(logBO.getDataIt());
        fireflyStaticAccessLog.setApplicationName(logBO.getApplicationName());
        fireflyStaticAccessLog.setDataSize(logBO.getRspDataSize());
        fireflyStaticAccessLog.setDataSizeType(logBO.getRspDataSizeType());
        fireflyStaticAccessLog.setRequestId(logBO.getLogId());
        fireflyStaticAccessLog.setDuration(logBO.getDuration());
        fireflyStaticAccessLog.setEndTime(logBO.getEndTime());
        return fireflyStaticAccessLog;
    }

    private void throwableProceed(Throwable throwable, SysStaticLogBO logBO) {
        if (throwable instanceof BusinessException) {
            BusinessException businessException = (BusinessException) throwable;
            logBO.setType(AccessLogTypeEnums.BUSINESS_EXCEPTION.getCode());
            logBO.setMsgCd(businessException.getMsgCd());
        } else {
            //系统异常日志
            logBO.setType(AccessLogTypeEnums.TYPE_EXCEPTION.getCode());
            logBO.setMsgCd(MsgEnum.SYSTEM_EXCEPTION.getMsgCd());
            logBO.setException(throwable.getMessage());
        }
    }
}
