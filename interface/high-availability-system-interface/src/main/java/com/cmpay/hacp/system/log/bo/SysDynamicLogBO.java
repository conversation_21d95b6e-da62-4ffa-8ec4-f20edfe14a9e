/*
 * @ClassName SysDynamicLogDO
 * @Description
 * @version 1.0
 * @Date 2024-03-25 09:35:14
 */
package com.cmpay.hacp.system.log.bo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SysDynamicLogBO {
    /**
     * @Fields id ID
     */
    private String id;
    /**
     * @Fields logId 日志号
     */
    private String logId;
    /**
     * @Fields operator 操作者
     */
    private String operator;
    /**
     * @Fields operationAction 操作动作
     */
    private String operationAction;
    /**
     * @Fields executionTarget 执行对象
     */
    private String executionTarget;
    /**
     * @Fields operatorIp 操作者IP地址
     */
    private String operatorIp;
    /**
     * @Fields operatorTime 执行时间
     */
    private LocalDateTime operatorTime;
    /**
     * @Fields operatorStatus 操作状态
     */
    private String operatorStatus;
    /**
     * @Fields dataSize 数据量级
     */
    private Long dataSize;
    /**
     * @Fields dataSizeType 数据量级单位(line-行数、byte-大小)
     */
    private String dataSizeType;
    /**
     * @Fields applicationName 应用名称/ID
     */
    private String applicationName;
    /**
     * @Fields dataIt 数据所属对象
     */
    private String dataIt;
    /**
     * @Fields dataPath 流转路径
     */
    private String dataPath;
    /**
     * @Fields interfaceRecord 接口记录
     */
    private String interfaceRecord;
    /**
     * @Fields endTime 结束时间
     */
    private LocalDateTime endTime;
    /**
     * @Fields duration 耗时(毫秒)
     */
    private Long duration;
    /**
     * @Fields type 日志类型
     */
    private String type;
    /**
     * @Fields params 请求参数
     */
    private String params;
    /**
     * @Fields exception 异常信息
     */
    private String exception;
}
