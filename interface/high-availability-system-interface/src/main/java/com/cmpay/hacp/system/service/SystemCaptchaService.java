package com.cmpay.hacp.system.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
public interface SystemCaptchaService {
    /**
     * 校验图片验证码
     *
     * @param captchaReqId 图形验证码请求随机ID
     * @param captchaCode  用户输入的图形验证码
     * @return boolean
     */
    boolean checkCaptcha(String captchaReqId, String captchaCode);

    /**
     * 生成图片验证码
     *
     * @param request
     * @return
     */
    void captcha(HttpServletRequest request, HttpServletResponse resp, String captchaReqId);
}
