package com.cmpay.hacp.system.client;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemCipherApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.dto.system.CipherDataInfoReqDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform}", url = "${hacp.management.discovery.url:}", contextId = "systemCipherClient")
public interface SystemCipherClient {

    /**
     * 解密数据，不包括加密，由各服务自己再次加密存储
     * @param data
     * @param token
     * @return
     */
    @PostMapping(VersionApi.VERSION_V1 + SystemCipherApi.DECRYPT_DATA)
    GenericRspDTO<String> decryptDataBySm4AndSm2(@RequestBody CipherDataInfoReqDTO data,@RequestHeader(CommonConstant.TOKEN)String token);

    /**
     * 获取sm4密钥
     * @param key
     * @param token
     * @return
     */
    @GetMapping(VersionApi.VERSION_V1 + SystemCipherApi.SM4_KEY_DATA)
    GenericRspDTO<String> getSm4RandomSalt(@RequestParam("key") String key,@RequestHeader(CommonConstant.TOKEN)String token);
}
