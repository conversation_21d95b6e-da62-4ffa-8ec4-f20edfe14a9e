package com.cmpay.hacp.system.client;

import com.cmpay.lemon.framework.data.DefaultRspDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform}", url = "${hacp.management.discovery.url:}", contextId = "systemCaptchaServiceClient")
public interface SystemCaptchaServiceClient {
    @GetMapping("/v1/sys/captcha/checkCaptcha")
    DefaultRspDTO<Boolean> checkCaptcha(@RequestParam("captchaReqId")String captchaReqId, @RequestParam("captchaCode") String captchaCode);
}
