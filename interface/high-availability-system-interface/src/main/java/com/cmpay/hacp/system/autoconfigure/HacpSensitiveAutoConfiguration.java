package com.cmpay.hacp.system.autoconfigure;

import com.cmpay.hacp.system.mybatis.crypt.CryptService;
import com.cmpay.hacp.system.mybatis.crypt.CryptServiceFactory;
import com.cmpay.hacp.system.mybatis.crypt.impl.AESCryptServiceImpl;
import com.cmpay.hacp.system.mybatis.crypt.impl.SM4CryptServiceImpl;
import com.cmpay.hacp.system.mybatis.interceptor.MyBatisSensitiveInterceptor;
import com.cmpay.hacp.system.properties.HacpWebAdminProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties({HacpWebAdminProperties.class})
@ConditionalOnClass({MyBatisSensitiveInterceptor.class, HacpWebAdminProperties.class})
public class HacpSensitiveAutoConfiguration {


    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass({AESCryptServiceImpl.class})
    public AESCryptServiceImpl aesCryptServiceImpl(HacpWebAdminProperties hacpWebAdminProperties) {
        return new AESCryptServiceImpl(hacpWebAdminProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass({SM4CryptServiceImpl.class})
    public SM4CryptServiceImpl sm4CryptServiceImpl(HacpWebAdminProperties hacpWebAdminProperties) {
        return new SM4CryptServiceImpl(hacpWebAdminProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass({CryptServiceFactory.class})
    public CryptServiceFactory cryptServiceFactory(List<CryptService> services) {
        return new CryptServiceFactory(services);
    }

    @Bean
    @ConditionalOnBean({CryptServiceFactory.class})
    @ConditionalOnMissingBean
    @ConditionalOnClass({MyBatisSensitiveInterceptor.class})
    public MyBatisSensitiveInterceptor hacpMyBatisSensitiveInterceptor(HacpWebAdminProperties hacpWebAdminProperties,
            CryptServiceFactory cryptServiceFactory) {
        return new MyBatisSensitiveInterceptor(hacpWebAdminProperties, cryptServiceFactory);
    }

}
