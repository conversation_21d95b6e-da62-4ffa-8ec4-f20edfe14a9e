package com.cmpay.hacp.system.autoconfigure;


import com.cmpay.hacp.system.properties.HacpWebAdminProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties({HacpWebAdminProperties.class})
public class HacpSystemManagementAutoConfiguration {
    /**
     * @return
     */
    @ConditionalOnClass({RestTemplateBuilder.class, RestTemplate.class})
    @Bean(name = "restTemplate")
    public RestTemplate restTemplate(HacpWebAdminProperties hacpWebAdminProperties) {
        RestTemplateBuilder restTemplateBuilder = new RestTemplateBuilder();
        RestTemplate restTemplate = restTemplateBuilder.build();
        SimpleClientHttpRequestFactory simpleClientHttpRequestFactory = new SimpleClientHttpRequestFactory();
        HacpWebAdminProperties.Rest rest = hacpWebAdminProperties.getRest();
        simpleClientHttpRequestFactory.setConnectTimeout(rest.getConnectTimeout());
        simpleClientHttpRequestFactory.setReadTimeout(rest.getReadTimeout());
        restTemplate.setRequestFactory(simpleClientHttpRequestFactory);
        return restTemplate;
    }

}
