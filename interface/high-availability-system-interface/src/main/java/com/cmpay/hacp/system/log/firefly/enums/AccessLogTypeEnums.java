package com.cmpay.hacp.system.log.firefly.enums;

/**
 * <AUTHOR>
 */
public enum AccessLogTypeEnums {
    /**
     * 正常日志
     */
    STATIC_ACCESS("1"),
    /**
     * 系统异常日志
     */
    TYPE_EXCEPTION("2"),

    /**
     * 业务异常日志
     */
    BUSINESS_EXCEPTION("3"),
    /**
     * HTTP_CLIENT动态日志
     */
    DYNAMIC_ACCESS_HTTP_CLIENT("4"),
    /**
     * FEIGN_CLIENT动态日志
     */
    DYNAMIC_ACCESS_FEIGN_CLIENT("5"),
    /**
     * HTTP_CLIENT动态日志
     */
    DYNAMIC_ACCESS_FILE_CLIENT("6");

    private final String code;


    AccessLogTypeEnums(String code) {
        this.code = code;
    }

    /**
     * 获取错误码
     *
     * @return
     */
    public String getCode() {
        return code;
    }


}
