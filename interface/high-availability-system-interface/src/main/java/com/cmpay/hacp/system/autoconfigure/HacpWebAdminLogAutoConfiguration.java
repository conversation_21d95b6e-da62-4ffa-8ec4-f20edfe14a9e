package com.cmpay.hacp.system.autoconfigure;

import com.cmpay.hacp.system.log.aspect.CloseableHttpClientAspect;
import com.cmpay.hacp.system.log.aspect.DynamicHttpLogAspect;
import com.cmpay.hacp.system.log.aspect.StaticAccessLogAspect;
import com.cmpay.hacp.system.log.firefly.logger.FireflyDynamicAccessLogger;
import com.cmpay.hacp.system.log.firefly.logger.FireflyLogger;
import com.cmpay.hacp.system.log.firefly.logger.FireflyStaticAccessLogger;
import com.cmpay.hacp.system.log.service.SystemDynamicLogService;
import com.cmpay.hacp.system.log.service.SystemStaticLogService;
import com.cmpay.hacp.system.properties.HacpWebAdminLogProperties;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 日志记录Bean
 *
 * @author: lihuiquan
 */
@Slf4j
@EnableAsync
@Configuration(proxyBeanMethods = false)
public class HacpWebAdminLogAutoConfiguration {

    @Value("${spring.application.name}")
    private String applicationName;


    @Configuration(proxyBeanMethods = false)
    @EnableConfigurationProperties(HacpWebAdminLogProperties.class)
    @ConditionalOnProperty(prefix = "hacp.web.admin.log.static-access", name = "enabled", havingValue = "true", matchIfMissing = true)
    public class HacpWebAdminStaticAccessLogConfiguration {


        @Bean(name = "fireflyStaticAccessLogger")
        @ConditionalOnBean({ObjectMapper.class})
        @ConditionalOnClass({ObjectMapper.class, FireflyLogger.class, FireflyStaticAccessLogger.class})
        public FireflyStaticAccessLogger fireflyStaticAccessLogger(ObjectMapper objectMapper) {
            log.info("init FireflyStaticAccessLogger");
            return new FireflyStaticAccessLogger(objectMapper, LoggerFactory.getLogger("fireflyStaticAccessLogger"));
        }

        @Bean
        @ConditionalOnBean({FireflyStaticAccessLogger.class, SystemStaticLogService.class})
        @ConditionalOnClass({StaticAccessLogAspect.class})
        public StaticAccessLogAspect staticAccessLogAspect(SystemStaticLogService systemStaticLogService,
                ObjectMapper objectMapper,
                FireflyStaticAccessLogger fireflyStaticAccessLogger,
                HacpWebAdminLogProperties hacpWebAdminLogProperties) {
            if (JudgeUtils.isBlank(hacpWebAdminLogProperties.getDataIt())) {
                hacpWebAdminLogProperties.setDataIt(applicationName);
            }
            log.info("init StaticAccessLogAspect");
            return new StaticAccessLogAspect(systemStaticLogService,
                    objectMapper,
                    fireflyStaticAccessLogger,
                    hacpWebAdminLogProperties,
                    applicationName);
        }


    }

    @Configuration(proxyBeanMethods = false)
    @EnableConfigurationProperties(HacpWebAdminLogProperties.class)
    @ConditionalOnProperty(prefix = "hacp.web.admin.log.dynamic-access", name = "enabled", havingValue = "true", matchIfMissing = true)
    public class HacpWebAdminDynamicAccessLogConfiguration {


        @Bean(name = "fireflyDynamicAccessLogger")
        @ConditionalOnBean({ObjectMapper.class})
        @ConditionalOnClass({ObjectMapper.class, FireflyLogger.class, FireflyDynamicAccessLogger.class})
        public FireflyDynamicAccessLogger fireflyDynamicAccessLogger(ObjectMapper objectMapper) {
            log.info("init FireflyDynamicAccessLogger");
            return new FireflyDynamicAccessLogger(objectMapper, LoggerFactory.getLogger("fireflyDynamicAccessLogger"));
        }

        @Bean
        @ConditionalOnBean({SystemDynamicLogService.class})
        @ConditionalOnClass({CloseableHttpClientAspect.class})
        public CloseableHttpClientAspect closeableHttpClientAspect(HacpWebAdminLogProperties hacpWebAdminLogProperties) {
            if (JudgeUtils.isBlank(hacpWebAdminLogProperties.getDataIt())) {
                hacpWebAdminLogProperties.setDataIt(applicationName);
            }
            log.info("init CloseableHttpClientAspect");
            return new CloseableHttpClientAspect(applicationName, hacpWebAdminLogProperties);
        }

        @Bean
        @ConditionalOnBean({FireflyDynamicAccessLogger.class, CloseableHttpClientAspect.class, SystemDynamicLogService.class})
        @ConditionalOnClass({DynamicHttpLogAspect.class})
        public DynamicHttpLogAspect dynamicHttpLogAspect(SystemDynamicLogService systemDynamicLogService,
                ObjectMapper objectMapper,
                HacpWebAdminLogProperties hacpWebAdminLogProperties,
                FireflyDynamicAccessLogger fireflyDynamicAccessLogger) {
            log.info("init DynamicHttpLogAspect");
            return new DynamicHttpLogAspect(systemDynamicLogService, objectMapper, hacpWebAdminLogProperties, fireflyDynamicAccessLogger);
        }

    }


}
