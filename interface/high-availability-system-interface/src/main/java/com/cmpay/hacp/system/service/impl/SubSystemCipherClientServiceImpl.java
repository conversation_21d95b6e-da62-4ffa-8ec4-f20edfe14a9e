package com.cmpay.hacp.system.service.impl;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dto.system.CipherDataInfoReqDTO;
import com.cmpay.hacp.system.client.SystemCipherClient;
import com.cmpay.hacp.system.service.SubAppCipherService;
import com.cmpay.hacp.system.service.SubSystemCipherService;
import com.cmpay.hacp.utils.crypto.SM2EncryptorUtil;
import com.cmpay.hacp.utils.security.TokenUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SubSystemCipherClientServiceImpl implements SubSystemCipherService {

    private final SystemCipherClient systemCipherClient;

    private final SubAppCipherService subAppCipherService;
    @Override
    public String decryptDataBySm4AndSm2(String sm4key, String data) {
        CipherDataInfoReqDTO reqDTO =  new CipherDataInfoReqDTO();
        reqDTO.setKey(sm4key);
        reqDTO.setEncryptData(data);
        GenericRspDTO<String> rspDTO = systemCipherClient.decryptDataBySm4AndSm2(reqDTO, TokenUtil.getToken());
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(rspDTO.getMsgCd(),rspDTO.getMsgInfo());
        }
        return rspDTO.getBody();
    }

    @Override
    public String subDecryptData(String data) {
        String key = subAppCipherService.getPrivateKey();
        return SM2EncryptorUtil.decrypt(key,data);
    }


    @Override
    public String getSm4RandomSalt(String key, String key2) {
        GenericRspDTO<String> rspDTO = systemCipherClient.getSm4RandomSalt(key, TokenUtil.getToken());
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(rspDTO.getMsgCd(),rspDTO.getMsgInfo());
        }
        return rspDTO.getBody();
    }

}
