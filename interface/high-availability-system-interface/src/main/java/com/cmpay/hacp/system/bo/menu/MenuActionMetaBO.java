package com.cmpay.hacp.system.bo.menu;

import lombok.Data;

import java.util.Map;

/**
 * 菜单操作 RSP 元 DTO
 *
 * <AUTHOR>
 * @create 2024/05/09 15:12:24
 * @since 1.0.0
 */
@Data
public class MenuActionMetaBO {

    private Long parentId;

    private String parentName;

    private String type;

    private Long menuId;

    private String name;

    private String url;

    private String perms;

    private String icon;

    private Long orderNum;

    private Map meta;

    private String component;

    private String redirect;

    private String enName;
    /**
     * 隐藏标题
     */
    private Boolean hideTitle;
    /**
     * 显示
     */
    private Boolean hidden;
    /**
     * 隐藏子菜单
     */
    private Boolean hideChildren;

    /**
     * 是否缓存
     */
    private Boolean keepalive;

    /**
     * 隐藏页面标题栏
     */
    private Boolean hidePageTitleBar;
}
