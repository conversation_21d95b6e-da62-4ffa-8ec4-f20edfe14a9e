package com.cmpay.hacp.system.bo.menu;

import lombok.Data;

import java.util.List;

/**
 * @author: tnw
 * @Date: 2020/02/15
 * 菜单资源返回DTO
 */
@Data
public class MenuTreeBO {


    private List<MenuTreeBO> children;

    private Long parentId;

    private String parentName;

    private String type;

    private Long menuId;

    private String name;

    private String url;

    private String perms;

    private String icon;

    private Long orderNum;

    private String meta;

    private String component;

    private String redirect;

    private String enName;

    /**
     * 隐藏标题
     */
    private Boolean hideTitle;
    /**
     * 显示
     */
    private Boolean hidden;
    /**
     * 隐藏子菜单
     */
    private Boolean hideChildren;

    /**
     * 是否缓存
     */
    private Boolean keepalive;

    /**
     * 隐藏页面标题栏
     */
    private Boolean hidePageTitleBar;

}
