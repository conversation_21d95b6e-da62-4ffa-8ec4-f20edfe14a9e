package com.cmpay.hacp.system.auth;

import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.service.SystemCaptchaService;
import com.cmpay.hacp.system.service.SystemLoginService;
import com.cmpay.lemon.common.codec.ObjectDecoder;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.exception.ErrorMsgCode;
import com.cmpay.lemon.common.exception.LemonException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.security.SimpleUserInfo;
import com.cmpay.lemon.framework.security.UserInfoBase;
import com.cmpay.lemon.framework.security.auth.AbstractGenericMatchableAuthenticationProcessor;
import com.cmpay.lemon.framework.security.auth.AuthenticationRequest;
import com.cmpay.lemon.framework.security.auth.GenericAuthenticationToken;
import com.cmpay.lemon.framework.utils.WebUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.AuthenticationException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: lihuiquan
 */
public class HacpWebAdminLoginAuthenticationProcessor extends AbstractGenericMatchableAuthenticationProcessor<GenericAuthenticationToken> {

    private static final Logger logger = LoggerFactory.getLogger(HacpWebAdminLoginAuthenticationProcessor.class);

    public static final String USERNAME = "username";

    public static final String PASSWORD = "password";

    public static final String CAPTCHA_CODE = "captchaCode";

    public static final String CAPTCHA_REQ_ID = "captchaReqId";

    private final ObjectDecoder objectDecoder;

    private final SystemLoginService systemLoginService;

    private final ObjectMapper lemonWebAdminObjectMapper;

    private final SystemCaptchaService systemCaptchaService;

    /**
     * "filterProcessesUrl"前缀必须与"lemon.security.authentication.loginPathPrefix"一致
     */
    public HacpWebAdminLoginAuthenticationProcessor(SystemLoginService systemLoginService,
            String filterProcessesUrl,
            ObjectDecoder objectDecoder,
            ObjectMapper lemonWebAdminObjectMapper,
            SystemCaptchaService systemCaptchaService) {
        super(filterProcessesUrl);
        this.objectDecoder = objectDecoder;
        this.systemLoginService = systemLoginService;
        this.lemonWebAdminObjectMapper = lemonWebAdminObjectMapper;
        this.systemCaptchaService = systemCaptchaService;
    }

    @Override
    protected UserInfoBase doProcessAuthentication(GenericAuthenticationToken genericAuthenticationToken) throws AuthenticationException {
        AuthenticationRequest authenticationRequest = genericAuthenticationToken.getAuthenticationRequest();
        Map<String, String> authenticationRequestParameters = new HashMap<>();
        try {
            HttpServletRequest httpServletRequest = authenticationRequest.getHttpServletRequest();
            InputStream inputStream = getRequestInputStream(authenticationRequest);
            if (inputStream != null && inputStream.available() > 0) {
                authenticationRequestParameters = this.objectDecoder.readValue(inputStream, Map.class);
            } else {
                authenticationRequestParameters = Optional.ofNullable(httpServletRequest.getParameterMap())
                        .map(pm -> pm.entrySet().stream().map(e ->
                                        new AbstractMap.SimpleEntry<>(e.getKey(), Optional.ofNullable(e.getValue()).map(v -> v[0]).orElse(null)))
                                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)))
                        .orElse(null);
            }
        } catch (Exception e) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, e);
        }
        if (JudgeUtils.isEmpty(authenticationRequestParameters)) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, "No authentication parameter found in request body.");
        }
        //获取登录参数
        UserLoginBO userLogin = new UserLoginBO();
        userLogin.setUsername(authenticationRequestParameters.get(USERNAME));
        userLogin.setPassword(authenticationRequestParameters.get(PASSWORD));
        userLogin.setCaptchaCode(authenticationRequestParameters.get(CAPTCHA_CODE));
        userLogin.setCaptchaReqId(authenticationRequestParameters.get(CAPTCHA_REQ_ID));
        boolean checkResult = systemCaptchaService.checkCaptcha(userLogin.getCaptchaReqId(), userLogin.getCaptchaCode());
        if (!checkResult) {
            BusinessException.throwBusinessException(MsgEnum.WRONG_IMAGE_CODE);
        }
        UserLoginBO userLoginBO = systemLoginService.login(userLogin);
        //session 暂时存入
        HttpSession httpSession = WebUtils.getHttpServletRequest().getSession();
        try {
            String userInfoJson = this.lemonWebAdminObjectMapper.writeValueAsString(userLoginBO);
            httpSession.setAttribute(HacpWebAdminAuthenticationSuccessHandler.LOGIN_INFO, userInfoJson);
        } catch (Exception e) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, e);
        }
        return new SimpleUserInfo(userLoginBO.getUserInfo().getUpmsUserId(),
                userLoginBO.getUserInfo().getUserName(),
                userLoginBO.getUserInfo().getMobile());
    }

    protected static InputStream getRequestInputStream(AuthenticationRequest authenticationRequest) {
        try {
            return authenticationRequest.getHttpServletRequest().getInputStream();
        } catch (IOException ex) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, ex);
            return null;
        }
    }

}


