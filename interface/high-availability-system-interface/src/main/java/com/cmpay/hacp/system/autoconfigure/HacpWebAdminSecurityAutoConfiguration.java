package com.cmpay.hacp.system.autoconfigure;

import com.cmpay.hacp.system.service.SystemCaptchaService;
import com.cmpay.hacp.system.service.SystemLoginService;
import com.cmpay.hacp.system.auth.*;
import com.cmpay.hacp.utils.security.LoginPathUtil;
import com.cmpay.lemon.common.Env;
import com.cmpay.lemon.common.codec.ObjectDecoder;
import com.cmpay.lemon.framework.autoconfigure.condition.ConditionalOnEnvironment;
import com.cmpay.lemon.framework.autoconfigure.security.SecurityAuthenticationConfiguration;
import com.cmpay.lemon.framework.autoconfigure.security.SecurityAutoConfiguration;
import com.cmpay.lemon.framework.autoconfigure.security.SecurityProperties;
import com.cmpay.lemon.framework.data.InternalDataHelper;
import com.cmpay.lemon.framework.response.ResponseMessageResolver;
import com.cmpay.lemon.framework.security.RefreshTokenService;
import com.cmpay.lemon.framework.security.auth.MatchableAuthenticationProcessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.web.access.expression.DefaultWebSecurityExpressionHandler;
import org.springframework.security.web.authentication.session.ConcurrentSessionControlAuthenticationStrategy;


/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@AutoConfigureBefore({SecurityAutoConfiguration.class, SecurityAuthenticationConfiguration.class})
@EnableGlobalMethodSecurity(prePostEnabled = true)
@ConditionalOnClass({MatchableAuthenticationProcessor.class, SecurityAutoConfiguration.class})
public class HacpWebAdminSecurityAutoConfiguration {

    /**
     * 密码登录
     *
     * @param systemLoginService
     * @param objectDecoder
     * @param securityProperties
     * @param lemonWebAdminObjectMapper
     * @param systemCaptchaService
     * @return
     */
    @Bean
    @Primary
    @ConditionalOnClass({HacpWebAdminLoginAuthenticationProcessor.class})
    public MatchableAuthenticationProcessor lemonWebAdminLoginAuthenticationProcessor(SystemLoginService systemLoginService,
            ObjectDecoder objectDecoder, SecurityProperties securityProperties,
            @Qualifier("hacpObjectMapper") ObjectMapper lemonWebAdminObjectMapper,
            SystemCaptchaService systemCaptchaService) {
        return new HacpWebAdminLoginAuthenticationProcessor(systemLoginService,
                LoginPathUtil.getLoginPathPrefix(securityProperties),
                objectDecoder,
                lemonWebAdminObjectMapper,
                systemCaptchaService);
    }

    /**
     * 认证异常，返回处理
     *
     * @param responseMessageResolver
     * @return
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass({HacpWebAdminAuthenticationLogoutEntryPoint.class})
    public HacpWebAdminAuthenticationLogoutEntryPoint lemonWebAdminAuthenticationLogoutEntryPoint(ResponseMessageResolver responseMessageResolver) {
        return new HacpWebAdminAuthenticationLogoutEntryPoint(responseMessageResolver);
    }

    /**
     * 多终端登录控制Bean
     *
     * @param sessionRegistry
     * @return
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnEnvironment(envs = {Env.PRD, Env.PRE})
    @ConditionalOnProperty(prefix = "hacp.web.admin.session", name = "control", havingValue = "true", matchIfMissing = true)
    @ConditionalOnClass({ConcurrentSessionControlAuthenticationStrategy.class})
    public ConcurrentSessionControlAuthenticationStrategy concurrentSessionControlAuthenticationStrategy(SessionRegistry sessionRegistry) {
        return new ConcurrentSessionControlAuthenticationStrategy(sessionRegistry);
    }

    /**
     * 多终端登录踢出回调
     *
     * @param responseMessageResolver
     * @return
     */
    @Bean
    @ConditionalOnBean(ConcurrentSessionControlAuthenticationStrategy.class)
    @ConditionalOnClass({HacpWebAdminTerminalSessionInformationExpiredStrategy.class})
    public HacpWebAdminTerminalSessionInformationExpiredStrategy lemonWebAdminTerminalSessionInformationExpiredStrategy(
            ResponseMessageResolver responseMessageResolver) {
        return new HacpWebAdminTerminalSessionInformationExpiredStrategy(responseMessageResolver);
    }

    /**
     * 成功后置处理
     *
     * @param responseMessageResolver
     * @param internalDataHelper
     * @param refreshTokenService
     * @param objectMapper
     * @return
     */
    @Bean(name = "lemonAuthenticationSuccessHandler")
    @ConditionalOnMissingBean
    @ConditionalOnClass({HacpWebAdminAuthenticationSuccessHandler.class})
    public HacpWebAdminAuthenticationSuccessHandler lemonAuthenticationSuccessHandler(
            ResponseMessageResolver responseMessageResolver, InternalDataHelper internalDataHelper,
            RefreshTokenService refreshTokenService, @Qualifier("hacpObjectMapper") ObjectMapper objectMapper) {
        return new HacpWebAdminAuthenticationSuccessHandler(responseMessageResolver, refreshTokenService,
                internalDataHelper, objectMapper);
    }

    /**
     * 接口权限控制
     *
     * @param webPermissionEvaluator
     * @return
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass({DefaultWebSecurityExpressionHandler.class})
    public DefaultWebSecurityExpressionHandler defaultWebSecurityExpressionHandler(WebPermissionEvaluator webPermissionEvaluator) {
        DefaultWebSecurityExpressionHandler defaultWebSecurityExpressionHandler = new DefaultWebSecurityExpressionHandler();
        defaultWebSecurityExpressionHandler.setPermissionEvaluator(webPermissionEvaluator);
        return defaultWebSecurityExpressionHandler;
    }
}
