package com.cmpay.hacp.system.mybatis.crypt;


import com.cmpay.hacp.annotation.mybatis.CryptType;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class CryptServiceFactory {

    private Map<String, CryptService> crypts = new HashMap<>();

    public CryptServiceFactory(List<CryptService> cryptServices) {
        if (null != cryptServices && !cryptServices.isEmpty()) {
            for (CryptService cryptService : cryptServices) {
                this.setCrypt(cryptService.type(), cryptService);
            }
        }
    }

    /**
     * 获取加密方式
     *
     * @param cryptType 加密方式枚举
     * @return 机密方式实现类
     */
    public CryptService getCrypt(String cryptType) {
        CryptService cryptService = crypts.get(cryptType);
        if (cryptService == null) {
            cryptService = crypts.get(CryptType.AES);
        }
        return cryptService;
    }

    /**
     * 设置加密方式
     *
     * @param cryptType    加密类型
     * @param cryptService 加载方式
     */
    private void setCrypt(String cryptType, CryptService cryptService) {
        crypts.put(cryptType, cryptService);
    }

}
