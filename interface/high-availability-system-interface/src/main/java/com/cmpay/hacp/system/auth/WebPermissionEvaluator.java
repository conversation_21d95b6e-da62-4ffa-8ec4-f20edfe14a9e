package com.cmpay.hacp.system.auth;

import com.cmpay.hacp.system.bo.menu.MenuActionMetaBO;
import com.cmpay.hacp.system.bo.menu.MenuTreeMetaBO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.service.SystemPermissionService;
import com.cmpay.lemon.common.context.LemonContext;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.security.LemonUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义权限过滤器
 *
 * <AUTHOR>
 */
@Configuration
public class WebPermissionEvaluator implements PermissionEvaluator {

    private static final Logger logger = LoggerFactory.getLogger(WebPermissionEvaluator.class);

    private static final String TITLE = "TITLE";

    private static final String[] SPLITS = {",", "/"};

    @Value("${spring.application.name}")
    private String applicationName;

    @Autowired
    private SystemPermissionService systemPermissionService;

    /**
     * 检查是否拥有该权限
     *
     * @param authentication
     * @param targetDomainObject
     * @param permission
     * @return
     */
    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        LemonUser principal = (LemonUser) authentication.getPrincipal();
        String userId = principal.getUserInfo().getUserId();
        PermMenuTreeMetaBO userPermissionsByUpms = systemPermissionService.queryUserPermissions(userId, applicationName);
        List<String> permissions = systemPermissionService.getUserPermissions(userId, applicationName);
        if (JudgeUtils.isNotEmpty(permissions)) {
            for (String permissiondb : permissions) {
                //查询权限是否匹配
                if (JudgeUtils.equalsIgnoreCase(permission.toString(), permissiondb)) {
                    logger.info("有权限,用户:{},接口:{},权限标识:{}", principal.getUserInfo().getUserId(), targetDomainObject, permission);
                    //保存标题至上下文
                    try {
                        this.getTitle(userPermissionsByUpms, permissiondb);
                    } catch (Exception e) {
                        logger.warn("get logger title fail {}", e.getMessage());
                    }
                    return true;
                }
            }
        }
        logger.warn("没有权限,用户:{},接口:{},权限标识:{}", principal.getUserInfo().getUserId(), targetDomainObject, permission);
        BusinessException.throwBusinessException(MsgEnum.MENU_NO_PERMISSION);
        return false;
    }

    /**
     * 获取当前功能按钮的标题
     * 暂时只支持三级按钮功能eg
     * /系统设置/用户管理/新增
     *
     * @param userPermissions
     * @param permission
     */
    private void getTitle(PermMenuTreeMetaBO userPermissions, String permission) {
        List<MenuActionMetaBO> menuActionList = userPermissions.getMenuActionList();
        if (JudgeUtils.isEmpty(menuActionList)) {
            return;
        }
        MenuActionMetaBO menuActionRspMetaDTO = null;
        for (int i = 0; i < menuActionList.size(); i++) {
            MenuActionMetaBO actionRspMetaDTO = menuActionList.get(i);
            String[] perms = actionRspMetaDTO.getPerms().split(SPLITS[0]);
            for (int j = 0; j < perms.length; j++) {
                if (JudgeUtils.equalsIgnoreCase(permission, perms[j])) {
                    menuActionRspMetaDTO = actionRspMetaDTO;
                    break;
                }
            }
            //找到权限按钮，结束当前所有循环
            if (JudgeUtils.isNotNull(menuActionRspMetaDTO)) {
                break;
            }
        }

        //找上级菜单
        if (JudgeUtils.isNotNull(menuActionRspMetaDTO)) {
            List<String> menuNames = new ArrayList<>();
            menuNames.add(menuActionRspMetaDTO.getName());
            List<MenuTreeMetaBO> menuTreeList = userPermissions.getMenuTreeList();
            if (JudgeUtils.isEmpty(menuTreeList)) {
                return;
            }
            this.isExist(menuNames, menuActionRspMetaDTO.getParentId(), menuTreeList);
            String title = "";
            for (int i = 0; i < menuNames.size(); i++) {
                if (i == 0) {
                    title = menuNames.get(i) + title;
                } else {
                    title = menuNames.get(i) + SPLITS[1] + title;
                }
            }
            if (menuNames.size() > 1) {
                title = SPLITS[1] + title;
            }
            LemonContext.getCurrentContext().put(TITLE, title);
        }
    }

    private boolean isExist(List<String> menuNames, Long parentId, List<MenuTreeMetaBO> menuTreeList) {
        for (int i = 0; i < menuTreeList.size(); i++) {
            MenuTreeMetaBO menuTreeRspMetaDTO = menuTreeList.get(i);
            if (menuTreeRspMetaDTO.getMenuId().longValue() == parentId.longValue()) {
                menuNames.add(menuTreeRspMetaDTO.getName());
                return true;
            } else if (JudgeUtils.isNotEmpty(menuTreeRspMetaDTO.getChildren())) {
                boolean isExist = this.isExist(menuNames, parentId, menuTreeRspMetaDTO.getChildren());
                if (isExist) {
                    menuNames.add(menuTreeRspMetaDTO.getName());
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        return false;
    }
}
