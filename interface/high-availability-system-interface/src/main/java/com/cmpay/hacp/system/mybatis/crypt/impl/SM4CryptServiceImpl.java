package com.cmpay.hacp.system.mybatis.crypt.impl;

import com.cmpay.hacp.annotation.mybatis.CryptType;
import com.cmpay.hacp.system.mybatis.crypt.CryptService;
import com.cmpay.hacp.system.properties.HacpWebAdminProperties;
import com.cmpay.hacp.utils.crypto.AESEncryptorUtil;
import com.cmpay.hacp.utils.crypto.SM4EncryptorUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SM4CryptServiceImpl implements CryptService {

    private HacpWebAdminProperties.Sensitive sensitive;

    public SM4CryptServiceImpl(HacpWebAdminProperties HacpWebAdminProperties) {
        this.sensitive = HacpWebAdminProperties.getSensitive();
    }

    @Override
    public String encrypt(String plain) {
        try {
            return SM4EncryptorUtil.encryptEcb(sensitive.getSM4Key(), plain);
        } catch (Exception e) {
            log.warn("SM4CryptImpl encrypt Exception {}, plain {}", e.getMessage(), plain);
            return plain;
        }
    }

    @Override
    public String decrypt(String cipher) {
        try {
            return SM4EncryptorUtil.decryptEcb(sensitive.getSM4Key(), cipher);
        } catch (Exception e) {
            //兼容历史数据
            try {
                return AESEncryptorUtil.decrypt(sensitive.getAesKey(), cipher);
            } catch (Exception ex) {
                log.warn("SM4CryptImpl AESEncryptorUtil.decrypt Exception {}, cipher {}", e.getMessage(), cipher);
                return cipher;
            }
        }
    }

    @Override
    public String type() {
        return CryptType.SM4;
    }
}
