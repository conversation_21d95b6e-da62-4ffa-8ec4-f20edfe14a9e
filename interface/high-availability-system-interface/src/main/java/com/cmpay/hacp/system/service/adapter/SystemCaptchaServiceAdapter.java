package com.cmpay.hacp.system.service.adapter;

import com.cmpay.hacp.system.service.SystemCaptchaService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public abstract class SystemCaptchaServiceAdapter implements SystemCaptchaService {

    @Override
    public boolean checkCaptcha(String captchaReqId, String captchaCode) {
        return false;
    }

    @Override
    public void captcha(HttpServletRequest request, HttpServletResponse resp, String captchaReqId) {

    }
}
