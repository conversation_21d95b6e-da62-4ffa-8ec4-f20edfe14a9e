package com.cmpay.hacp.system.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 日志开关配置
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "hacp.web.admin.log")
public class HacpWebAdminLogProperties {


    /**
     * 登记日志所属应用id,如果不配置则取spring.application.name的值
     */
    private String dataIt;
    /**
     * 是否管理日志数据
     */
    private Boolean management;

    private StaticAccess staticAccess = new StaticAccess();

    private DynamicAccess dynamicAccess = new DynamicAccess();

    @Data
    public static class StaticAccess {
        /**
         * 是否启用日志登记,默认启用
         */
        private boolean enabled = true;

        /**
         * 是否只登记登录过的用户日志，默认是
         */
        private boolean recordNeedLogin = true;

        /**
         * 返回消息码字段的名称
         */
        private String messageCode = "msgCd";

        /**
         * 不登记日志的接口配置，分割符“,”
         */
        private String permitUrl;

    }

    @Data
    public static class DynamicAccess {
        /**
         * 是否启用日志登记,默认启用
         */
        private boolean enabled = true;

        /**
         * 默认的dataPath
         */
        private String defaultDataPath = "defaultDataPath";

        /**
         * 默认的operator
         */
        private String defaultOperator = "Scheduled";

        /**
         * 默认的operatorStatus
         */
        private String defaultOperatorStatus = "500";

        /**
         * 返回消息码字段的名称
         */
        private String messageCode = "msgCd";

    }

}
