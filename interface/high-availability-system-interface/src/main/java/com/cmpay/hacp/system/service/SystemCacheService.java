package com.cmpay.hacp.system.service;

import com.hazelcast.collection.IList;
import com.hazelcast.collection.IQueue;
import com.hazelcast.map.IMap;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public interface SystemCacheService {
    /**
     * 获取缓存
     *
     * @param key
     * @return
     */
    Object getValue(String key);

    /**
     * 获取缓存
     *
     * @param key
     * @return
     */
    Object getHashMap(String key);

    /**
     * 获取缓存
     *
     * @param key
     * @return
     */
    Object getHashMap(String key, String filed);

    /**
     * 删除缓存
     *
     * @param key
     * @return
     */
    boolean delete(String key);

    /**
     * 设置缓存
     *
     * @param key
     * @return
     */
    boolean setValue(String key, Object value, long time, TimeUnit timeUnit);

    /**
     * 查询缓存是否存在
     *
     * @param key
     * @return
     */
    boolean exists(String key);

    /**
     * 获取分布式锁 获取到返回true
     *
     * @param key
     * @param value
     * @param time
     * @param timeUnit
     * @return
     */
    boolean setIfAbsent(String key, Object value, long time, TimeUnit timeUnit);

    /**
     * 设置hash缓存
     *
     * @param key
     * @param map
     * @param time
     * @param timeUnit
     * @return
     */
    boolean setHashMap(String key, Map map, long time, TimeUnit timeUnit);

    int drainToQueue(String key, List<String> list, int maxSize);

    boolean offerQueue(String key,  String value);

    IList<String> getList(String key);

    IQueue<String> getQueue(String key);

    IMap<Object, Object> getMap(String key);
}
