package com.cmpay.hacp.system.service.impl;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dto.system.UserLoginReqDTO;
import com.cmpay.hacp.dto.system.UserLoginRspDTO;
import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.hacp.system.client.SystemLoginClient;
import com.cmpay.hacp.system.service.adapter.SystemLoginServiceAdapter;
import com.cmpay.hacp.system.utils.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SystemLoginClientServiceImpl extends SystemLoginServiceAdapter {

    private final SystemLoginClient systemLoginClient;

    @Override
    public UserLoginBO login(UserLoginBO userLoginBO) {
        UserLoginReqDTO loginReqDTO = new UserLoginReqDTO();
        BeanUtils.copyProperties(loginReqDTO, userLoginBO);
        GenericRspDTO<UserLoginRspDTO> genericRspDTO = systemLoginClient.login(loginReqDTO);
        if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(genericRspDTO.getMsgCd(),genericRspDTO.getMsgInfo());
        }
        UserLoginRspDTO loginRspDTO = genericRspDTO.getBody();
        return BeanConvertUtil.getUserLoginBO(loginRspDTO);
    }
}
