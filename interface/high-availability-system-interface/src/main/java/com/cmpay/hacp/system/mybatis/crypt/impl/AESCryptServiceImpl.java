package com.cmpay.hacp.system.mybatis.crypt.impl;

import com.cmpay.hacp.annotation.mybatis.CryptType;
import com.cmpay.hacp.system.mybatis.crypt.CryptService;
import com.cmpay.hacp.system.properties.HacpWebAdminProperties;
import com.cmpay.hacp.utils.crypto.AESEncryptorUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class AESCryptServiceImpl implements CryptService {

    private HacpWebAdminProperties.Sensitive sensitive;

    public AESCryptServiceImpl(HacpWebAdminProperties hacpWebAdminProperties) {
        this.sensitive = hacpWebAdminProperties.getSensitive();
    }

    @Override
    public String encrypt(String plain) {
        try {
            return AESEncryptorUtil.encrypt(sensitive.getAesKey(), plain);
        } catch (Exception e) {
            log.warn("AESCryptImpl encrypt Exception {}, plain {}", e.getMessage(), plain);
            return plain;
        }
    }

    @Override
    public String decrypt(String cipher) {
        try {
            return AESEncryptorUtil.decrypt(sensitive.getAesKey(), cipher);
        } catch (Exception e) {
            log.warn("AESCryptImpl decrypt Exception {}, cipher {}", e.getMessage(), cipher);
            return cipher;
        }
    }

    @Override
    public String type() {
        return CryptType.AES;
    }

}
