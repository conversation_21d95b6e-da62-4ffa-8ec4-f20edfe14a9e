package com.cmpay.hacp.system.bo.system;

import com.cmpay.hacp.bo.system.SessionTokenVO;
import lombok.Data;

/**
 * 描述
 *
 * <AUTHOR> tnw
 * @date : 2018/10/31
 */
@Data
public class UserLoginBO {
    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 图形验证码
     */
    private String captcha;


    /**
     * 短信验证码
     */
    private String messageCode;

    /**
     * 图片验证码请求ID
     */
    private String captchaReqId;

    /**
     * 图片验证码
     */
    private String captchaCode;


    /**
     * 上次登陆时间
     */
    private String lastLoginTime;

    /**
     * 是否需要提醒用户修改密码
     */
    private String pwdNeedToModify;

    /**
     * 会话token  信息登录平台
     */
    private SessionTokenVO userInfo;

    /**
     * 单点登录票据
     */
    private String ticket;


    /**
     * 上一次登录时间
     */
    private LoginHistoryLogBO loginHistory;

}
