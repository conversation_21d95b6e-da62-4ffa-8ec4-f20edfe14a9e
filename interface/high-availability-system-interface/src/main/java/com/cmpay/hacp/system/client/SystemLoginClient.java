package com.cmpay.hacp.system.client;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemLoginApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.dto.system.UserLoginReqDTO;
import com.cmpay.hacp.dto.system.UserLoginRspDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform}", url = "${hacp.management.discovery.url:}", contextId = "systemLoginClient")
public interface SystemLoginClient {


    /**
     * 登录效验接口，提供给子系统
     *
     * @param userLoginReqDTO
     * @return
     */
    @PostMapping(VersionApi.VERSION_V1 + SystemLoginApi.LOGIN)
    GenericRspDTO<UserLoginRspDTO> login(@RequestBody UserLoginReqDTO userLoginReqDTO);
}
