package com.cmpay.hacp.system.service.adapter;

import com.cmpay.hacp.system.service.SubSystemCipherService;

public abstract class SubSystemCipherServiceAdapter implements SubSystemCipherService {

    @Override
    public String decryptDataBySm4AndSm2(String sm4Key, String data) {
        return null;
    }

    @Override
    public String subDecryptData(String data) {
        return null;
    }

    @Override
    public String getSm4RandomSalt(String key, String captchaReqId) {
        return null;
    }
}
