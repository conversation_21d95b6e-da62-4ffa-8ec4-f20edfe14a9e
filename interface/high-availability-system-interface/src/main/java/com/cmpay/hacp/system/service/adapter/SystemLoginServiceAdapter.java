package com.cmpay.hacp.system.service.adapter;

import com.cmpay.hacp.system.bo.system.LoginBO;
import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.hacp.system.service.SystemLoginService;

public abstract class SystemLoginServiceAdapter implements SystemLoginService {

    @Override
    public UserLoginBO login(UserLoginBO userLoginBO) {
        return null;
    }

    @Override
    public UserLoginBO getUserLoginBO(LoginBO loginRspDTO) {
        return null;
    }

    @Override
    public LoginBO passwordLogin(LoginBO loginBO) {
        return null;
    }
}
