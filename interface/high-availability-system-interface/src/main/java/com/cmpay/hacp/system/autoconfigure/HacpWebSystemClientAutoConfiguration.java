package com.cmpay.hacp.system.autoconfigure;

import com.cmpay.hacp.system.client.SystemCaptchaServiceClient;
import com.cmpay.hacp.system.client.SystemCipherClient;
import com.cmpay.hacp.system.client.SystemLoginClient;
import com.cmpay.hacp.system.client.SystemPermissionClient;
import com.cmpay.hacp.system.log.client.SystemDynamicLogClient;
import com.cmpay.hacp.system.log.client.SystemStaticLogClient;
import com.cmpay.hacp.system.log.service.SystemDynamicLogService;
import com.cmpay.hacp.system.log.service.SystemStaticLogService;
import com.cmpay.hacp.system.log.service.impl.SystemDynamicAccessLogClientImpl;
import com.cmpay.hacp.system.log.service.impl.SystemStaticLogClientImpl;
import com.cmpay.hacp.system.service.*;
import com.cmpay.hacp.system.service.impl.SubSystemCipherClientServiceImpl;
import com.cmpay.hacp.system.service.impl.SystemCaptchaServiceClientImpl;
import com.cmpay.hacp.system.service.impl.SystemLoginClientServiceImpl;
import com.cmpay.hacp.system.service.impl.SystemPermissionClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "hacp.management.discovery", name = "enabled", havingValue = "true", matchIfMissing = true)
@AutoConfigureBefore({ HacpWebAdminLogAutoConfiguration.class,HacpWebAdminSecurityAutoConfiguration.class})
public class HacpWebSystemClientAutoConfiguration {


    @Bean
    @ConditionalOnMissingBean(SystemStaticLogService.class)
    public SystemStaticLogService systemStaticLogClientImpl(SystemStaticLogClient systemStaticLogClient) {
        log.info("init RiseSystemLogClientImpl");
        return new SystemStaticLogClientImpl(systemStaticLogClient);
    }


    /**
     *
     * @param systemDynamicLogClient
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(SystemDynamicLogService.class)
    public SystemDynamicLogService dynamicAccessLogClientImpl(SystemDynamicLogClient systemDynamicLogClient) {
        log.info("init SystemDynamicLogClientImpl");
        return new SystemDynamicAccessLogClientImpl(systemDynamicLogClient);
    }

    /**
     *
     * @param systemPermissionClient
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(SystemPermissionService.class)
    public SystemPermissionService systemPermissionClientImpl(SystemPermissionClient systemPermissionClient) {
        log.info("init SystemDynamicLogClientImpl");
        return new SystemPermissionClientImpl(systemPermissionClient);
    }

    @Bean
    @ConditionalOnMissingBean(SystemLoginService.class)
    public SystemLoginService SystemLoginClientServiceImpl(SystemLoginClient systemLoginClient) {
        log.info("init SystemLoginClientServiceImpl");
        return new SystemLoginClientServiceImpl(systemLoginClient);
    }


    @Bean
    @ConditionalOnMissingBean(SystemCaptchaService.class)
    public SystemCaptchaService systemCaptchaServiceClientImpl(SystemCaptchaServiceClient systemCaptchaServiceClient) {
        log.info("init SystemCaptchaServiceClientImpl");
        return new SystemCaptchaServiceClientImpl(systemCaptchaServiceClient);
    }

    @Bean
    @ConditionalOnMissingBean(SubSystemCipherService.class)
    public SubSystemCipherService systemCipherClientServiceImpl(SystemCipherClient systemCipherClient, SubAppCipherService subAppCipherService) {
        log.info("init SubSystemCipherClientServiceImpl");
        return new SubSystemCipherClientServiceImpl(systemCipherClient,subAppCipherService);
    }

}
