package com.cmpay.hacp.system.service.impl;

import com.cmpay.hacp.system.service.SystemCacheService;
import com.hazelcast.collection.IList;
import com.hazelcast.collection.IQueue;
import com.hazelcast.map.IMap;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class RedisCacheServiceImpl implements SystemCacheService {

    private final RedisTemplate redisTemplate;

    public RedisCacheServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }


    @Override
    public Object getValue(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    @Override
    public Object getHashMap(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    @Override
    public Object getHashMap(String key, String filed) {
        return redisTemplate.opsForHash().get(key, filed);
    }

    @Override
    public boolean delete(String key) {
        return redisTemplate.delete(key);
    }

    @Override
    public boolean setValue(String key, Object value, long time, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, time, timeUnit);
        return this.exists(key);
    }

    @Override
    public boolean exists(String key) {
        return redisTemplate.hasKey(key);
    }

    @Override
    public boolean setIfAbsent(String key, Object value, long time, TimeUnit timeUnit) {
        return redisTemplate.opsForValue().setIfAbsent(key, value, time, timeUnit);
    }

    @Override
    public boolean setHashMap(String key, Map map, long time, TimeUnit timeUnit) {
        redisTemplate.opsForHash().putAll(key, map);
        redisTemplate.expire(key, time, timeUnit);
        return this.exists(key);
    }

    @Override
    public int drainToQueue(String key, List<String> list, int maxSize) {
        return 0;
    }


    @Override
    public boolean offerQueue(String key, String value) {
        return false;
    }

    @Override
    public IList<String> getList(String key) {
        return null;
    }

    @Override
    public IQueue<String> getQueue(String key) {
        return null;
    }

    @Override
    public IMap<Object, Object> getMap(String key) {
        return null;
    }
}
