package com.cmpay.hacp.system.service.impl;

import com.cmpay.hacp.system.client.SystemCaptchaServiceClient;
import com.cmpay.hacp.system.service.adapter.SystemCaptchaServiceAdapter;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SystemCaptchaServiceClientImpl extends SystemCaptchaServiceAdapter {

    private final SystemCaptchaServiceClient systemCaptchaServiceClient;

    @Override
    public boolean checkCaptcha(String captchaReqId, String captchaCode) {
        DefaultRspDTO<Boolean> rspDTO = systemCaptchaServiceClient.checkCaptcha(captchaReqId, captchaCode);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(rspDTO.getMsgCd(),rspDTO.getMsgInfo());
        }
        return rspDTO.getBody();
    }
}
