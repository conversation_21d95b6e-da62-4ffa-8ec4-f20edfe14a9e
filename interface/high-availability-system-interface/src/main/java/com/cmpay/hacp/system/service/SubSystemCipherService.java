package com.cmpay.hacp.system.service;

public interface SubSystemCipherService {

    /**
     * 搭配
     * @param sm4Key
     * @param data
     * @return
     */
    String decryptDataBySm4AndSm2(String sm4Key, String data);

    /**
     * 子应用解密数据使用，子应用必须实现
     * @param data
     * @return
     */
    String subDecryptData(String data);

    String getSm4RandomSalt(String key, String captchaReqId);
}
