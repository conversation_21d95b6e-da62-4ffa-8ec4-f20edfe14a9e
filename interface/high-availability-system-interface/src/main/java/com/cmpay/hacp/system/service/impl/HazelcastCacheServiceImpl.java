package com.cmpay.hacp.system.service.impl;

import com.cmpay.hacp.system.service.SystemCacheService;
import com.hazelcast.collection.IList;
import com.hazelcast.collection.IQueue;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class HazelcastCacheServiceImpl implements SystemCacheService {

    private final HazelcastInstance hazelcastInstance;

    public HazelcastCacheServiceImpl(HazelcastInstance hazelcastInstance) {
        this.hazelcastInstance = hazelcastInstance;
    }


    @Override
    public Object getValue(String key) {
        return hazelcastInstance.getMap(key).get(key);
    }

    @Override
    public Object getHashMap(String key) {
        return this.getMap(key);
    }

    @Override
    public Object getHashMap(String key, String filed) {
        return this.getMap(key).get(filed);
    }

    @Override
    public boolean delete(String key) {
        this.getMap(key).delete(key);
        return !this.exists(key);
    }

    @Override
    public boolean setValue(String key, Object value, long time, TimeUnit timeUnit) {
        this.getMap(key).put(key, value, time, timeUnit);
        return this.exists(key);
    }

    @Override
    public boolean exists(String key) {
        return this.getMap(key).containsKey(key);
    }

    @Override
    public boolean setIfAbsent(String key, Object value, long time, TimeUnit timeUnit) {
        IMap<Object, Object> lockMap = this.getMap(key);
        return lockMap.putIfAbsent(key, value, time, timeUnit) == null;
    }

    @Override
    public boolean setHashMap(String key, Map map, long time, TimeUnit timeUnit) {
        this.getMap(key).putAll(map);
        return this.exists(key);
    }

    @Override
    public boolean offerQueue(String key,  String value) {
        IQueue<Object> queue = hazelcastInstance.getQueue(key);
        return queue.offer(value);
    }

    @Override
    public IList<String> getList(String key) {
        return hazelcastInstance.getList(key);
    }

    @Override
    public IQueue<String> getQueue(String key) {
        return hazelcastInstance.getQueue(key);
    }

    @Override
    public int drainToQueue(String key, List<String> list, int maxSize) {
        IQueue<String> queue = hazelcastInstance.getQueue(key);
        return queue.drainTo(list, maxSize);
    }

    @Override
    public IMap<Object, Object> getMap(String key) {
        return hazelcastInstance.getMap(key);
    }

}