package com.cmpay.hacp.system.log.service;

import com.cmpay.hacp.system.log.bo.StaticDataSizeBO;
import com.cmpay.hacp.system.log.bo.SysStaticLogBO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemStaticLogService {

    String STATIC_RSP_DATA_SIZE = "STATIC_RSP_DATA_SIZE";

    /**
     * 登记日志信息
     *
     * @param logBO
     */
    void add(SysStaticLogBO logBO);


    /**
     * 查询日志
     *
     * @param pageNum
     * @param pageSize
     * @param logBO
     * @return
     */
    PageInfo<SysStaticLogBO> list(int pageNum, int pageSize, SysStaticLogBO logBO);

    /**
     * 查询日志详情
     *
     * @param id
     * @return
     */
    SysStaticLogBO info(String id);

    /**
     * 批量删除日志
     *
     * @param ids
     */
    void delete(List<String> ids);


    /**
     * 设置响应数据大小
     *
     * @param rspDataSize     数据大小
     * @param rspDataSizeType 数据大小类型 可自定义 比如字节大小 bytes, 或者行数 line
     */
    void setStaticRspDataSize(long rspDataSize, String rspDataSizeType);

    /**
     * 获取响应数据大小
     *
     * @return
     */
    StaticDataSizeBO getStaticRspDataSize();

}
