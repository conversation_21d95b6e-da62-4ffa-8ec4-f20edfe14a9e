package com.cmpay.hacp.system.log.aspect;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.bo.SysDynamicLogBO;
import com.cmpay.hacp.system.log.firefly.entity.FireflyDynamicAccessLog;
import com.cmpay.hacp.system.log.firefly.enums.AccessLogTypeEnums;
import com.cmpay.hacp.system.log.firefly.enums.FireflyLogDataTypeEnum;
import com.cmpay.hacp.system.log.firefly.enums.FireflyLogTypeEnum;
import com.cmpay.hacp.system.log.firefly.logger.FireflyDynamicAccessLogger;
import com.cmpay.hacp.system.log.firefly.logger.FireflyLogger;
import com.cmpay.hacp.system.log.service.SystemDynamicLogService;
import com.cmpay.hacp.system.properties.HacpWebAdminLogProperties;
import com.cmpay.lemon.common.context.LemonContext;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.ReflectionUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.util.UriComponentsBuilder;

import java.lang.reflect.Field;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;

import static com.cmpay.hacp.constant.CommonConstant.DYNAMIC_ACCESS_LOG;


/**
 * <AUTHOR>
 * @create 2024/03/15 11:28
 * @since 1.0.0
 */
@Slf4j
@Aspect
public class DynamicHttpLogAspect {

    private final SystemDynamicLogService systemDynamicLogService;

    private final ObjectMapper resultObjectMapper;

    private final ObjectMapper paramsObjectMapper;

    private final FireflyLogger fireflyLogger;

    private final HacpWebAdminLogProperties.DynamicAccess dynamicAccess;


    public DynamicHttpLogAspect(SystemDynamicLogService systemDynamicLogService,
                                ObjectMapper resultObjectMapper,
            HacpWebAdminLogProperties lemonWebAdminLogProperties,
                                FireflyDynamicAccessLogger fireflyLogger) {
        this.systemDynamicLogService = systemDynamicLogService;
        this.resultObjectMapper = resultObjectMapper;
        this.paramsObjectMapper = resultObjectMapper;
        this.paramsObjectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        this.dynamicAccess = lemonWebAdminLogProperties.getDynamicAccess();
        this.fireflyLogger = fireflyLogger;
    }

    @Pointcut("@annotation(com.cmpay.hacp.system.log.annotation.LogNoneRecord)")
    public void logNoneRecordMethod() {

    }

    /**
     *
     */
    @Pointcut("@within(org.springframework.cloud.openfeign.FeignClient)")
    public void feignClientPointcut() {

    }

    /**
     *
     */
    @Pointcut(value = "execution (* org.springframework.web.client.RestTemplate.exchange(..))")
    public void restTemplatePointcut() {

    }

    @Around(value = "feignClientPointcut()&&!logNoneRecordMethod()")
    public Object feignClientAround(ProceedingJoinPoint pjp) throws Throwable {
        return doAround(pjp);
    }

    @Around(value = "restTemplatePointcut()")
    public Object restTemplateAround(ProceedingJoinPoint pjp) throws Throwable {
        return doAround(pjp);
    }

    private Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        Object responseObject = null;
        Object[] args = pjp.getArgs();
        SysDynamicLogBO sysDynamicLogBO = null;
        try {
            responseObject = pjp.proceed();
            sysDynamicLogBO = this.afterProceed(responseObject, getSysDynamicLog(), args);
        } catch (Throwable throwable) {
            sysDynamicLogBO = this.throwableProceed(getSysDynamicLog(), throwable);
            throw throwable;
        } finally {
            this.finallyProceed(sysDynamicLogBO, responseObject);
        }
        return responseObject;
    }


    private SysDynamicLogBO afterProceed(Object responseObject, SysDynamicLogBO sysDynamicLogBO, Object[] objs) {
        if (JudgeUtils.isNull(sysDynamicLogBO)) {
            return null;
        }
        sysDynamicLogBO.setOperatorStatus(getOperatorStatus(responseObject));
        try {
            if (JudgeUtils.equalsIgnoreCase(sysDynamicLogBO.getType(), AccessLogTypeEnums.DYNAMIC_ACCESS_FEIGN_CLIENT.getCode())) {
                sysDynamicLogBO.setParams(this.paramsObjectMapper.writeValueAsString(objs));
            } else {
                for (Object obj : objs) {
                    if (obj instanceof String) {
                        URI uri = new URI((String) obj);
                        sysDynamicLogBO.setExecutionTarget(uri.getAuthority());
                        sysDynamicLogBO.setInterfaceRecord(uri.getPath());
                        if (JudgeUtils.isBlank(sysDynamicLogBO.getParams())) {
                            sysDynamicLogBO.setParams(getParams(uri));
                        }
                    } else if (obj instanceof HttpMethod) {
                        HttpMethod httpMethod = (HttpMethod) obj;
                        sysDynamicLogBO.setOperationAction(httpMethod.name());
                    } else if (obj instanceof HttpEntity) {
                        HttpEntity requestEntity = (HttpEntity) obj;
                        if (JudgeUtils.isBlank(sysDynamicLogBO.getParams())) {
                            sysDynamicLogBO.setParams(getParams(requestEntity));
                        }
                    }

                }
            }
        } catch (Exception exception) {
            log.warn("beforeProceed Exception {}", exception.getMessage());
        }
        return sysDynamicLogBO;
    }

    private String getParams(URI uri) {
        String params = null;
        try {
            Map<String, List<String>> queryParams = UriComponentsBuilder.fromUri(uri).build().getQueryParams();
            if (JudgeUtils.isNotEmpty(queryParams)) {
                params = this.paramsObjectMapper.writeValueAsString(queryParams);
            }
        } catch (Exception exception) {
            log.warn("getParams Exception {}", exception.getMessage());
        }
        return params;
    }

    private String getParams(HttpEntity httpEntity) {
        String params = null;
        try {
            if (JudgeUtils.isNotNull(httpEntity.getBody())) {
                params = this.paramsObjectMapper.writeValueAsString(httpEntity.getBody());
            }
        } catch (Exception exception) {
            log.warn("getParams Exception {}", exception.getMessage());
        }
        return params;
    }

    private String getOperatorStatus(Object responseObject) {
        String operatorStatus = dynamicAccess.getDefaultOperatorStatus();
        if (JudgeUtils.isNull(responseObject)) {
            return operatorStatus;
        }
        if (responseObject instanceof ResponseEntity) {
            ResponseEntity responseEntity = (ResponseEntity) responseObject;
            if (JudgeUtils.isNotNull(responseEntity.getBody())) {
                operatorStatus = getMessageCode(responseEntity.getBody(), operatorStatus);
            }
            if (JudgeUtils.isBlank(operatorStatus)) {
                operatorStatus = String.valueOf(responseEntity.getStatusCodeValue());
            }
        } else {
            operatorStatus = getMessageCode(responseObject, operatorStatus);
        }
        return operatorStatus;
    }

    private String getMessageCode(Object responseObject, String messageCode) {
        Class<?> clazz = responseObject.getClass();
        try {
            Field declaredField = clazz.getDeclaredField(this.dynamicAccess.getMessageCode());
            ReflectionUtils.makeAccessible(declaredField);
            messageCode = (String) declaredField.get(responseObject);
        } catch (Exception exception) {
            log.warn("getMessageCode Exception {}", exception.getMessage());
        }
        return messageCode;
    }

    private void finallyProceed(SysDynamicLogBO sysDynamicLogBO, Object responseObject) {
        if (JudgeUtils.isNull(sysDynamicLogBO)) {
            log.warn("finallyProceed SysDynamicLogBO is Null");
            return;
        }
        this.getDynamicRspDataSize(responseObject, sysDynamicLogBO);
        sysDynamicLogBO.setEndTime(DateTimeUtils.getCurrentLocalDateTime());
        sysDynamicLogBO.setDuration(ChronoUnit.MILLIS.between(sysDynamicLogBO.getOperatorTime(), sysDynamicLogBO.getEndTime()));
        if (JudgeUtils.isNotBlank(sysDynamicLogBO.getOperatorIp())) {
            fireflyLogger.println(convertFireflyDynamicAccessLog(sysDynamicLogBO));
            systemDynamicLogService.addDynamicLog(sysDynamicLogBO);
        }
        LemonContext.getCurrentContext().remove(DYNAMIC_ACCESS_LOG);
    }

    private void getDynamicRspDataSize(Object responseObject, SysDynamicLogBO sysDynamicLogBO) {
        try {
            sysDynamicLogBO.setDataSizeType(FireflyLogDataTypeEnum.bytes.name());
            sysDynamicLogBO.setDataSize((long) this.resultObjectMapper.writeValueAsString(responseObject).getBytes(StandardCharsets.UTF_8).length);
        } catch (Exception exception) {
            log.warn("getDataSize Exception {}", exception.getMessage());
        }
    }


    private FireflyDynamicAccessLog convertFireflyDynamicAccessLog(SysDynamicLogBO sysDynamicLogBO) {
        FireflyDynamicAccessLog fireflyDynamicAccessLog = new FireflyDynamicAccessLog();
        BeanUtils.copyProperties(fireflyDynamicAccessLog, sysDynamicLogBO);
        fireflyDynamicAccessLog.setLogType(FireflyLogTypeEnum.dynamicLog.name());
        fireflyDynamicAccessLog.setRequestId(sysDynamicLogBO.getLogId());
        return fireflyDynamicAccessLog;
    }

    private SysDynamicLogBO throwableProceed(SysDynamicLogBO sysDynamicLogBO, Throwable throwable) {
        if (JudgeUtils.isNull(sysDynamicLogBO)) {
            return null;
        }
        if (throwable instanceof BusinessException) {
            BusinessException businessException = (BusinessException) throwable;
            sysDynamicLogBO.setOperatorStatus(businessException.getMsgCd());
            sysDynamicLogBO.setType(AccessLogTypeEnums.BUSINESS_EXCEPTION.getCode());
        } else {
            //系统异常日志
            sysDynamicLogBO.setType(AccessLogTypeEnums.TYPE_EXCEPTION.getCode());
            sysDynamicLogBO.setOperatorStatus(MsgEnum.SYSTEM_EXCEPTION.getMsgCd());
            sysDynamicLogBO.setException(throwable.getMessage());
        }
        return sysDynamicLogBO;
    }

    public SysDynamicLogBO getSysDynamicLog() {
        return LemonContext.getCurrentContext().containsKey(DYNAMIC_ACCESS_LOG) ? (SysDynamicLogBO) LemonContext.getCurrentContext().get(DYNAMIC_ACCESS_LOG) : null;
    }
}
