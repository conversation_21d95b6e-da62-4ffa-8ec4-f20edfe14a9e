package com.cmpay.hacp.system.service.adapter;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.system.bo.menu.MenuActionMetaBO;
import com.cmpay.hacp.system.bo.menu.MenuTreeMetaBO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.system.service.SystemPermissionService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.cache.jcache.JCacheCacheable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public abstract class SystemPermissionServiceAdapter implements SystemPermissionService {


    /**
     * 获取用户权限
     *
     * @param userId
     * @return
     */
    @Override
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_PERMISSION",
            key = "'PERMISSION:' + #applicationName+':'+#userId",
            unless = "#result == null")
    public List<String> getUserPermissions(String userId, String applicationName) {
        PermMenuTreeMetaBO permMenuTreeMetaBO = this.queryUserPermissions(userId, applicationName);
        List<MenuTreeMetaBO> menus = permMenuTreeMetaBO.getMenuTreeList();
        List<String> permissions = new ArrayList();
        if (JudgeUtils.isNotEmpty(menus)) {
            permissions = this.getPermissionsByMenu(menus, permissions);
        }
        List<MenuActionMetaBO> menuActions = permMenuTreeMetaBO.getMenuActionList();
        if (JudgeUtils.isNotEmpty(menuActions)) {
            permissions = this.getPermissionsByAction(menuActions, permissions);
        }
        return permissions;
    }

    /**
     * @param userMenus
     * @return
     */
    @Override
    public List<String> getPermissionsByMenu(List<MenuTreeMetaBO> userMenus, List<String> permissions) {
        for (MenuTreeMetaBO metaDTO : userMenus) {
            if (JudgeUtils.isNotBlank(metaDTO.getPerms())) {
                String[] perms = metaDTO.getPerms().split(",");
                for (int i = 0; i < perms.length; i++) {
                    permissions.add(perms[i]);
                }
            }
            if (JudgeUtils.isNotEmpty(metaDTO.getChildren())) {
                return this.getPermissionsByMenu(metaDTO.getChildren(), permissions);
            }
        }
        return permissions;
    }

    /**
     * @param userMenus
     * @return
     */
    @Override
    public List<String> getPermissionsByAction(List<MenuActionMetaBO> userMenus, List<String> permissions) {
        for (MenuActionMetaBO metaDTO : userMenus) {
            if (JudgeUtils.isNotBlank(metaDTO.getPerms())) {
                String[] perms = metaDTO.getPerms().split(CommonConstant.COMMA);
                permissions.addAll(Arrays.asList(perms));
            }
        }
        return permissions;
    }
}
