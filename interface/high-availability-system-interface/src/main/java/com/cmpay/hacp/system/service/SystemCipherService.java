package com.cmpay.hacp.system.service;


import com.cmpay.lemon.framework.cache.jcache.JCacheCacheable;

/**
 * 秘钥管理
 *
 * <AUTHOR>
 */
public interface SystemCipherService {

    /**
     * 获取sm2公钥
     *
     * @param applicationName
     * @return
     */
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_CIPHER", key = "'SM2:PUBLICKEY:'+#applicationName")
    String getSm2PublicKey(String applicationName);

    /**
     * 获取sm2私钥
     *
     * @param applicationName
     * @return
     */
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_CIPHER", key = "'SM2:PRIVATEKEY:'+#applicationName")
    String getSm2PrivateKey(String applicationName);

    /**
     * 获取sm4秘钥
     *
     * @param applicationName
     * @return
     */
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_CIPHER", key = "'SM4:KEY:'+#applicationName")
    String getSm4Key(String applicationName);

    /**
     * 获取rsa公钥
     *
     * @param applicationName
     * @return
     */
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_CIPHER", key = "'RSA:PUBLICKEY:'+#applicationName")
    String getRsaPublicKey(String applicationName);

    /**
     * 获取rsa私钥
     *
     * @param applicationName
     * @return
     */
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_CIPHER", key = "'RSA:PRIVATEKEY:'+#applicationName")
    String getRsaPrivateKey(String applicationName);

    /**
     * sm2解密
     *
     * @param encryptData
     * @return
     */
    String sm2Decrypt(String encryptData);

    /**
     * sm4解密
     *
     * @param encryptData
     * @return
     */
    String sm4Decrypt(String encryptData);

    /**
     * rsa解密
     *
     * @param encryptData
     * @return
     */
    String rsaDecrypt(String encryptData);
    String setSm4RandomSalt(String username, String captchaReqId);

    String getSm4RandomSalt(String key, String captchaReqId);

    String otherModuleEncryptAndDecryptData(String key, String data);

    String subDecryptData(String key, String data);

    String otherModuleEncryptAndDecryptData(String data);
    String otherModuleDecryptData(String data);

    /**
     * 其他应用请求管理应用时，敏感数据加密
     * @param dictEncryptKey 存储到字典中sm2密钥的key，各应用唯一
     * @param data 需要加密的数据
     * @return
     */
    String subEncryptData(String dictEncryptKey, String data);
}
