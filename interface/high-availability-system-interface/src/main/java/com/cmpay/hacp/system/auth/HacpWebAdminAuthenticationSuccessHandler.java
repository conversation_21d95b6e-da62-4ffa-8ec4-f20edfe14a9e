package com.cmpay.hacp.system.auth;

import com.cmpay.hacp.system.bo.system.LoginHistoryLogBO;
import com.cmpay.hacp.bo.system.SessionTokenVO;
import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.lemon.common.exception.ErrorMsgCode;
import com.cmpay.lemon.common.exception.LemonException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.InternalDataHelper;
import com.cmpay.lemon.framework.response.ResponseMessageResolver;
import com.cmpay.lemon.framework.security.AbstractAuthenticationSuccessHandler;
import com.cmpay.lemon.framework.security.RefreshTokenService;
import com.cmpay.lemon.framework.security.callback.AuthenticationSuccessPostProcessor;
import com.cmpay.lemon.framework.utils.WebUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class HacpWebAdminAuthenticationSuccessHandler extends AbstractAuthenticationSuccessHandler {

    public static final String LOGIN_INFO = "LOGIN_INFO";

    private static final Logger logger = LoggerFactory.getLogger(HacpWebAdminAuthenticationSuccessHandler.class);

    private final RefreshTokenService refreshTokenService;

    private final ObjectMapper lemonWebAdminObjectMapper;

    public HacpWebAdminAuthenticationSuccessHandler(ResponseMessageResolver responseMessageResolver,
            RefreshTokenService refreshTokenService, InternalDataHelper internalDataHelper, ObjectMapper lemonWebAdminObjectMapper) {
        this(responseMessageResolver, refreshTokenService, internalDataHelper, null, lemonWebAdminObjectMapper);

    }

    public HacpWebAdminAuthenticationSuccessHandler(ResponseMessageResolver responseMessageResolver,
            RefreshTokenService refreshTokenService, InternalDataHelper internalDataHelper,
            AuthenticationSuccessPostProcessor authenticationSuccessPostProcessor, ObjectMapper lemonWebAdminObjectMapper) {
        super(responseMessageResolver, internalDataHelper, authenticationSuccessPostProcessor);
        this.refreshTokenService = refreshTokenService;
        this.lemonWebAdminObjectMapper = lemonWebAdminObjectMapper;
    }

    @Override
    protected DefaultRspDTO<LoginUser> doCreateResponseDTO(HttpServletRequest request,
            Authentication authentication) {
        LoginUser loginUser = new LoginUser();
        HttpSession httpSession = WebUtils.getHttpServletRequest().getSession();
        //从session中获取信息
        String loginInfo = (String) httpSession.getAttribute(LOGIN_INFO);
        try {
            UserLoginBO userLoginRspDTO = this.lemonWebAdminObjectMapper.readValue(loginInfo, UserLoginBO.class);
            BeanUtils.copyProperties(loginUser, userLoginRspDTO);
            loginUser.setUserInfo(userLoginRspDTO.getUserInfo());
            loginUser.setLoginHistory(userLoginRspDTO.getLoginHistory());
        } catch (IOException e) {
            logger.error("doCreateResponseDTO err {}", e.getMessage());
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, e);
        }
        if (!this.refreshTokenService.isDummy()) {
            loginUser.setRefreshToken(this.refreshTokenService.resolveRefreshToken(request.getSession()));
        }
        return DefaultRspDTO.newSuccessInstance(loginUser);
    }

    @Getter
    @Setter
    @ToString
    public static class LoginUser extends LoginUserBase {

        private String userId;

        private String loginName;

        private String mblNo;

        private String refreshToken;

        private SessionTokenVO userInfo;

        private LoginHistoryLogBO loginHistory;

    }
}

