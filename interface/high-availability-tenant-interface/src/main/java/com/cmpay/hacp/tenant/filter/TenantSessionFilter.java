package com.cmpay.hacp.tenant.filter;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.tenant.service.TenantWorkspaceUserService;
import com.cmpay.hacp.tenant.context.TenantContextHolder;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.lemon.framework.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.util.AntPathMatcher;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.Optional;

import static com.cmpay.hacp.constant.TenantConstant.WORKSPACE_ID;

/**
 * 搭配{@link TenantSessionFilterProperties}注册到{@link FilterRegistrationBean}中，<h2 color="#FF0000">拦截指定的url</h2>
 * <AUTHOR>
 * 租户session权限校验
 */
@Slf4j
public class TenantSessionFilter implements Filter {

    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();
    private final TenantWorkspaceUserService tenantWorkspaceUserService;
    private String[] permitUrls;
    private String[] defaultPermitUrls = {"/actuator", "/actuator/info", "/actuator/health", "/actuator/status", "/actuator/status", "/actuator/prometheus", "/actuator/**","/v1/tenant/workspace/initialization"};

    public TenantSessionFilter(TenantWorkspaceUserService tenantWorkspaceUserService,
            TenantSessionFilterProperties tenantSessionFilterProperties) {
        this.tenantWorkspaceUserService = tenantWorkspaceUserService;
        this.permitUrls = Optional.ofNullable(tenantSessionFilterProperties.getPermitUrls())
                .orElse(new String[0]);
        log.info("TenantSessionFilter InitParameter: permitUrls {}", Arrays.toString(this.permitUrls));
    }

    private static String getDisallowedResponseMessage(String msgCd, String msgInfo, String workspaceId, String userId) {
        StringBuilder msg = new StringBuilder("{");
        msg.append("\"msgCd\"").append(":\"").append(msgCd).append("\",")
                .append("\"msgInfo\"").append(":\"").append(msgInfo).append("\",")
                .append("\"requestId\"").append(":\"").append(LemonUtils.getRequestId()).append("\",")
                .append("\"workspaceId\"").append(":\"").append(workspaceId).append("\",")
                .append("\"userId\"").append(":\"").append(userId)
                .append("\"}");
        return msg.toString();
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        //白名单放行
        HttpServletRequest servletRequest = (HttpServletRequest) request;
        if (isAllowedUrl(servletRequest)) {
            chain.doFilter(request, response);
            return;
        }

        String loginUserId = SecurityUtils.getLoginUserId();
        String workspaceId = WebUtils.getHttpServletRequest().getHeader(WORKSPACE_ID);
        if (JudgeUtils.isBlank(workspaceId) || JudgeUtils.equalsAny(workspaceId, "null", "undefined")) {
            response.getWriter()
                    .write(this.getDisallowedResponseMessage(MsgEnum.QUERY_WORKPLACE_EXPIRE.getMsgCd(),
                            MsgEnum.QUERY_WORKPLACE_EXPIRE.getMsgInfo(),
                            workspaceId,
                            loginUserId));
            log.error("workspaceId it cant be null");
            return;
        }
        //如果异常，抛出异常
        try {
            this.tenantWorkspaceUserService.existWorkspaceUser(workspaceId, loginUserId);
            TenantUtils.setWorkspaceId(workspaceId);
        } catch (Exception exception) {
            String msgCd = MsgEnum.USER_IS_NOT_EXIST_WORKPLACE.getMsgCd();
            String msgInfo = MsgEnum.USER_IS_NOT_EXIST_WORKPLACE.getMsgInfo();
            if (exception instanceof BusinessException) {
                BusinessException businessException = (BusinessException) exception;
                msgCd = businessException.getMsgCd();
                msgInfo = businessException.getMsgInfo();
            }
            if (JudgeUtils.equalsIgnoreCase(msgInfo, "null") || JudgeUtils.isBlank(msgInfo)) {
                msgInfo = MsgEnum.USER_IS_NOT_EXIST_WORKPLACE.getMsgInfo();
            }
            response.getWriter().write(this.getDisallowedResponseMessage(msgCd, msgInfo, workspaceId, loginUserId));
            return;
        }
        //放行
        chain.doFilter(request, response);
        TenantContextHolder.clearContext();
    }

    private boolean isAllowedUrl(HttpServletRequest request) {
        String apiPermitUrls[] = {
                "/upms/v1/captcha/getCaptcha", "/v1/sys/captcha/getCaptcha", "/v1/sys/cipher/rsa/publickey", "/swagger-ui.html", "/webjars/**", "/swagger-resources", "/swagger-resources/**", "/v2/api-docs", "/druid/**", "/doc.html"
        };
        for (int i = 0, len = apiPermitUrls.length; i < len; i++) {
            if (PATH_MATCHER.match(apiPermitUrls[i], request.getRequestURI())) {
                return true;
            }
        }
        for (int i = 0, len = this.defaultPermitUrls.length; i < len; i++) {
            if (PATH_MATCHER.match(this.defaultPermitUrls[i], request.getRequestURI())) {
                return true;
            }
        }
        for (int i = 0, len = this.permitUrls.length; i < len; i++) {
            if (PATH_MATCHER.match(this.permitUrls[i], request.getRequestURI())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
