package com.cmpay.hacp.tenant.aspect;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.tenant.annotation.TenantBeforeProcess;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;

/**
 * <AUTHOR>
 */
@Aspect
@Slf4j
public class TenantBeforeProcessAspect {

    public TenantBeforeProcessAspect() {
    }

    /**
     * 租户前置处理切点
     */
    @Pointcut("@annotation(com.cmpay.hacp.tenant.annotation.TenantBeforeProcess)")
    public void tenantBeforeProcess() {

    }

    /**
     * 租户前置处理切面
     *
     * @param joinPoint
     * @return
     */
    @Around("tenantBeforeProcess()")
    public Object tenantBeforeProcessPoint(ProceedingJoinPoint joinPoint) {
        Object proceed = null;
        try {
            //方法执行前
            doTenantBeforeProcess(joinPoint);
            proceed = joinPoint.proceed();
        } catch (Throwable throwable) {
            if (throwable instanceof BusinessException) {
                BusinessException.throwBusinessException(((BusinessException) throwable).getMsgCd());
            } else {
                log.error("tenantBeforeProcessPoint exception {}", throwable.getMessage());
                BusinessException.throwBusinessException(MsgEnum.NOT_OPERATION_PERMISSION);
            }
        }
        return proceed;
    }

    /**
     * 租户前置处理业务逻辑
     */
    private void doTenantBeforeProcess(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        TenantBeforeProcess tenantBeforeProcess = methodSignature.getMethod().getAnnotation(TenantBeforeProcess.class);
        //参数
        Object[] args = joinPoint.getArgs();
        //参数名称
        String[] names = methodSignature.getParameterNames();
        //配置的参数名称
        String applicationParamName = tenantBeforeProcess.applicationParamName();
        if (JudgeUtils.isEmpty(args) || JudgeUtils.isEmpty(names) || JudgeUtils.isBlank(applicationParamName)) {
            BusinessException.throwBusinessException(MsgEnum.NOT_OPERATION_PERMISSION);
        }
        String application = null;
        for (int i = 0; i < names.length - 1; i++) {
            if (JudgeUtils.equalsIgnoreCase(applicationParamName, names[i])) {
                application = (String) args[i];
                break;
            }
        }
        String workspaceId = WebUtils.getHttpServletRequest().getHeader(tenantBeforeProcess.workspaceIdHeaderName());
        if (JudgeUtils.isBlankAny(workspaceId, application)) {
            BusinessException.throwBusinessException(MsgEnum.NOT_OPERATION_PERMISSION);
        }
    }
}
