package com.cmpay.hacp.tenant.service;


import com.cmpay.hacp.tenant.bo.TenantWorkspaceBO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 租户项目管理
 *
 * <AUTHOR>
 */
public interface TenantWorkspaceService {


    /**
     * 新增租户与项目绑定的关联关系
     *
     * @param loginName     操作用户账号
     * @param localDateTime 操作时间
     * @param tenantId      租户ID
     * @param workspaceId   项目ID
     */
    void addTenantWorkspace(String loginName, LocalDateTime localDateTime, String tenantId, String workspaceId);

    /**
     * 修改租户与项目绑定的关联关系
     *
     * @param loginName     操作用户
     * @param localDateTime 操作时间
     * @param id            主键
     * @param tenantId      租户ID
     * @param workspaceId   项目ID
     */
    void updateTenantWorkspace(String loginName, LocalDateTime localDateTime, String id, String tenantId, String workspaceId);

    /**
     * 删除租户下所有项目关联关系
     *
     * @param tenantId 租户ID
     */
    void deleteTenantWorkspaceByTenantId(String tenantId);

    /**
     * 查询租户所有项目ID列表
     *
     * @param tenantId 租户ID
     * @return 项目ID列表
     */
    List<String> getWorkspaceIdsByTenantId(String tenantId);

    /**
     * 查询项目列表
     *
     * @param tenantId 租户ID
     * @return 项目列表
     */
    List<TenantWorkspaceBO> getWorkspacesByTenantId(String tenantId);


    /**
     * 查询项目所属租户
     *
     * @param workspaceId 项目ID
     * @return 项目所属租户
     */
    TenantWorkspaceBO getWorkspaceInfo(String workspaceId);
}
