package com.cmpay.hacp.tenant.adapter;

import com.cmpay.hacp.tenant.bo.TenantWorkspaceUserBO;
import com.cmpay.hacp.tenant.service.TenantWorkspaceUserService;
import com.github.pagehelper.PageInfo;

import java.time.LocalDateTime;
import java.util.List;

public abstract class TenantWorkspaceUserAdapter implements TenantWorkspaceUserService {

    @Override
    public void updateAdminToAnyoneWorkspaceUserByWorkspaceId(String loginName, LocalDateTime localDateTime, String workspaceId) {

    }

    @Override
    public void updateAdminWorkspaceUserByWorkspaceId(String loginName, LocalDateTime localDateTime, String workspaceId, String userId) {

    }

    @Override
    public void deleteWorkspaceUserByWorkspaceId(String workspaceId) {

    }

    @Override
    public void addWorkspaceUser(String loginName, TenantWorkspaceUserBO tenantWorkspaceUser) {

    }

    @Override
    public void deleteWorkspaceUserRoleByWorkspaceId(String workspaceId, String userId) {

    }

    @Override
    public void addWorkspaceUserRole(String loginName, LocalDateTime localDateTime, TenantWorkspaceUserBO tenantWorkspaceUser) {

    }

    @Override
    public void updateWorkspaceUser(String loginName, TenantWorkspaceUserBO tenantWorkspaceUser) {

    }

    @Override
    public void deletesWorkspaceUser(List<String> ids) {

    }

    @Override
    public void deleteWorkspaceUser(String id) {

    }

    @Override
    public void deleteWorkspaceUser(String workspaceId, String userId) {

    }

    @Override
    public TenantWorkspaceUserBO getWorkspaceUserInfo(String id) {
        return null;
    }

    @Override
    public TenantWorkspaceUserBO getTenantWorkspaceUserInfo(String workspaceId, String userId) {
        return null;
    }

    @Override
    public void existWorkspaceUser(String workspaceId, String userId) {

    }

    @Override
    public List<TenantWorkspaceUserBO> getWorkspaceUsers(TenantWorkspaceUserBO tenantWorkspaceUser) {
        return null;
    }

    @Override
    public List<String> getWorkspaceUserIds(String workspaceId) {
        return null;
    }

    @Override
    public PageInfo<TenantWorkspaceUserBO> getWorkspaceUsersByPage(int pageNum, int pageSize, TenantWorkspaceUserBO tenantWorkspaceUser) {
        return null;
    }
}
