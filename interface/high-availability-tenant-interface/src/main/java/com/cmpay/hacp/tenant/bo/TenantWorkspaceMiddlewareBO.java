package com.cmpay.hacp.tenant.bo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TenantWorkspaceMiddlewareBO {
    /**
     * @Fields id 编号
     */
    private String id;
    /**
     * @Fields url 注册中心地址
     */
    private String url;
    /**
     * @Fields region 归属区域
     */
    private String region;
    /**
     * @Fields zone 归属机房
     */
    private String zone;
    /**
     * @Fields cluster 所属集群
     */
    private String cluster;
    /**
     * @Fields type 注册中心类型[eureka]
     */
    private String type;
    /**
     * @Fields configServerName 配置中心服务名
     */
    private String configServerName;
    /**
     * @Fields configUserName 配置中心用户名
     */
    private String configUserName;
    /**
     * @Fields configPassword 配置中心密码
     */
    private String configPassword;
    /**
     * @Fields profile 运行环境
     */
    private String profile;
    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

}
