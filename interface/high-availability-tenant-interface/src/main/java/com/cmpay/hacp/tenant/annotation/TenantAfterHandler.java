package com.cmpay.hacp.tenant.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 租户前置处理注解,作用于controller上面
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TenantAfterHandler {

    /**
     * 项目ID,请求头标识
     *
     * @return {@link String}
     */
    String workspaceIdHeaderName() default "workspaceId";

    /**
     * 返回对象过滤标识
     *
     * @return {@link String}
     */
    String filterField();

}
