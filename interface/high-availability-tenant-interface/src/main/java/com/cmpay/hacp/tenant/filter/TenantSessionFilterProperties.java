package com.cmpay.hacp.tenant.filter;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "hacp.management.tenant-session-filter")
public class TenantSessionFilterProperties {

    private String[] urlPatterns = {"/*"};

    private String[] permitUrls;

    private boolean enabled = true;

    private int order = Integer.MAX_VALUE;
}
