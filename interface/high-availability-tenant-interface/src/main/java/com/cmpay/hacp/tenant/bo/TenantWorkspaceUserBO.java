
package com.cmpay.hacp.tenant.bo;


import com.cmpay.hacp.annotation.mybatis.CryptField;
import com.cmpay.hacp.annotation.mybatis.CryptType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TenantWorkspaceUserBO {
    /**
     * @Fields id 主键ID
     */
    private String id;
    /**
     * @Fields tenantId 项目ID
     */
    private String tenantId;

    /**
     * @Fields tenantName 租户名称
     */
    private String tenantName;
    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;
    /**
     * @Fields workspaceName 项目名称
     */
    private String workspaceName;
    /**
     * @Fields userId 用户ID
     */
    private String userId;
    /**
     * @Fields workspaceUserType 项目成员类型(1-项目管理员，0-普通用户)
     */
    private String workspaceUserType;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

    /**
     * @Fields userName 项目成员用户名
     */
    private String userName;
    /**
     * @Fields fullName 项目成员姓名
     */
    @CryptField(type = CryptType.SM4)
    private String fullName;
    /**
     * @Fields email 项目成员邮箱
     */
    @CryptField(type = CryptType.SM4)
    private String email;
    /**
     * @Fields mobile 项目成员手机
     */
    @CryptField(type = CryptType.SM4)
    private String mobile;
    /**
     * @Fields userStatus 项目成员用户状态
     */
    private String userStatus;
    /**
     * @Fields workspaceRoleIds 项目角色ID集合
     */
    private List<String> workspaceRoleIds;

    /**
     * @Fields workspaceRoles 项目角色集合
     */
    private List<TenantWorkspaceRoleBO> workspaceRoles;

    /**
     * @Fields workspaceRoleId 角色ID查询条件
     */
    private String workspaceRoleId;

}
