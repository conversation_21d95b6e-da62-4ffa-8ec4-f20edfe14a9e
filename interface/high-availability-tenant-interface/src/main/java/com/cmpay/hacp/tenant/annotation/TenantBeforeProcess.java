package com.cmpay.hacp.tenant.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 租户前置处理注解 作用于任何方法
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TenantBeforeProcess {


    /**
     * 项目ID,请求头标识
     *
     * @return
     */
    String workspaceIdHeaderName() default "workspaceId";

    /**
     * 应用字段参数名称
     *
     * @return
     */
    String applicationParamName() default "application";
}
