package com.cmpay.hacp.tenant.utils;

import com.cmpay.hacp.capable.OperatorCapable;
import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.lemon.framework.security.SecurityUtils;

import java.util.Optional;

public class TenantSecurityUtils {

    public static void copyTenantSecurity(TenantCapable capable){
        copyOperator(capable);
        copyWorkspace(capable);
    }

    public static void copyOperator(OperatorCapable capable){
        Optional.ofNullable(SecurityUtils.getLoginUserId()).ifPresent(operatorId -> capable.setOperatorId(operatorId));
        Optional.ofNullable(SecurityUtils.getLoginName()).ifPresent(operatorName -> capable.setOperatorName(operatorName));
    }

    public static void copyWorkspace(TenantCapable capable){
        Optional.ofNullable(TenantUtils.getWorkspaceId()).ifPresent(workspaceId -> capable.setWorkspaceId(workspaceId));
    }

}
