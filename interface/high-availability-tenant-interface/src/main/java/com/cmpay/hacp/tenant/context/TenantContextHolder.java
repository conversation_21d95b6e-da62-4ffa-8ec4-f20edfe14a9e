package com.cmpay.hacp.tenant.context;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/06/26 9:22
 * @since 1.0.0
 */

public class TenantContextHolder {

    private static final ThreadLocal<TenantContext> CONTEXT_HOLDER = new ThreadLocal<>();

    public static void setContext(TenantContext context) {
        CONTEXT_HOLDER.set(context);
    }

    public static TenantContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    public static void clearContext() {
        CONTEXT_HOLDER.remove();
    }

    @Data
    @Builder
    public static class TenantContext{
        private String tenantId;

        private String workspaceId;

        private Map<String,String> meta;
    }

}
