
package com.cmpay.hacp.tenant.bo;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TenantWorkspaceUserRoleBO {
    /**
     * @Fields id 主键ID
     */
    private String id;
    /**
     * @Fields workspaceRoleId 项目角色ID
     */
    private String workspaceRoleId;
    /**
     * @Fields userId 用户ID
     */
    private String userId;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

}
