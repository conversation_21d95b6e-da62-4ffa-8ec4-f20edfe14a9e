package com.cmpay.hacp.tenant.api;

import com.cmpay.hacp.api.VersionApi;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import org.springframework.web.bind.annotation.PostMapping;

public interface WorkspaceApi {

    String WORKSPACE_api = "/tenant/workspace";

    String WORKSPACE_INITIALIZATION_API = VersionApi.VERSION_V1 + WORKSPACE_api + "/initialization";

    /**
     * 初始化项目
     * @return
     */
    @PostMapping(WORKSPACE_INITIALIZATION_API)
    DefaultRspDTO<NoBody> initialization();
}
