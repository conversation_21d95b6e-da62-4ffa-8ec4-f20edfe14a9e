package com.cmpay.hacp.tenant.service;

public interface HacpBaseService {
    /**
     * 项目是否存在
     *
     * @param workspaceId 项目ID
     */
    void workspaceExist(String workspaceId);

    /**
     * 租户是否存在
     *
     * @param tenantId 租户ID
     */
    void tenantExist(String tenantId);

    /**
     * 用户是否存在
     *
     * @param userId 用户ID
     * @return
     */
    void userExist(String userId);
}
