package com.cmpay.hacp.tenant.service;

/**
 * 其他模块依赖项目需要同步删除时，需要实现该方法
 * <AUTHOR>
 * @create 2024/07/03 9:56
 * @since 1.0.0
 */

public interface WorkspaceBaseService {

    /**
     * 是否允许删除项目
     * <h3 color="#FF0000">允许删除必须返回true</h3>
     *
     * @param workspaceId 工作区 ID
     * @return boolean
     */
    boolean whetherOrNotToAllowItemsToBeDeleted(String workspaceId);

    /**
     * 初始化项目
     * <h3 color="#FF0000">初始化成功返回true</h3>
     *
     * @param workspaceId 工作区 ID
     * @return boolean
     */
    void initialization(String workspaceId);
}
