package com.cmpay.hacp.tenant.autoconfigure;


import com.cmpay.hacp.tenant.client.TenantWorkspaceUserServiceClient;
import com.cmpay.hacp.tenant.service.TenantWorkspaceUserService;
import com.cmpay.hacp.tenant.aspect.TenantAfterHandlerAspect;
import com.cmpay.hacp.tenant.aspect.TenantBeforeProcessAspect;
import com.cmpay.hacp.tenant.filter.TenantSessionFilter;
import com.cmpay.hacp.tenant.filter.TenantSessionFilterProperties;
import com.cmpay.hacp.tenant.service.impl.TenantWorkspaceUserServiceClientImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class HacpSystemManagementFilterAutoConfiguration {
    /**
     * 前置处理切面
     * 默认打开
     *
     * @return
     */
    @Bean
    @ConditionalOnClass({TenantBeforeProcessAspect.class})
    @ConditionalOnProperty(prefix = "hacp.management.tenant-before-process-aspect",
            name = "enabled",
            havingValue = "true",
            matchIfMissing = true)
    public TenantBeforeProcessAspect tenantBeforeProcessAspect() {
        return new TenantBeforeProcessAspect();
    }

    /**
     * 后置处理切面
     * 默认打开
     *
     * @return
     */
    @Bean
    @ConditionalOnClass({TenantAfterHandlerAspect.class})
    @ConditionalOnProperty(prefix = "hacp.management.tenant-after-handler-aspect",
            name = "enabled",
            havingValue = "true",
            matchIfMissing = true)
    public TenantAfterHandlerAspect tenantAfterHandlerAspect() {
        return new TenantAfterHandlerAspect();
    }

    /**
     * 租户session检查拦截器
     */
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass({TenantSessionFilter.class})
    @EnableConfigurationProperties(TenantSessionFilterProperties.class)
    @ConditionalOnProperty(prefix = "hacp.management.tenant-session-filter", name = "enabled", havingValue = "true", matchIfMissing = true)
    public static class FixedIpAccessFilterConfiguration {

        /**
         * 调用管理端用户与项目绑定关系查询
         *
         * @return
         */
        @Bean
        @ConditionalOnMissingBean
        @ConditionalOnClass({TenantWorkspaceUserServiceClientImpl.class})
        public TenantWorkspaceUserService tenantWorkspaceUserService(TenantWorkspaceUserServiceClient tenantWorkspaceUserServiceClient) {
            return new TenantWorkspaceUserServiceClientImpl(tenantWorkspaceUserServiceClient);
        }


        /**
         * 业务模块租户权限session检查拦截器
         * 默认打开
         *
         * @param tenantWorkspaceUserService
         * @return
         */
        @Bean(name = "tenantSessionFilter")
        public TenantSessionFilter tenantSessionFilter(TenantWorkspaceUserService tenantWorkspaceUserService,
                TenantSessionFilterProperties tenantSessionFilterProperties) {
            return new TenantSessionFilter(tenantWorkspaceUserService, tenantSessionFilterProperties);
        }


        /**
         * 租户过滤器
         *
         * @param tenantSessionFilterProperties
         * @param tenantSessionFilter
         * @return
         */
        @Bean(name = "tenantSessionFilterRegistration")
        public FilterRegistrationBean tenantSessionFilterRegistration(
                TenantSessionFilterProperties tenantSessionFilterProperties,
                @Qualifier("tenantSessionFilter") TenantSessionFilter tenantSessionFilter) {
            FilterRegistrationBean registration = new FilterRegistrationBean();
            registration.setFilter(tenantSessionFilter);
            registration.addUrlPatterns(tenantSessionFilterProperties.getUrlPatterns());

            registration.setName("tenantSessionFilter");
            registration.setOrder(tenantSessionFilterProperties.getOrder());
            return registration;
        }

    }
}
