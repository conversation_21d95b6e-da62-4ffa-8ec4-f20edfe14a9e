
package com.cmpay.hacp.tenant.bo;

import com.cmpay.hacp.annotation.mybatis.CryptField;
import com.cmpay.hacp.annotation.mybatis.CryptType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TenantWorkspaceBO {
    /**
     * @Fields id 租户项目关联表ID
     */
    private String id;
    /**
     * @Fields tenantId 租户ID
     */
    private String tenantId;
    /**
     * @Fields tenantName 租户名称
     */
    private String tenantName;
    /**
     * @Fields deptId 部门编号
     */
    private String deptId;
    /**
     * @Fields deptName 部门名称
     */
    private String deptName;
    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;
    /**
     * @Fields workspaceName 项目名称
     */
    private String workspaceName;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;
    /**
     * @Fields workspaceUserType 项目成员类型
     */
    private String workspaceUserType;
    /**
     * @Fields userId 项目管理员ID
     */
    private String userId;
    /**
     * @Fields userName 项目管理员用户名
     */
    private String userName;
    /**
     * @Fields fullName 项目管理员姓名
     */
    @CryptField(type = CryptType.SM4)
    private String fullName;
    /**
     * @Fields email 项目管理员邮箱
     */
    @CryptField(type = CryptType.SM4)
    private String email;
    /**
     * @Fields mobile 项目管理员手机
     */
    @CryptField(type = CryptType.SM4)
    private String mobile;

    /**
     * @Fields mobile 项目管理员用户状态
     */
    private String userStatus;

    /**
     * @Fields profile 所属环境
     */
    private String profile;

    /**
     * @Fields workspaceRoles 我在项目中拥有的角色
     */
    private List<TenantWorkspaceRoleBO> workspaceRoles;


}
