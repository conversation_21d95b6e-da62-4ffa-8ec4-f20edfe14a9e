package com.cmpay.hacp.tenant.bo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目角色BO
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
@Data
public class TenantWorkspaceRoleBO {

    /**
     * @Fields workspaceRoleId 项目角色ID
     */
    private String workspaceRoleId;
    /**
     * @Fields workspaceRoleName 项目角色名称
     */
    private String workspaceRoleName;
    /**
     * @Fields workspaceRoleType 项目角色类型
     */
    private String workspaceRoleType;
    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;
    /**
     * @Fields workspaceName 项目名称
     */
    private String workspaceName;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

    /**
     * @Fields menuIds 菜单ID集合
     */
    private List<Integer> menuIds;
}
