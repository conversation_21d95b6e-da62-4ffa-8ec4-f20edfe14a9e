package com.cmpay.hacp.tenant.client;

import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

@Validated
@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform}", url = "${hacp.management.discovery.url:}", contextId = "tenantWorkspaceUserServiceClient")
public interface TenantWorkspaceUserServiceClient {
    /**
     * 用戶是否是项目成员
     *
     * @param workspaceId 项目ID
     * @param userId      用户ID
     * @return 是、不是
     */
    @GetMapping(value = "/v1/tenant/workspace/user/exist")
    @LogNoneRecord
    DefaultRspDTO existWorkspaceUser(
            @ApiParam(name = "workspaceId", value = "项目ID", required = true, example = "3279035dc433428c84ff434379374157")
            @RequestParam(name = "workspaceId", required = true) @NotBlank(message = "SMD00004") String workspaceId,
            @ApiParam(name = "userId", value = "项目成员ID", required = true, example = "3279035dc433428c84ff434379374157")
            @RequestParam(name = "userId", required = true) @NotBlank(message = "SMD00022") String userId);
}
