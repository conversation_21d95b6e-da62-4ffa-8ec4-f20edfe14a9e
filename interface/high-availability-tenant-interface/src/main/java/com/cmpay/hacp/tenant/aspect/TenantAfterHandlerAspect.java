package com.cmpay.hacp.tenant.aspect;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.tenant.annotation.TenantAfterHandler;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Aspect
@Slf4j
public class TenantAfterHandlerAspect {
    public TenantAfterHandlerAspect() {
    }

    private static Object getDefaultRspDto(Object resultObject,
            String filterField) throws NoSuchFieldException, IllegalAccessException {
        DefaultRspDTO defaultRspDto = (DefaultRspDTO) resultObject;
        if (defaultRspDto.getBody() instanceof List) {
            List results = (List) defaultRspDto.getBody();
            if (JudgeUtils.isNotEmpty(results)) {
                Iterator resultIterator = results.iterator();
                while (resultIterator.hasNext()) {
                    Object result = resultIterator.next();
                    Field field = result.getClass().getDeclaredField(filterField);
                    ReflectionUtils.makeAccessible(field);
                    String value = (String) field.get(result);
                    if (JudgeUtils.isBlank(value)) {
                        resultIterator.remove();
                    }
                }
                defaultRspDto.setBody(results);
            }
        } else {
            Object result = defaultRspDto.getBody();
            Field field = result.getClass().getDeclaredField(filterField);
            ReflectionUtils.makeAccessible(field);
            String value = (String) field.get(result);
            if (JudgeUtils.isBlank(value)) {
                defaultRspDto.setBody(null);
            }
        }
        return defaultRspDto;
    }

    private static Object getResultObject(Object resultObject,
            String filterField) throws NoSuchFieldException, IllegalAccessException {
        if (resultObject instanceof List) {
            List results = (List) resultObject;
            Iterator resultIterator = results.iterator();
            while (resultIterator.hasNext()) {
                Object result = resultIterator.next();
                Field field = result.getClass().getDeclaredField(filterField);
                ReflectionUtils.makeAccessible(field);
                String value = (String) field.get(result);
                if (JudgeUtils.isBlank(value)) {
                    resultIterator.remove();
                }
            }
            return results;
        } else {
            Field field = resultObject.getClass().getDeclaredField(filterField);
            ReflectionUtils.makeAccessible(field);
            String value = (String) field.get(resultObject);
            if (JudgeUtils.isBlank(value)) {
                return null;
            }
        }
        return resultObject;
    }

    /**
     * 租户后置处理切点
     */
    @Pointcut("@annotation(com.cmpay.hacp.tenant.annotation.TenantAfterHandler)")
    public void tenantAfterHandler() {

    }

    /**
     * 租户后置处理切面
     *
     * @param joinPoint
     * @return
     */
    @Around("tenantAfterHandler()")
    public Object tenantAfterHandlerPoint(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        TenantAfterHandler tenantAfterHandler = methodSignature.getMethod().getAnnotation(TenantAfterHandler.class);
        Object proceed = null;
        try {
            //方法执行前
            proceed = joinPoint.proceed();
            //方式执行后
            doTenantAfterHandler(tenantAfterHandler, proceed);
        } catch (Throwable throwable) {
            if (throwable instanceof BusinessException) {
                BusinessException.throwBusinessException(((BusinessException) throwable).getMsgCd());
            } else {
                log.error("tenantAfterHandlerPoint exception {}", throwable.getMessage());
                BusinessException.throwBusinessException(MsgEnum.QUERY_WORKPLACE_APPLICATION_EXCEPTION);
            }
        }
        return proceed;
    }

    /**
     * 租户后置处理业务逻辑
     *
     * @param tenantAfterHandler
     * @param resultObject
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    private Object doTenantAfterHandler(TenantAfterHandler tenantAfterHandler,
            Object resultObject) throws NoSuchFieldException, IllegalAccessException {
        String filterField = tenantAfterHandler.filterField();
        if (resultObject instanceof DefaultRspDTO) {
            return getDefaultRspDto(resultObject, filterField);
        } else {
            return getResultObject(resultObject, filterField);
        }
    }
}
