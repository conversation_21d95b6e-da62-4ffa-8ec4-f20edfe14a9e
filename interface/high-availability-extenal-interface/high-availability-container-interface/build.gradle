dependencies {
    optional project(':common:high-availability-common')
    optional project(':interface:high-availability-base-interface')
    optional project(':interface:high-availability-system-interface')
    optional("com.cmpay:cmpay-interface")
    optional("org.springframework.cloud:spring-cloud-openfeign-core")
    optional("org.springframework:spring-core")
    optional("com.cmpay:lemon-framework-mybatis")
    optional("com.fasterxml.jackson.core:jackson-databind")
    optional("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
}
