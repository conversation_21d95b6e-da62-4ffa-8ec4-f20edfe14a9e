package com.cmpay.hacp.extend.container.autoconfigure;

import com.cmpay.hacp.extend.container.client.HacpContainerClient;
import com.cmpay.hacp.extend.container.k8s.KubesphereClient;
import com.cmpay.hacp.extend.container.service.HacpContainerService;
import com.cmpay.hacp.extend.container.service.HacpKubesphereService;
import com.cmpay.hacp.extend.container.service.impl.HacpContainerServiceClientImpl;
import com.cmpay.hacp.extend.container.service.impl.HacpKubesphereServiceClientImpl;
import com.cmpay.hacp.system.autoconfigure.HacpWebAdminAutoConfiguration;
import com.cmpay.hacp.system.service.SubSystemCipherService;
import com.cmpay.hacp.system.service.SystemCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "hacp.management.discovery", name = "enabled", havingValue = "true", matchIfMissing = true)
@AutoConfigureAfter({ HacpWebAdminAutoConfiguration.class, K8sAutoConfiguration.class })
public class HacpWebContainerClientAutoConfiguration {
    @Bean
    @ConditionalOnMissingBean(HacpContainerService.class)
    @ConditionalOnBean({HacpContainerClient.class,SubSystemCipherService.class})
    public HacpContainerService hacpContainerServiceClientImpl(HacpContainerClient hacpContainerClient, SubSystemCipherService systemCipherService) {
        log.info("init HacpContainerServiceClientImpl");
        return new HacpContainerServiceClientImpl(hacpContainerClient,systemCipherService);
    }

    @Bean
    @ConditionalOnMissingBean(HacpKubesphereService.class)
    @ConditionalOnBean({HacpContainerService.class,KubesphereClient.class})
    public HacpKubesphereService hacpKubesphereServiceClientImpl(HacpContainerService containerService,
            SystemCacheService systemCacheService,
            KubesphereClient kubesphereClient) {
        log.info("init HacpKubesphereServiceClientImpl");
        return new HacpKubesphereServiceClientImpl(containerService,systemCacheService,kubesphereClient);
    }
}
