package com.cmpay.hacp.extend.container.service;

import com.cmpay.hacp.extend.container.bo.ContainerConfigBO;
import com.cmpay.lemon.framework.page.PageInfo;

/**
 * <AUTHOR>
 * @create 2024/09/02 10:23
 * @since 1.0.0
 */

public interface HacpContainerService {

    void add(ContainerConfigBO bo);

    void update(ContainerConfigBO bo);

    void delete(ContainerConfigBO bo);

    ContainerConfigBO getDetailInfo(ContainerConfigBO bo);

    ContainerConfigBO getDecryptDetailInfo(ContainerConfigBO bo);

    PageInfo<ContainerConfigBO> getPage(int pageNum, int pageSize, ContainerConfigBO bo);
}
