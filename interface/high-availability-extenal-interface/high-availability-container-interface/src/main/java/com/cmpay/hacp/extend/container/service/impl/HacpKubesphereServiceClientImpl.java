package com.cmpay.hacp.extend.container.service.impl;

import com.cmpay.hacp.extend.container.bo.ContainerConfigBO;
import com.cmpay.hacp.extend.container.bo.KubesphereMetaData;
import com.cmpay.hacp.extend.container.k8s.KubesphereClient;
import com.cmpay.hacp.extend.container.service.HacpContainerService;
import com.cmpay.hacp.extend.container.service.adapter.HacpKubesphereServiceAdapter;
import com.cmpay.hacp.system.service.SystemCacheService;

import java.util.Collections;
import java.util.List;

public class HacpKubesphereServiceClientImpl extends HacpKubesphereServiceAdapter {

    public HacpKubesphereServiceClientImpl(HacpContainerService containerService,
            SystemCacheService systemCacheService,
            KubesphereClient kubesphereClient) {
        super(containerService, systemCacheService, kubesphereClient);
    }

    @Override
    public List<KubesphereMetaData> queryWorkspaces(ContainerConfigBO bo, String workspaceId) {
        return Collections.emptyList();
    }
}
