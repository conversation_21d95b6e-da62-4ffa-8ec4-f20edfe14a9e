package com.cmpay.hacp.extend.container.autoconfigure;

import com.cmpay.hacp.extend.container.properties.KsApiProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableFeignClients("com.cmpay.hacp.extend.container.k8s")
@EnableConfigurationProperties({KsApiProperties.class})
public class K8sAutoConfiguration {
}
