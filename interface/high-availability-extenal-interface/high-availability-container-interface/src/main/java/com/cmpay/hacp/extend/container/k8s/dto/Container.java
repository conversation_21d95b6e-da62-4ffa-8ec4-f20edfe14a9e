package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

import java.util.List;

/**
 * 容器
 */
@Data
public class Container {

    /**
     * 容器名称
     */
    private String name;

    /**
     * 镜像
     */
    private String image;

    /**
     * 端口列表
     */
    private List<ContainerPort> ports;

    /**
     * 环境变量
     */
    private List<EnvVar> env;

    /**
     * 资源配置
     */
    private ResourceRequirements resources;

    /**
     * 卷挂载
     */
    private List<VolumeMount> volumeMounts;

    /**
     * 存活探针
     */
    private Probe livenessProbe;

    /**
     * 就绪探针
     */
    private Probe readinessProbe;

    /**
     * 生命周期
     */
    private Lifecycle lifecycle;

    /**
     * 终止消息路径
     */
    private String terminationMessagePath;

    /**
     * 终止消息策略
     */
    private String terminationMessagePolicy;

    /**
     * 镜像拉取策略
     */
    private String imagePullPolicy;
}
