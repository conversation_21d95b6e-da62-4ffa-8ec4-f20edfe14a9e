package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Deployment状态
 */
@Data
public class DeploymentStatus {
    
    /**
     * 观察到的代数
     */
    private Integer observedGeneration;
    
    /**
     * 副本数
     */
    private Integer replicas;
    
    /**
     * 更新的副本数
     */
    private Integer updatedReplicas;
    
    /**
     * 就绪副本数
     */
    private Integer readyReplicas;
    
    /**
     * 可用副本数
     */
    private Integer availableReplicas;
    
    /**
     * 不可用副本数
     */
    private Integer unavailableReplicas;
    
    /**
     * 条件列表
     */
    private List<DeploymentCondition> conditions;
}

/**
 * Deployment条件
 */
@Data
class DeploymentCondition {
    /**
     * 条件类型
     */
    private String type;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 最后转换时间
     */
    private LocalDateTime lastTransitionTime;
    
    /**
     * 原因
     */
    private String reason;
    
    /**
     * 消息
     */
    private String message;
}
