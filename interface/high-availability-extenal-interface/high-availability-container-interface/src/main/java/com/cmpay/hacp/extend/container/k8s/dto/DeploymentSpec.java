package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

import java.util.Map;

/**
 * Deployment 规格
 */
@Data
public class DeploymentSpec {
    
    /**
     * 副本数
     */
    private Integer replicas;
    
    /**
     * 选择器
     */
    private Selector selector;
    
    
    /**
     * 部署策略
     */
    private DeploymentStrategy strategy;
    
    /**
     * 修订历史限制
     */
    private Integer revisionHistoryLimit;
    
    /**
     * 进度截止时间（秒）
     */
    private Integer progressDeadlineSeconds;
}

/**
 * 选择器
 */
@Data
class Selector {
    /**
     * 匹配标签
     */
    private Map<String, String> matchLabels;
}

/**
 * 部署策略
 */
@Data
class DeploymentStrategy {
    /**
     * 策略类型
     */
    private String type;
    
    /**
     * 滚动更新配置
     */
    private RollingUpdate rollingUpdate;
}

/**
 * 滚动更新
 */
@Data
class RollingUpdate {
    /**
     * 最大不可用
     */
    private String maxUnavailable;
    
    /**
     * 最大激增
     */
    private String maxSurge;
}
