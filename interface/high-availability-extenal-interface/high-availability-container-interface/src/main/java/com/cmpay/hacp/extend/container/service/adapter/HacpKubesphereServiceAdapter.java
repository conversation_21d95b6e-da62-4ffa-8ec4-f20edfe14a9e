package com.cmpay.hacp.extend.container.service.adapter;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.extend.container.bo.ContainerConfigBO;
import com.cmpay.hacp.extend.container.bo.HacpAggregateBO;
import com.cmpay.hacp.extend.container.bo.KubesphereMetaData;
import com.cmpay.hacp.extend.container.k8s.KubesphereClient;
import com.cmpay.hacp.extend.container.k8s.dto.*;
import com.cmpay.hacp.extend.container.service.HacpContainerService;
import com.cmpay.hacp.extend.container.service.HacpKubesphereService;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 云平台接口
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/14 15:21
 */
public abstract class HacpKubesphereServiceAdapter implements HacpKubesphereService {
    private static final Logger logger = LoggerFactory.getLogger(HacpKubesphereServiceAdapter.class);

    protected final HacpContainerService containerService;

    protected final SystemCacheService systemCacheService;

    protected final KubesphereClient kubesphereClient;

    public HacpKubesphereServiceAdapter(HacpContainerService containerService,
            SystemCacheService systemCacheService,
            KubesphereClient kubesphereClient) {
        this.containerService = containerService;
        this.systemCacheService = systemCacheService;
        this.kubesphereClient = kubesphereClient;
    }

    @Override
    public KubesphereTokenReqDTO buildTokenBody(ContainerConfigBO bo, String workspaceId) {
        KubesphereTokenReqDTO tokenBody = new KubesphereTokenReqDTO();
        ContainerConfigBO tenantContainerConfigBO = new ContainerConfigBO();
        tenantContainerConfigBO.setWorkspaceId(workspaceId);
        tenantContainerConfigBO.setClientId(bo.getClientId());
        tenantContainerConfigBO.setContainerId(bo.getContainerId());
        tenantContainerConfigBO.setClientSecret(bo.getClientSecret());
        tenantContainerConfigBO.setUsername(bo.getUsername());
        ContainerConfigBO detailInfo = containerService.getDecryptDetailInfo(tenantContainerConfigBO);
        if (CommonConstant.ENCRYPTED_DISPLAY.equals(bo.getPassword())&&JudgeUtils.isNull(detailInfo)){
            BusinessException.throwBusinessException(MsgEnum.KUBESPHERE_PASSWORD_IS_ERROR);
        }
        if (JudgeUtils.isNull(detailInfo)) {
            BusinessException.throwBusinessException(MsgEnum.KUBESPHERE_CONFIG_IS_NULL);
        }
        tokenBody.setClient_id(detailInfo.getClientId());
        tokenBody.setClient_secret(detailInfo.getClientSecret());
        tokenBody.setGrant_type(detailInfo.getGrantType());
        tokenBody.setUsername(detailInfo.getUsername());
        tokenBody.setPassword(detailInfo.getPassword());
        tokenBody.setWorkspaceId(detailInfo.getCloudWorkspaceId());
        tokenBody.setClusters(JsonUtil.strToObject(detailInfo.getCloudClusters(),new TypeReference<List<String>>(){}));
        return tokenBody;
    }

    @Override
    public List<KubesphereMetaData> queryClusters(String workspaceId) {
        //先获取token
        KubesphereTokenRspDTO token = getToken(workspaceId);
        if (token != null) {
            KubesphereClusterRspDTO clusters = kubesphereClient.getClusters_v1beta1(token.getTokenReqDTO().getWorkspaceId(), token.getAccessToken());
            if (JudgeUtils.isNull(clusters)) {
                return null;
            }
            return parseSingleWorkSpace(clusters, token.getTokenReqDTO().getClusters());
        }
        return null;
    }

    @Override
    public List<KubesphereMetaData> queryNameSpaces(String workspaceId, String cluster) {
        //先获取token
        KubesphereTokenRspDTO token = getToken(workspaceId);
        if (token != null) {
            KubesphereRspDTO body = kubesphereClient.getNs_v1beta1(cluster, token.getAccessToken());
            if (JudgeUtils.isNull(body)) {
                return null;
            }
            return parse(body);
        }
        return null;
    }

    @Override
    public KubesphereTokenRspDTO getToken(String workspaceId) {
        //String workspaceId = TenantUtils.getWorkspaceIdNotNull();
        KubesphereTokenReqDTO tokenParam = buildTokenBody(new ContainerConfigBO(), workspaceId);
        KubesphereTokenRspDTO token = getToken(tokenParam);
        token.setTokenReqDTO(tokenParam);
        return token;
    }

    @Override
    public KubesphereTokenRspDTO getToken(KubesphereTokenReqDTO tokenParam) {
        String key = tokenParam.getClient_id() + "-" + tokenParam.getUsername();
        if (systemCacheService.exists(key)) {
            return JsonUtil.strToObject(systemCacheService.getValue(key).toString(), KubesphereTokenRspDTO.class);
        }
        try {
            KubesphereTokenRspDTO token = kubesphereClient.getToken(tokenParam);
            token.setAccessToken("Bearer " + token.getAccessToken());
            if (JudgeUtils.isBlank(tokenParam.getWorkspaceId())) {
                return token;
            }
            token.setWorkspaceId(tokenParam.getWorkspaceId());
            systemCacheService.setValue(key, JsonUtil.objToStr(token), 6000, TimeUnit.SECONDS);
            return token;
        } catch (Exception e) {
            // 记录错误日志或抛出自定义异常
            logger.info("Failed to get token: " + e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.KUBESPHERE_PERMISSION_IS_ERROR);
        }
        return null;
    }

    @Override
    public Map<String, List<String>> queryPods(String workspaceId, String cluster, String namespace, String appName) {
        //先获取token
        KubesphereTokenRspDTO token = getToken(workspaceId);
        if (token != null) {
            String selector = Optional.ofNullable(appName)
                    .map(m -> "app.kubernetes.io/name=" + m)
                    .orElse(null);
            KubesphereRspDTO nsPods = kubesphereClient.getNsPods_v1(cluster, namespace, token.getAccessToken(),selector);
            return parseMap(nsPods);
        }
        return null;
    }


    @Override
    public Map<String, List<String>> queryDeployments(String workspaceId, String cluster, String namespace, String appName) {
        KubesphereTokenRspDTO token = this.getToken(workspaceId);
        if (token != null) {
            String selector = Optional.ofNullable(appName)
                    .map(m -> "app.kubernetes.io/name=" + m)
                    .orElse(null);
            KubesphereRspDTO kubesphereDeploymentRspDTO = kubesphereClient.getDeploys_v1(cluster, namespace, token.getAccessToken(),selector);
            return parseMap(kubesphereDeploymentRspDTO);
        }
        return null;
    }

    @Override
    public ResponseStatusRspDTO deletePod(String workspaceId, String cluster, String namespace, String pod) {
        //先获取token
        KubesphereTokenRspDTO token = getToken(workspaceId);
        ResponseStatusRspDTO responseStatusRspDTO = new ResponseStatusRspDTO();
        try {
            String response= kubesphereClient.deletePod_v1(cluster, namespace, pod, token.getAccessToken());
            responseStatusRspDTO.setStatus(200);
            responseStatusRspDTO.setMessage(response);
        } catch (FeignException e) {
            responseStatusRspDTO.setStatus(e.status());
            responseStatusRspDTO.setMessage(e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.POD_NOT_EXIST);
        }
        return responseStatusRspDTO;
    }

    @Override
    public List<KubesphereMetaData> parseMetaDataWorkSpace(KubesphereRspDTO body) {
        Integer totalItems = body.getTotalItems();
        if (totalItems < 1) {
            return null;
        }
        ArrayList<KubesphereMetaData> mataData = new ArrayList<>();
        body.getItems()
                .forEach(f -> {
                    KubesphereMetaData y = new KubesphereMetaData();
                    List<OwnerReference> ownerReferences = f.getMetadata().getOwnerReferences();
                    String kind = Optional.ofNullable(ownerReferences).orElse(new ArrayList<>()).stream()
                            .map(OwnerReference::getKind)
                            .distinct()
                            .filter(JudgeUtils::isNotEmpty)
                            .findFirst()
                            .orElse(null);
                    String name = f.getMetadata().getName();
                    y.setType(kind);
                    y.setName(name);
                    List<Cluster> clusters = f.getSpec().getPlacement().getClusters();
                    ArrayList<KubesphereMetaData> clusterList = new ArrayList<>();
                    clusters.forEach(c->{
                        KubesphereMetaData metaData = new KubesphereMetaData();
                        metaData.setName(c.getName());
                        metaData.setType("cluster");
                        clusterList.add(metaData);
                    });
                    y.setItems(clusterList);
                    mataData.add(y);
                });
        return mataData;
    }

    @Override
    public List<KubesphereMetaData> parseSingleWorkSpace(KubesphereClusterRspDTO body, List<String> limitCluster) {
        String name = body.getMetadata().getName();
        String kind = body.getKind();
        List<Cluster> clusters = body.getSpec().getPlacement().getClusters();
        ArrayList<KubesphereMetaData> mataData = new ArrayList<>();
        KubesphereMetaData workspace = new KubesphereMetaData();
        ArrayList<KubesphereMetaData> clusterList = new ArrayList<>();
        if(JudgeUtils.isEmpty(clusters)){
            logger.error("clusters is {}",clusters);
            return mataData;
        }
        clusters.stream().filter(f->limitCluster.contains(f.getName())).forEach(f->{
            KubesphereMetaData metaData = new KubesphereMetaData();
            metaData.setName(f.getName());
            metaData.setType("cluster");
            clusterList.add(metaData);
        });

        workspace.setType(kind);
        workspace.setName(name);
        workspace.setItems(clusterList);
        mataData.add(workspace);

        return mataData;
    }

    /**
     * 解析列表
     * key：metadata.name
     * value：app.kubernetes.io/name列表
     * @param body Pod响应DTO
     * @return Pod名称映射表
     */
    protected Map<String, List<String>> parseMap(KubesphereRspDTO body) {
        List<Item> item = body.getItems();
        HashMap<String, List<String>> hashMap = new HashMap<>();
        if (JudgeUtils.isEmpty(item)) {
            return hashMap;
        }
        item.forEach(f -> {
            KubesphereMetadata metadata = f.getMetadata();
            String name = metadata.getName();
            Map<String, String> labels = metadata.getLabels();
            if(JudgeUtils.isNull(labels)){
                return;
            }
            String key = labels.getOrDefault("app.kubernetes.io/name", null);
            if (JudgeUtils.isBlank(key)) {
                return;
            }
            if (hashMap.containsKey(key)) {
                List<String> pods = hashMap.get(key);
                pods.add(name);
            } else {
                ArrayList<String> pods = new ArrayList<>();
                pods.add(name);
                hashMap.put(key, pods);
            }
        });
        return hashMap;
    }

    @Override
    public List<KubesphereMetaData> parse(KubesphereRspDTO body) {
        ArrayList<KubesphereMetaData> mataData = new ArrayList<>();
        Optional.ofNullable(body.getItems())
                .orElse(new ArrayList<>())
                .forEach(f -> {
                    KubesphereMetaData metaData = new KubesphereMetaData();
                    metaData.setType("namespace");
                    metaData.setName(f.getMetadata().getName());
                    mataData.add(metaData);
                });
        return mataData;
    }

    @Override
    public List<HacpAggregateBO> queryAggregateResources(String workspaceId, String cluster, String namespace) {
        List<String> clusters = new ArrayList<>();
        List<HacpAggregateBO> result = new ArrayList<>();
        if(JudgeUtils.isEmpty(cluster)){
            queryCluster(workspaceId, clusters,cs->
                    queryNameSpace(workspaceId,cs,ns->
                            queryResources(workspaceId, cs, ns, result)));
        }else if(JudgeUtils.isBlank(namespace)){
            queryNameSpace(workspaceId,cluster,ns->
                    queryResources(workspaceId, cluster, ns, result));
        }else{
            queryResources(workspaceId, cluster, namespace, result);
        }
        return result;
    }

    private void queryNameSpace(String workspaceId, String cluster, Consumer<String> namespace) {
        List<KubesphereMetaData> aggregateBOList = queryNameSpaces(workspaceId, cluster);
        if(JudgeUtils.isNotEmpty(aggregateBOList)){
            aggregateBOList.forEach(g-> namespace.accept(g.getName()));
        }
    }

    private void queryCluster(String workspaceId, List<String> clusters, Consumer<String> cluster) {
        List<KubesphereMetaData> kubesphereMetaData = queryClusters(workspaceId);
        Optional.ofNullable(kubesphereMetaData).ifPresent(f->{
            f.forEach(g->{
                g.getItems().forEach(h->{
                    cluster.accept(h.getName());
                });
            });
        });
    }

    private void queryResources(String workspaceId, String cluster, String namespace, List<HacpAggregateBO> result) {
        queryResource(()->this.queryPods(workspaceId, cluster, namespace,null), result, cluster, namespace);
    }

    private void queryResource(Supplier<Map<String, List<String>>> resourceList, List<HacpAggregateBO> result, String cluster, String namespace) {
        Optional.ofNullable(resourceList.get()).ifPresent(m ->
                m.forEach((k, v) ->
                        result.add(HacpAggregateBO.builder()
                                .cluster(cluster)
                                .namespace(namespace)
                                .name(k)
                                .total(v.size())
                                .build())));
    }


}
