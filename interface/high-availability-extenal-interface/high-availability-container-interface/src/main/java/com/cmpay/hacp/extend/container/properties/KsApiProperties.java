package com.cmpay.hacp.extend.container.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
/**
 * @description:
 * <AUTHOR>
 * @date 2024/8/29 14:58
 * @version 1.0
 */
@Data
@ConfigurationProperties(prefix = "hacp.emergence.kubesphere")
public class KsApiProperties {
    private Properties properties = new Properties();

    @Data
    public static class Properties {
        private String clientId = "kubesphere";
        private String clientSecret = "";
        private String grantType = "password";
        private String username = "admin";
        private String password = "admin";
        private String getClustersUrl;
        private String getWorkspacesUrl;
        private String getTokenUrl;
        private String getNsUrl;
        private String getNsPodsUrl;
        private String deletePodUrl;
    }
}
