package com.cmpay.hacp.extend.container.client;

import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.extend.container.api.ContainerApi;
import com.cmpay.hacp.extend.container.client.dto.EmergencyContainerQueryReqDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform}", url = "${hacp.management.discovery.url:}", contextId = "hacpContainerClient")
public interface HacpContainerClient {

    @PostMapping(VersionApi.VERSION_V1+ContainerApi.CONTAINER+ContainerApi.SUB_INFO)
    DefaultRspDTO<String> getSubDetailInfo(@RequestBody EmergencyContainerQueryReqDTO bo);
}
