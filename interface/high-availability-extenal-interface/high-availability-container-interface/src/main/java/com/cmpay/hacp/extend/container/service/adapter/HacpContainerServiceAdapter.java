package com.cmpay.hacp.extend.container.service.adapter;

import com.cmpay.hacp.extend.container.bo.ContainerConfigBO;
import com.cmpay.hacp.extend.container.service.HacpContainerService;
import com.cmpay.lemon.framework.page.PageInfo;

public abstract class HacpContainerServiceAdapter implements HacpContainerService {
    @Override
    public void add(ContainerConfigBO bo) {

    }

    @Override
    public void update(ContainerConfigBO bo) {

    }

    @Override
    public void delete(ContainerConfigBO bo) {

    }

    @Override
    public ContainerConfigBO getDetailInfo(ContainerConfigBO bo) {
        return null;
    }

    @Override
    public ContainerConfigBO getDecryptDetailInfo(ContainerConfigBO bo) {
        return null;
    }

    @Override
    public PageInfo<ContainerConfigBO> getPage(int pageNum, int pageSize, ContainerConfigBO bo) {
        return null;
    }
}
