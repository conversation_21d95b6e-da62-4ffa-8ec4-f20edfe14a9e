package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class KubesphereMetadata {
    private String name;
    private String selfLink;
    private String uid;
    private String resourceVersion;
    private Integer generation;
    private Date creationTimestamp;
    private Map<String, String> labels;
    private Map<String, String> annotations;
    private List<OwnerReference> ownerReferences;
    private List<String> finalizers;
    private List<ManagedFieldDTO> managedFields;
}