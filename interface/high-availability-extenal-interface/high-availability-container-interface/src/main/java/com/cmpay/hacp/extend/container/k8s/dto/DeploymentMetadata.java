package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Deployment 元数据
 */
@Data
public class DeploymentMetadata {
    
    /**
     * 资源名称
     */
    private String name;
    
    /**
     * 命名空间
     */
    private String namespace;
    
    /**
     * 创建时间
     */
    private LocalDateTime creationTimestamp;
    
    /**
     * 标签
     */
    private Map<String, String> labels;
    
    /**
     * 注解
     */
    private Map<String, String> annotations;
    
    /**
     * 资源版本
     */
    private String resourceVersion;
    
    /**
     * UID
     */
    private String uid;
    
    /**
     * 代数
     */
    private Integer generation;
}
