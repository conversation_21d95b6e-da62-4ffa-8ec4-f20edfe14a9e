package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

/**
 * 生命周期
 */
@Data
public class Lifecycle {
    
    /**
     * 启动后钩子
     */
    private Handler postStart;
    
    /**
     * 停止前钩子
     */
    private Handler preStop;
}

/**
 * 处理器
 */
@Data
class Handler {
    /**
     * 执行动作
     */
    private ExecAction exec;
    
    /**
     * HTTP GET动作
     */
    private HTTPGetAction httpGet;
    
    /**
     * TCP Socket动作
     */
    private TCPSocketAction tcpSocket;
}
