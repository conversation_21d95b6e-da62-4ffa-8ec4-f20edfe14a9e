package com.cmpay.hacp.extend.container.k8s.enums;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@Getter
@Slf4j
public enum KindEnum {

    SERVICE(0,"Service"),
    POD(1,"Pod"),
    DEPLOYMENT(2,"Deployment");

    @JsonValue
    private final Integer code;

    private final String desc;


    private static final Map<Integer, KindEnum> ENUM_MAP = Arrays.stream(KindEnum.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static KindEnum getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
