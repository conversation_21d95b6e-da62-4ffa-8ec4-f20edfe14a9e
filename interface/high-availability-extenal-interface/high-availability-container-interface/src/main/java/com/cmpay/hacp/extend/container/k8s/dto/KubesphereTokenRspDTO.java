package com.cmpay.hacp.extend.container.k8s.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/10/12 14:12
 * @since 1.0.0
 */
@Data
public class KubesphereTokenRspDTO {
    private String workspaceId;
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("token_type")
    private String tokenType;

    @JsonProperty("refresh_token")
    private String refreshToken;

    @JsonProperty("expires_in")
    private String expiresIn;

    private KubesphereTokenReqDTO tokenReqDTO;
}
