package com.cmpay.hacp.extend.container.service;

import com.cmpay.hacp.extend.container.bo.ContainerConfigBO;
import com.cmpay.hacp.extend.container.bo.HacpAggregateBO;
import com.cmpay.hacp.extend.container.bo.KubesphereMetaData;
import com.cmpay.hacp.extend.container.k8s.dto.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

public interface HacpKubesphereService {


    /**
     * 构建Token请求体
     * @param bo 应急容器BO
     * @param workspaceId 工作空间ID
     * @return Token请求DTO
     */
    KubesphereTokenReqDTO buildTokenBody(ContainerConfigBO bo, String workspaceId);

    /**
     * 查询集群列表
     * @param workspaceId 工作空间ID
     * @return 集群元数据列表
     */
    List<KubesphereMetaData> queryClusters(String workspaceId);

    /**
     * 查询命名空间列表
     * @param workspaceId 工作空间ID
     * @param cluster 集群名称
     * @return 命名空间元数据列表
     */
    List<KubesphereMetaData> queryNameSpaces(String workspaceId, String cluster);

    /**
     * 获取Token
     * @param workspaceId 工作空间ID
     * @return Token响应DTO
     */
    KubesphereTokenRspDTO getToken(String workspaceId);

    /**
     * 获取Token
     * @param tokenParam Token请求DTO
     * @return Token响应DTO
     */
    KubesphereTokenRspDTO getToken(KubesphereTokenReqDTO tokenParam);

    /**
     * 查询Pod列表
     * @param workspaceId 工作空间ID
     * @param cluster 集群名称
     * @param namespace 命名空间名称
     * @return Pod名称映射表
     */
    Map<String, List<String>> queryPods(String workspaceId, String cluster, String namespace, String appName);

    Map<String, List<String>> queryDeployments(String workspaceId, String cluster, String namespace, String appName);

    /**
     * 删除Pod
     * @param workspaceId 工作空间ID
     * @param cluster 集群名称
     * @param namespace 命名空间名称
     * @param pod Pod名称
     * @return 响应状态DTO
     */
    ResponseStatusRspDTO deletePod(String workspaceId, String cluster, String namespace, String pod);

    /**
     * 解析工作空间元数据
     * @param body 响应DTO
     * @return 元数据列表
     */
    List<KubesphereMetaData> parseMetaDataWorkSpace(KubesphereRspDTO body);

    /**
     * 解析单个工作空间
     * @param body 集群响应DTO
     * @param limitCluster 限制的集群列表
     * @return 元数据列表
     */
    List<KubesphereMetaData> parseSingleWorkSpace(KubesphereClusterRspDTO body, List<String> limitCluster);

    /**
     * 解析响应数据
     * @param body 响应DTO
     * @return 元数据列表
     */
    List<KubesphereMetaData> parse(KubesphereRspDTO body);

    List<KubesphereMetaData> queryWorkspaces(ContainerConfigBO bo, String workspaceId);

    List<HacpAggregateBO> queryAggregateResources(@NotNull String workspaceId, @NotNull String cluster, @NotNull String namespace);


}
